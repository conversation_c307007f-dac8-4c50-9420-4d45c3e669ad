# Redis??????
# ??????????????

# ========== ?????? ==========
# Redis?????????0?
spring.redis.database=2

# Redis?????
spring.redis.host=localhost
# Redis???????
spring.redis.port=6379
# Redis?????????????
spring.redis.password=Das2023redis.com

# ========== ?????? ==========
# ??????????- ?????3000ms
spring.redis.timeout=3000
# ????????????
spring.redis.command-timeout=5000

# ========== Lettuce????? ==========
# ????????????????????- ????
spring.redis.lettuce.pool.max-active=20
# ???????????????????????- ????
spring.redis.lettuce.pool.max-wait=10000
# ???????????
spring.redis.lettuce.pool.max-idle=10
# ???????????
spring.redis.lettuce.pool.min-idle=2

# ========== Lettuce???? ==========
# ??????
spring.redis.lettuce.shutdown-timeout=100ms
# ?????
spring.redis.lettuce.pool.time-between-eviction-runs=30s
spring.redis.lettuce.pool.min-evictable-idle-time=60s
spring.redis.lettuce.pool.num-tests-per-eviction-run=3

# ========== SSL???????? ==========
# spring.redis.ssl=true

# ========== ???????????? ==========
# spring.redis.cluster.nodes=127.0.0.1:7000,127.0.0.1:7001,127.0.0.1:7002
# spring.redis.cluster.max-redirects=3

# ========== ???????????? ==========
# spring.redis.sentinel.master=mymaster
# spring.redis.sentinel.nodes=127.0.0.1:26379,127.0.0.1:26380,127.0.0.1:26381

# ========== Sa-Token Redis???? ==========
# Sa-Token??Redis??
sa-token.alone-redis.database=2
sa-token.alone-redis.host=localhost
sa-token.alone-redis.port=6379
sa-token.alone-redis.password=Das2023redis.com
# ??Sa-Token Redis????
sa-token.alone-redis.timeout=5s
# Sa-Token Redis?????
sa-token.alone-redis.lettuce.pool.max-active=15
sa-token.alone-redis.lettuce.pool.max-wait=8000
sa-token.alone-redis.lettuce.pool.max-idle=8
sa-token.alone-redis.lettuce.pool.min-idle=1

# ========== ?????? ==========
# ??Redis????
management.health.redis.enabled=true
# Redis??????
app.redis.retry.max-attempts=3
# Redis??????????
app.redis.retry.delay=1000

# ========== ???? ==========
# Redis??????
logging.level.org.springframework.data.redis=INFO
logging.level.io.lettuce.core=INFO
logging.level.com.das.province.infr.config.RedisConfig=INFO
logging.level.com.das.province.infr.config.RedisHealthChecker=INFO

# ========== JVM???? ==========
# ?????????????
# -Dspring.redis.lettuce.pool.validation-query-timeout=3000
# -Dspring.redis.lettuce.pool.test-on-borrow=true
# -Dspring.redis.lettuce.pool.test-while-idle=true

# ========== ???????? ==========
# ????????
# spring.redis.timeout=5000
# spring.redis.lettuce.pool.max-active=50
# spring.redis.lettuce.pool.max-wait=15000
# spring.redis.lettuce.pool.max-idle=20
# spring.redis.lettuce.pool.min-idle=5

# ========== ???? ==========
# ??Redis????
management.metrics.export.redis.enabled=true
# Redis?????
management.endpoint.metrics.enabled=true
management.endpoints.web.exposure.include=health,metrics,info

# ========== ???? ==========
# Spring Cache??
spring.cache.type=redis
spring.cache.redis.time-to-live=3600s
spring.cache.redis.cache-null-values=false
spring.cache.redis.use-key-prefix=true
spring.cache.redis.key-prefix=wisdom:

# ========== ??????? ==========
# ????Jackson?????FastJSON
# ?RedisConfig??????Jackson????

# ========== ?????? ==========
# ??Redis????????????????
# spring.redis.timeout=10000
# spring.redis.lettuce.pool.max-wait=20000
# ????????????

# ========== ?????? ==========
# Redis??????????
# maxmemory 2gb
# maxmemory-policy allkeys-lru
# save 900 1
# save 300 10
# save 60 10000

# ========== ?????? ==========
# 1. ?????
# 2. ????IP
# 3. ???????rename-command FLUSHDB ""
# 4. ??SSL????????
# 5. ????Redis??
