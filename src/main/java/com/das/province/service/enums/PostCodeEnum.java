package com.das.province.service.enums;

import com.das.province.common.exception.CommonErrorCodeEnum;
import com.das.province.common.exception.CommonException;

/**
 * <AUTHOR>
 * @date 2022/09/25
 */
public enum PostCodeEnum {

    PREN("PREN", "编制"),
    CONT("CONT", "合同"),
    ;

    private String code;
    private String desc;

    PostCodeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static PostCodeEnum fromCode(String code) {
        for (PostCodeEnum iEnum : PostCodeEnum.values()) {
            if (iEnum.getCode().equals(code)) {
                return iEnum;
            }
        }
        throw new CommonException(CommonErrorCodeEnum.ENUM_NOT_EXIST);
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
