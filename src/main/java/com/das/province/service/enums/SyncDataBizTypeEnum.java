package com.das.province.service.enums;

import com.das.province.common.exception.CommonErrorCodeEnum;
import com.das.province.common.exception.CommonException;

public enum SyncDataBizTypeEnum {

    UZ("UZ", "用户"),
    CP("CP", "藏品"),
    OD("OD", "票务"),
    HISTORY_OD("HISTORY_OD", "历史票务同步"),
    CL("CL", "全息"),
    CL_INFO("CL_INFO", "全息信息"),
    CP_DIGITAL("CP_DIGITAL", "藏品数字化"),
    VE("VE", "场馆信息"),
    DE("DE", "设备信息"),
    DER("DER", "设备记录信息"),
    ;

    private String code;
    private String desc;

    SyncDataBizTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static SyncDataBizTypeEnum fromCode(Integer code) {
        for (SyncDataBizTypeEnum iEnum : SyncDataBizTypeEnum.values()) {
            if (iEnum.getCode().equals(code)) {
                return iEnum;
            }
        }
        throw new CommonException(CommonErrorCodeEnum.ENUM_NOT_EXIST);
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
