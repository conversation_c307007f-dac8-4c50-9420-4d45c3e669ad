package com.das.province.service.enums;

import com.das.province.common.exception.CommonErrorCodeEnum;
import com.das.province.common.exception.CommonException;

public enum ClassificationBizTypeEnum {

    DIC(1, "字典管理"),
    WHS(2, "库房管理"),
    DPTMT(3, "组织部门管理"),
    ;

    private Integer code;
    private String desc;

    ClassificationBizTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static ClassificationBizTypeEnum fromCode(Integer code) {
        for (ClassificationBizTypeEnum iEnum : ClassificationBizTypeEnum.values()) {
            if (iEnum.getCode().equals(code)) {
                return iEnum;
            }
        }
        throw new CommonException(CommonErrorCodeEnum.ENUM_NOT_EXIST);
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
