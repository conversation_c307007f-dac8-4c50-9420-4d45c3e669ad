package com.das.province.service.enums;

import com.das.province.common.exception.CommonErrorCodeEnum;
import com.das.province.common.exception.CommonException;

public enum PermissionTypeEnum {

    MENU("MENU", "菜单权限"),
    FCN("FCN", "功能权限"),
    DATA("DATA", "数据权限"),
    ;

    private String code;
    private String desc;

    PermissionTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static PermissionTypeEnum fromCode(String code) {
        for (PermissionTypeEnum iEnum : PermissionTypeEnum.values()) {
            if (iEnum.getCode().equals(code)) {
                return iEnum;
            }
        }
        throw new CommonException(CommonErrorCodeEnum.ENUM_NOT_EXIST);
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
