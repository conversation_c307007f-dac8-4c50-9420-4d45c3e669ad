package com.das.province.service.enums;

import com.das.province.common.exception.CommonErrorCodeEnum;
import com.das.province.common.exception.CommonException;

public enum LoginClientTypeEnum {

    PROVINCEPC("PROVINCEPC", "PROVINCEPC"),
    APP("APP", "移动"),
    APPLETS("APPLETS", "小程序"),
    ;

    private String code;
    private String desc;

    LoginClientTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static LoginClientTypeEnum fromCode(String code) {
        for (LoginClientTypeEnum iEnum : LoginClientTypeEnum.values()) {
            if (iEnum.getCode().equals(code)) {
                return iEnum;
            }
        }
        throw new CommonException(CommonErrorCodeEnum.ENUM_NOT_EXIST);
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
