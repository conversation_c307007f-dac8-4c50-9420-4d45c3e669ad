package com.das.province.service.enums;

import com.das.province.common.exception.CommonErrorCodeEnum;
import com.das.province.common.exception.CommonException;

/**
 * <AUTHOR>
 * @date 2022/09/25
 */
public enum PermissionLevelEnum {

    一级(1, "一级"),
    二级(2, "二级"),
    三级(3, "三级"),
    四级(4, "四级"),
    五级(5, "五级"),
    六级(6, "六级"),
    七级(7, "七级"),
    八级(8, "八级"),
    九级(9, "九级"),
    十级(10, "十级"),
    ;

    private Integer code;
    private String desc;

    PermissionLevelEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static PermissionLevelEnum fromCode(Integer code) {
        for (PermissionLevelEnum iEnum : PermissionLevelEnum.values()) {
            if (iEnum.getCode().equals(code)) {
                return iEnum;
            }
        }
        throw new CommonException(CommonErrorCodeEnum.ENUM_NOT_EXIST);
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
