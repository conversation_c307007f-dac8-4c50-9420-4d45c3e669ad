package com.das.province.service.config;

import com.das.province.ProvinceDigitalMuseumApplication;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.ScheduledExecutorService;

@SuppressWarnings({"checkstyle:indentation", "PMD.ThreadPoolCreationRule"})
public class GlobalExecutor {

    private static final ScheduledExecutorService SYNC_DATA_NOTIFY_EXECUTOR = ExecutorFactory.Managed
            .newSingleScheduledExecutorService(ClassUtils.getCanonicalName(ProvinceDigitalMuseumApplication.class),
                    new NameThreadFactory("com.das.province.service.biz.data.ProvinceSyncDataServiceImpl.SyncDataNotifier"));
    
    private static final ExecutorService TCP_CHECK_EXECUTOR = ExecutorFactory.Managed
            .newFixedExecutorService(ClassUtils.getCanonicalName(ProvinceDigitalMuseumApplication.class), 4,
                    new NameThreadFactory("com.das.province.service.biz.data.ProvinceSyncDataServiceImpl.SyncDataNotifier.worker"));
    
    private static final ExecutorService PUSH_CALLBACK_EXECUTOR = ExecutorFactory.Managed
            .newSingleExecutorService("Push", new NameThreadFactory("com.das.province.service.biz.data.ProvinceSyncDataServiceImpl.SyncDataNotifier.push.callback"));
    
    public static void submitNotifyTask(Runnable runnable) {
        SYNC_DATA_NOTIFY_EXECUTOR.submit(runnable);
    }

    public static void submitTcpCheck(Runnable runnable) {
        TCP_CHECK_EXECUTOR.submit(runnable);
    }

    public static ExecutorService getCallbackExecutor() {
        return PUSH_CALLBACK_EXECUTOR;
    }
}
