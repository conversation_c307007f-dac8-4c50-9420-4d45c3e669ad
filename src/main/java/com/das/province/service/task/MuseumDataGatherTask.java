package com.das.province.service.task;

import com.das.province.common.bo.HolidayBO;
import com.das.province.common.utils.DateUtils;
import com.das.province.infr.dataobject.*;
import com.das.province.infr.dataobjectexpand.*;
import com.das.province.infr.mapper.*;
import com.das.province.infr.mapper.SyncCulturalRelicsMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.env.Environment;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Component
@Slf4j
@EnableScheduling
public class MuseumDataGatherTask {

    @Resource
    private SyncCommCollectionRegisterInfoMapper syncCommCollectionRegisterInfoMapper;

    @Resource
    private SyncUserInfoMapper syncUserInfoMapper;

    @Resource
    private SyncOrderBaseMapper syncOrderBaseMapper;

    @Resource
    private SyncCulturalRelicsMapper syncCulturalRelicsMapper;

    @Resource
    private SyncCulturalRelicsInfoMapper syncCulturalRelicsInfoMapper;

    @Resource
    private SyncCollectionDigitalMapper syncCollectionDigitalMapper;

    @Resource
    private StatisticMuseumClassifyMapper statisticMuseumClassifyMapper;

    @Resource
    private StatisticMuseumGeneralMapper statisticMuseumGeneralMapper;

    @Resource
    private MuseumBaseInfoMapper museumBaseInfoMapper;

    @Resource
    private DataChangeInfoMapper dataChangeInfoMapper;

    @Resource
    private DataChangeDetailInfoMapper dataChangeDetailInfoMapper;

    @Resource
    private StatisticVisitorDayMapper statisticVisitorDayMapper;

    @Resource
    private StatisticVisitorMonthMapper statisticVisitorMonthMapper;

    @Resource
    private StatisticVisitorYearMapper statisticVisitorYearMapper;

    @Resource
    private StatisticCollectionMapper statisticCollectionMapper;

    @Resource
    private CommClassificationMapper commClassificationMapper;

    @Resource
    private AccCompanyMapper accCompanyMapper;

    @Resource
    private DataChangeDetailTemporaryMapper dataChangeDetailTemporaryMapper;

    @Resource
    private RedissonClient redissonClient;

    @Resource
    private Environment environment;

    @Value("${system.location.adCode}")
    private String locationAdCode;

    @Resource
    private GetTicketPersonTask getTicketPersonTask;

    @Resource
    private GetTicketPersonTaskNew getTicketPersonTaskNew;

    private final ConcurrentHashMap<Integer, List<HolidayBO>> holidaysMap = new ConcurrentHashMap<>(16);


    public void holidayInit(){
        // 2022
        List<HolidayBO> l2022 = com.google.common.collect.Lists.newArrayList();
        HolidayBO newYear2022 = new HolidayBO();
        newYear2022.setName("元旦");
        newYear2022.setStartTime(DateUtils.parseDateYYYMMDDHHmmss(environment.getProperty("2022.holiday.newYearStart")));
        newYear2022.setEndTime(DateUtils.parseDateYYYMMDDHHmmss(environment.getProperty("2022.holiday.newYearEnd")));
        l2022.add(newYear2022);

        HolidayBO chineseNewYear2022 = new HolidayBO();
        chineseNewYear2022.setName("春节");
        chineseNewYear2022.setStartTime(DateUtils.parseDateYYYMMDDHHmmss(environment.getProperty("2022.holiday.chineseNewYearStart")));
        chineseNewYear2022.setEndTime(DateUtils.parseDateYYYMMDDHHmmss(environment.getProperty("2022.holiday.chineseNewYearEnd")));
        l2022.add(chineseNewYear2022);

        HolidayBO chingMing2022 = new HolidayBO();
        chingMing2022.setName("清明节");
        chingMing2022.setStartTime(DateUtils.parseDateYYYMMDDHHmmss(environment.getProperty("2022.holiday.chingMingStart")));
        chingMing2022.setEndTime(DateUtils.parseDateYYYMMDDHHmmss(environment.getProperty("2022.holiday.chingMingEnd")));
        l2022.add(chingMing2022);

        HolidayBO labor2022 = new HolidayBO();
        labor2022.setName("劳动节");
        labor2022.setStartTime(DateUtils.parseDateYYYMMDDHHmmss(environment.getProperty("2022.holiday.laborStart")));
        labor2022.setEndTime(DateUtils.parseDateYYYMMDDHHmmss(environment.getProperty("2022.holiday.laborEnd")));
        l2022.add(labor2022);

        HolidayBO dragonBoat2022 = new HolidayBO();
        dragonBoat2022.setName("劳动节");
        dragonBoat2022.setStartTime(DateUtils.parseDateYYYMMDDHHmmss(environment.getProperty("2022.holiday.dragonBoatStart")));
        dragonBoat2022.setEndTime(DateUtils.parseDateYYYMMDDHHmmss(environment.getProperty("2022.holiday.dragonBoatEnd")));
        l2022.add(dragonBoat2022);

        HolidayBO midAutumn2022 = new HolidayBO();
        midAutumn2022.setName("中秋节");
        midAutumn2022.setStartTime(DateUtils.parseDateYYYMMDDHHmmss(environment.getProperty("2022.holiday.midAutumnStart")));
        midAutumn2022.setEndTime(DateUtils.parseDateYYYMMDDHHmmss(environment.getProperty("2022.holiday.midAutumnEnd")));
        l2022.add(midAutumn2022);

        HolidayBO national2022 = new HolidayBO();
        national2022.setName("国庆节");
        national2022.setStartTime(DateUtils.parseDateYYYMMDDHHmmss(environment.getProperty("2022.holiday.nationalStart")));
        national2022.setEndTime(DateUtils.parseDateYYYMMDDHHmmss(environment.getProperty("2022.holiday.nationalEnd")));
        l2022.add(national2022);
        holidaysMap.put(2022, l2022);
        // 2023
        List<HolidayBO> l2023 = com.google.common.collect.Lists.newArrayList();
        HolidayBO newYear2023 = new HolidayBO();
        newYear2023.setName("元旦");
        newYear2023.setStartTime(DateUtils.parseDateYYYMMDDHHmmss(environment.getProperty("2023.holiday.newYearStart")));
        newYear2023.setEndTime(DateUtils.parseDateYYYMMDDHHmmss(environment.getProperty("2023.holiday.newYearEnd")));
        l2023.add(newYear2023);

        HolidayBO chineseNewYear2023 = new HolidayBO();
        chineseNewYear2023.setName("春节");
        chineseNewYear2023.setStartTime(DateUtils.parseDateYYYMMDDHHmmss(environment.getProperty("2023.holiday.chineseNewYearStart")));
        chineseNewYear2023.setEndTime(DateUtils.parseDateYYYMMDDHHmmss(environment.getProperty("2023.holiday.chineseNewYearEnd")));
        l2023.add(chineseNewYear2023);

        HolidayBO chingMing2023 = new HolidayBO();
        chingMing2023.setName("清明节");
        chingMing2023.setStartTime(DateUtils.parseDateYYYMMDDHHmmss(environment.getProperty("2023.holiday.chingMingStart")));
        chingMing2023.setEndTime(DateUtils.parseDateYYYMMDDHHmmss(environment.getProperty("2023.holiday.chingMingEnd")));
        l2023.add(chingMing2023);

        HolidayBO labor2023 = new HolidayBO();
        labor2023.setName("劳动节");
        labor2023.setStartTime(DateUtils.parseDateYYYMMDDHHmmss(environment.getProperty("2023.holiday.laborStart")));
        labor2023.setEndTime(DateUtils.parseDateYYYMMDDHHmmss(environment.getProperty("2023.holiday.laborEnd")));
        l2023.add(labor2023);

        HolidayBO dragonBoat2023 = new HolidayBO();
        dragonBoat2023.setName("端午节");
        dragonBoat2023.setStartTime(DateUtils.parseDateYYYMMDDHHmmss(environment.getProperty("2023.holiday.dragonBoatStart")));
        dragonBoat2023.setEndTime(DateUtils.parseDateYYYMMDDHHmmss(environment.getProperty("2023.holiday.dragonBoatEnd")));
        l2023.add(dragonBoat2023);

        HolidayBO nationalPlus2023 = new HolidayBO();
        nationalPlus2023.setName("中秋节/国庆节");
        nationalPlus2023.setStartTime(DateUtils.parseDateYYYMMDDHHmmss(environment.getProperty("2023.holiday.nationalPlusStart")));
        nationalPlus2023.setEndTime(DateUtils.parseDateYYYMMDDHHmmss(environment.getProperty("2023.holiday.nationalPlusEnd")));
        l2023.add(nationalPlus2023);
        holidaysMap.put(2023, l2023);
    }


    /**
     * 定时计算各博物馆藏品数据（每天凌晨3点）
     */
    //@Scheduled(cron = "0 05 3 * * ?")
    public void calculateMuseumCollection() {
        log.info("calculateMuseumCollection start.......");
        RLock rLock = redissonClient.getLock("calculateMuseumCollection");
        try {
            boolean getLock = rLock.tryLock(2, 10,TimeUnit.SECONDS);
            if(getLock) {
                calculateMuseumCollectionService();
                // 休眠3秒，保证只有一个服务可以加锁成功
                Thread.sleep(3000);
            }
        } catch (Exception e) {
            log.error("calculateMuseumCollection Exception:", e);
        } finally {
            if(rLock.isLocked() && rLock.isHeldByCurrentThread()){
                rLock.unlock();
            }
        }
        log.info("calculateMuseumCollection end.......");
    }


    /**
     * 定时计算藏品异动(每日凌晨计算昨日数据)
     * 每天凌晨3点
     */
    //@Scheduled(cron = "0 25 3 * * ?")
    public void calculateCollectionChange() {
        log.info("calculateCollectionChange start.......");
        RLock rLock = redissonClient.getLock("calculateCollectionChange");
        try {
            boolean getLock = rLock.tryLock(2, 10,TimeUnit.SECONDS);
            if(getLock) {
                calculateCollectionChangeService();
                // 休眠3秒，保证只有一个服务可以加锁成功
                Thread.sleep(3000);
            }
        } catch (Exception e) {
            log.error("calculateCollectionChange Exception:", e);
        } finally {
            if(rLock.isLocked() && rLock.isHeldByCurrentThread()){
                rLock.unlock();
            }
        }
        log.info("calculateCollectionChange end.......");
    }

    /**
     * 定时计算博物馆概况（每天凌晨1点）
     */
    //@Scheduled(cron = "0 01 1 * * ?")
    public void calculateMuseumGeneral() {
        log.info("calculateMuseumGeneral start.......");
        RLock rLock = redissonClient.getLock("calculateMuseumGeneral");
        try {
            boolean getLock = rLock.tryLock(2, 10,TimeUnit.SECONDS);
            if(getLock) {
                calculateMuseumGeneralService();
                // 休眠3秒，保证只有一个服务可以加锁成功
                Thread.sleep(3000);
            }
        } catch (Exception e) {
            log.error("calculateMuseumGeneral Exception:", e);
        } finally {
            if(rLock.isLocked() && rLock.isHeldByCurrentThread()){
                rLock.unlock();
            }
        }
        log.info("calculateMuseumGeneral end.......");
    }

    /**
     * 定时计算博物馆分类（每天凌晨1点）
     */
    //@Scheduled(cron = "0 21 1 * * ?")
    public void calculateMuseumClassify() {
        log.info("calculateMuseumClassify start.......");
        RLock rLock = redissonClient.getLock("calculateMuseumClassify");
        try {
            boolean getLock = rLock.tryLock(2, 10,TimeUnit.SECONDS);
            if(getLock) {
                calculateMuseumClassifyService();
                // 休眠3秒，保证只有一个服务可以加锁成功
                Thread.sleep(3000);
            }
        } catch (Exception e) {
            log.error("calculateMuseumClassify Exception:", e);
        } finally {
            if(rLock.isLocked() && rLock.isHeldByCurrentThread()){
                rLock.unlock();
            }
        }
        log.info("calculateMuseumClassify end.......");
    }

    /**
     * 定时计算各博物馆观众(日表只更新今日)
     * 每5分钟执行一次
     */
    //@Scheduled(cron = "0 0/5 * * * ?")
    public void calculateMuseumVisitor() {
        log.info("calculateMuseumVisitor start.......");
        RLock rLock = redissonClient.getLock("calculateMuseumVisitor");
        try {
            boolean getLock = rLock.tryLock(2, 10,TimeUnit.SECONDS);
            if(getLock) {
                Long endTime = new Date().getTime()/1000;
                // 今日0点
                Long startTime = DateUtils.getTimesDayMorning().getTime()/1000;
                try {
                    // 更新伪满、长影实时数据
                    getTicketPersonTaskNew.setWeiManTicketRealData(startTime, endTime);
                }catch (Exception e){
                    log.error("获取伪满、长影实时数据失败！:", e);
                }
                try {
                    // 更新东北沦陷实时数据
                    getTicketPersonTaskNew.setLunXianTicketRealData(startTime, endTime);
                }catch (Exception e){
                    log.error("获取东北沦陷实时数据失败！:", e);
                }
                calculateMuseumVisitorDayService(DateUtils.formatDateYYYMMDDHHmmss(DateUtils.getTimesDayMorning()),DateUtils.formatDateYYYMMDDHHmmss(DateUtils.getTimesDayNight()));
                calculateMuseumVisitorMonthService();
                calculateMuseumVisitorYearService();
                // 休眠3秒，保证只有一个服务可以加锁成功
                Thread.sleep(3000);
            }
        } catch (Exception e) {
            log.error("calculateMuseumVisitor Exception:", e);
        } finally {
            if(rLock.isLocked() && rLock.isHeldByCurrentThread()){
                rLock.unlock();
            }
        }
        log.info("calculateMuseumVisitor end.......");
    }

    /**
     * 定时计算各博物馆观众(包含以往历史数据)
     * 每晚4点执行
     */
    //@Scheduled(cron = "0 01 04 * * ?")
    public void calculateMuseumVisitorAll() {
        log.info("calculateMuseumVisitorAll start.......");
        RLock rLock = redissonClient.getLock("calculateMuseumVisitorAll");
        try {
            boolean getLock = rLock.tryLock(2, 10,TimeUnit.SECONDS);
            if(getLock) {
                calculateMuseumVisitorDayService("2024-01-01 00:00:00",DateUtils.formatDateYYYMMDDHHmmss(DateUtils.getTimesDayNight()));
                calculateMuseumVisitorMonthService();
                calculateMuseumVisitorYearService();
                // 休眠3秒，保证只有一个服务可以加锁成功
                Thread.sleep(3000);
            }
        } catch (Exception e) {
            log.error("calculateMuseumVisitor Exception:", e);
        } finally {
            if(rLock.isLocked() && rLock.isHeldByCurrentThread()){
                rLock.unlock();
            }
        }
        log.info("calculateMuseumVisitor end.......");
    }


    /**
     * 定时监测观众异动(每月最后一天23点计算)
     */
    //@Scheduled(cron = "0 01 23 L * ?")
    public void calculateMonthVisitorChange() {
        log.info("calculateMonthVisitorChange start.......");
        RLock rLock = redissonClient.getLock("calculateMonthVisitorChange");
        try {
            boolean getLock = rLock.tryLock(2, 10,TimeUnit.SECONDS);
            if(getLock) {
                calculateMonthVisitorChangeService();
                // 休眠3秒，保证只有一个服务可以加锁成功
                Thread.sleep(3000);
            }
        } catch (Exception e) {
            log.error("calculateMonthVisitorChange Exception:", e);
        } finally {
            if(rLock.isLocked() && rLock.isHeldByCurrentThread()){
                rLock.unlock();
            }
        }
        log.info("calculateMonthVisitorChange end.......");
    }

    /**
     * 定时监测观众异动(每年最后一天23点计算)
     */
    //@Scheduled(cron = " 0 02 23 31 12 ?")
    public void calculateYearVisitorChange() {
        log.info("calculateYearVisitorChange start.......");
        RLock rLock = redissonClient.getLock("calculateYearVisitorChange");
        try {
            boolean getLock = rLock.tryLock(2, 10,TimeUnit.SECONDS);
            if(getLock) {
                calculateYearVisitorChangeService();
                // 休眠3秒，保证只有一个服务可以加锁成功
                Thread.sleep(3000);
            }
        } catch (Exception e) {
            log.error("calculateYearVisitorChange Exception:", e);
        } finally {
            if(rLock.isLocked() && rLock.isHeldByCurrentThread()){
                rLock.unlock();
            }
        }
        log.info("calculateYearVisitorChange end.......");
    }

    /**
     * 定时监测节假日观众异动(每天23点计算)
     */
    //@Scheduled(cron = " 0 02 23 * * ?")
    public void calculateHolidayVisitorChange() {
        log.info("calculateHolidayVisitorChange start.......");
        RLock rLock = redissonClient.getLock("calculateHolidayVisitorChange");
        try {
            boolean getLock = rLock.tryLock(2, 10,TimeUnit.SECONDS);
            if(getLock) {
                calculateHolidayVisitorChangeService();
                // 休眠3秒，保证只有一个服务可以加锁成功
                Thread.sleep(3000);
            }
        } catch (Exception e) {
            log.error("calculateHolidayVisitorChange Exception:", e);
        } finally {
            if(rLock.isLocked() && rLock.isHeldByCurrentThread()){
                rLock.unlock();
            }
        }
        log.info("calculateHolidayVisitorChange end.......");
    }


    //@Transactional
    public void calculateMuseumCollectionService() {
        List<StatisticCollectionDO> statisticCollectionDOList = Lists.newArrayList();

        List<MuseumBaseInfoDO> museumBaseInfoDOList = museumBaseInfoMapper.selectList(null);
        Map<String,MuseumBaseInfoDO> museumBaseInfoMap = new HashMap<>();
        for(MuseumBaseInfoDO museumBaseInfoDO : museumBaseInfoDOList){
            museumBaseInfoMap.put(museumBaseInfoDO.getId(),museumBaseInfoDO);
        }

        // 统计各博物馆文物全息展示数据
        List<GroupCulturalRelicsByCodeDO> culturalRelicsByCodeDOList = this.syncCulturalRelicsInfoMapper.groupCulturalRelicsByCode();
        Map<String,Integer> groupCulturalRelicsMap = new HashMap<>();
        if(CollectionUtils.isNotEmpty(culturalRelicsByCodeDOList)){
            for(GroupCulturalRelicsByCodeDO codeDO : culturalRelicsByCodeDOList){
                groupCulturalRelicsMap.put(codeDO.getMuseumId(),codeDO.getHolographicNum());
            }
        }

        // 统计数字资源数字化数据
//        List<SyncCollectionDigitalDO> syncCollectionDigitalDOList = syncCollectionDigitalMapper.selectList();
//        Map<String,SyncCollectionDigitalDO> digitalDOMap = new HashMap<>();
//        if(CollectionUtils.isNotEmpty(syncCollectionDigitalDOList)){
//            for(SyncCollectionDigitalDO digitalDO : syncCollectionDigitalDOList){
//                digitalDOMap.put(digitalDO.getUniqueCode(),digitalDO);
//            }
//        }

        // 加载所有藏品信息
        List<SyncCommCollectionRegisterInfoDO> registerInfoDOList =  this.syncCommCollectionRegisterInfoMapper.selectList();
        Map<String,List<SyncCommCollectionRegisterInfoDO>> registerMap = registerInfoDOList.stream().collect(Collectors.groupingBy(SyncCommCollectionRegisterInfoDO::getUniqueCode));

        for (String key:registerMap.keySet()){
            StatisticCollectionDO statisticCollectionDO = new StatisticCollectionDO();
            List<SyncCommCollectionRegisterInfoDO> registerList = registerMap.get(key);
            if(museumBaseInfoMap.containsKey(key)){
                MuseumBaseInfoDO museumBaseInfo = museumBaseInfoMap.get(key);
                statisticCollectionDO.setMuseumId(key);
                statisticCollectionDO.setMuseumName(museumBaseInfo.getMuseumName());
                statisticCollectionDO.setRegionCode(museumBaseInfo.getRegionCode());
                statisticCollectionDO.setCityName(museumBaseInfo.getCityName());
                int collectionNum = registerList.size();
                int valuableNum = (int)registerList.stream().filter(registerInfoDO ->
                        "一级".equals(registerInfoDO.getIdentifyLevelName())
                        || "二级".equals(registerInfoDO.getIdentifyLevelName())
                        || "三级".equals(registerInfoDO.getIdentifyLevelName()))
                        .count();
                int exhibitionNum = (int)registerList.stream().filter(registerInfoDO ->
                        !"一级".equals(registerInfoDO.getIdentifyLevelName())
                        && !"二级".equals(registerInfoDO.getIdentifyLevelName())
                        && !"三级".equals(registerInfoDO.getIdentifyLevelName())).count();
                statisticCollectionDO.setCollectionNum(collectionNum);
                statisticCollectionDO.setValuableNum(valuableNum);
                statisticCollectionDO.setExhibitionNum(exhibitionNum);
                int holographicNum = 0;
                if(groupCulturalRelicsMap.containsKey(key)){
                    holographicNum = groupCulturalRelicsMap.get(key);
                }
                statisticCollectionDO.setHolographicNum(holographicNum);
                int twoModelNum = (int)registerList.stream().filter(registerInfoDO -> StringUtils.isNotBlank(registerInfoDO.getTwoModelUrl())).count();
                int threeModelNum = (int)registerList.stream().filter(registerInfoDO -> StringUtils.isNotBlank(registerInfoDO.getThreeModelUrl())).count();
//                if(digitalDOMap.containsKey(key)){
//                    SyncCollectionDigitalDO digitalDO = digitalDOMap.get(key);
//                    twoModelNum = digitalDO.getTwoModelNum();
//                    threeModelNum = digitalDO.getThreeModelNum();
//                }

                statisticCollectionDO.setTwoModelNum(twoModelNum);
                statisticCollectionDO.setThreeModelNum(threeModelNum);
                statisticCollectionDOList.add(statisticCollectionDO);
            }
        }

        // 清空博物馆藏品统计表
        statisticCollectionMapper.deleteAll();
        // 将统计数据批量入库
        if(CollectionUtils.isNotEmpty(statisticCollectionDOList)){
            statisticCollectionMapper.batchInsert(statisticCollectionDOList);
        }
    }


    @Transactional
    public void calculateMuseumGeneralService() {
        StatisticMuseumGeneralDO statisticMuseumGeneralDO = new StatisticMuseumGeneralDO();
        // 博物馆信息
        List<MuseumBaseInfoDO> museumBaseInfoDOList = museumBaseInfoMapper.selectList((byte)0);
        int totalNum = 0;
        int settleInNum = 0;
        if(CollectionUtils.isNotEmpty(museumBaseInfoDOList)){
            totalNum = museumBaseInfoDOList.size();
            settleInNum = (int)museumBaseInfoDOList.stream().filter(museumBaseInfoDO -> museumBaseInfoDO.getSettleIn() == 1).count();
        }

        // 人员信息
        List<SyncUserInfoDO> syncUserInfoDOList = syncUserInfoMapper.selectList();
        int underPreparationNum = 0;
        int notPreparationNum = 0;
        if(CollectionUtils.isNotEmpty(syncUserInfoDOList)){
            underPreparationNum = (int)syncUserInfoDOList.stream().filter(syncUserInfoDO -> "PREN".equals(syncUserInfoDO.getPostCode())).mapToLong(SyncUserInfoDO::getCount).sum();
            notPreparationNum = (int)syncUserInfoDOList.stream().filter(syncUserInfoDO -> "CONT".equals(syncUserInfoDO.getPostCode())).mapToLong(SyncUserInfoDO::getCount).sum();
        }

        statisticMuseumGeneralDO.setTotalNum(totalNum);
        statisticMuseumGeneralDO.setSettleInNum(settleInNum);
        statisticMuseumGeneralDO.setUnderPreparationNum(underPreparationNum);
        statisticMuseumGeneralDO.setNotPreparationNum(notPreparationNum);

        //删除上一次统计
        this.statisticMuseumGeneralMapper.deleteAll();

        this.statisticMuseumGeneralMapper.insertSelective(statisticMuseumGeneralDO);
    }

    @Transactional
    public void calculateMuseumClassifyService() {
        List<AccCompanyDO> accCompanyDOList = this.accCompanyMapper.selectList();
        if(CollectionUtils.isEmpty(accCompanyDOList)){
            return;
        }
        Long companyId = accCompanyDOList.get(0).getId();
        // 查询全省所有入驻博物馆
        List<MuseumBaseInfoDO> museumBaseInfoDOList = museumBaseInfoMapper.selectSettleInList((byte)1,null);

        // 删除历史数据
        this.statisticMuseumClassifyMapper.deleteAll();

        // 按博物馆等级统计
        CommClassificationDO levelDO = this.commClassificationMapper.selectByBizCodeAndBizType(companyId,(byte)1,"BWGDJ");
        if(Objects.nonNull(levelDO)){
            List<CommClassificationDO> levelList = this.commClassificationMapper.selectChildById(companyId,levelDO.getId());
            Map<Long,List<MuseumBaseInfoDO>> museumMap = museumBaseInfoDOList.stream().collect(Collectors.groupingBy(MuseumBaseInfoDO::getLevelId));
            for(CommClassificationDO commClassificationDO : levelList){
                StatisticMuseumClassifyDO statisticMuseumClassifyDO = new StatisticMuseumClassifyDO();
                statisticMuseumClassifyDO.setClassifyType((byte)1);
                statisticMuseumClassifyDO.setStatisticId(commClassificationDO.getId());
                statisticMuseumClassifyDO.setStatisticName(commClassificationDO.getName());
                int museumNum = 0;
                if(museumMap.containsKey(commClassificationDO.getId())){
                    museumNum = museumMap.get(commClassificationDO.getId()).size();
                }
                statisticMuseumClassifyDO.setMuseumNum(museumNum);
                statisticMuseumClassifyDO.setMuseumProportion("");
                this.statisticMuseumClassifyMapper.insertSelective(statisticMuseumClassifyDO);
            }
        }

        // 按博物馆性质统计
        CommClassificationDO natureDO = this.commClassificationMapper.selectByBizCodeAndBizType(companyId,(byte)1,"BWGXZ");
        if(Objects.nonNull(natureDO)){
            List<CommClassificationDO> natureList = this.commClassificationMapper.selectChildById(companyId,natureDO.getId());
            Map<Long,List<MuseumBaseInfoDO>> museumMap = museumBaseInfoDOList.stream().collect(Collectors.groupingBy(MuseumBaseInfoDO::getNatureId));
            for(CommClassificationDO commClassificationDO : natureList){
                StatisticMuseumClassifyDO statisticMuseumClassifyDO = new StatisticMuseumClassifyDO();
                statisticMuseumClassifyDO.setClassifyType((byte)2);
                statisticMuseumClassifyDO.setStatisticId(commClassificationDO.getId());
                statisticMuseumClassifyDO.setStatisticName(commClassificationDO.getName());
                int museumNum = 0;
                if(museumMap.containsKey(commClassificationDO.getId())){
                    museumNum = museumMap.get(commClassificationDO.getId()).size();
                }
                statisticMuseumClassifyDO.setMuseumNum(museumNum);
                statisticMuseumClassifyDO.setMuseumProportion("");
                this.statisticMuseumClassifyMapper.insertSelective(statisticMuseumClassifyDO);
            }
        }

        // 博物馆类型统计
        CommClassificationDO typeDO = this.commClassificationMapper.selectByBizCodeAndBizType(companyId,(byte)1,"BWGLX");
        if(Objects.nonNull(typeDO)){
            List<CommClassificationDO> typeList = this.commClassificationMapper.selectChildById(companyId,typeDO.getId());
            Map<Long,List<MuseumBaseInfoDO>> museumMap = museumBaseInfoDOList.stream().collect(Collectors.groupingBy(MuseumBaseInfoDO::getTypeId));
            for(CommClassificationDO commClassificationDO : typeList){
                StatisticMuseumClassifyDO statisticMuseumClassifyDO = new StatisticMuseumClassifyDO();
                statisticMuseumClassifyDO.setClassifyType((byte)3);
                statisticMuseumClassifyDO.setStatisticId(commClassificationDO.getId());
                statisticMuseumClassifyDO.setStatisticName(commClassificationDO.getName());
                int museumNum = 0;
                if(museumMap.containsKey(commClassificationDO.getId())){
                    museumNum = museumMap.get(commClassificationDO.getId()).size();
                }
                statisticMuseumClassifyDO.setMuseumNum(museumNum);
                statisticMuseumClassifyDO.setMuseumProportion("");
                this.statisticMuseumClassifyMapper.insertSelective(statisticMuseumClassifyDO);
            }
        }
    }


    @Transactional
    public void calculateMuseumVisitorDayService(String startStr, String endStr){
        // 查询全省所有入驻博物馆
        List<MuseumBaseInfoDO> museumBaseInfoDOList = museumBaseInfoMapper.selectSettleInList((byte)1,null);
        if(CollectionUtils.isEmpty(museumBaseInfoDOList)){
            return;
        }

        // 从当前时间逐天计算，直到2023-01-01
        List<String> dateStrList = DateUtils.getDateList(startStr,endStr,"yyyy-MM-dd",2);
        for(String dateStr : dateStrList) {
            Date startTime = DateUtils.parseDateYYYMMDDHHmmss(dateStr + " 00:00:00");
            Date endTime = DateUtils.parseDateYYYMMDDHHmmss(dateStr + " 23:59:59");
            // 线下
            List<SyncOrderBaseDO> syncOrderBaseDOList = syncOrderBaseMapper.listByReserveDate(null, startTime, endTime, null, null);
            Map<String, List<SyncOrderBaseDO>> syncOrderBaseMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(syncOrderBaseDOList)) {
                syncOrderBaseMap = syncOrderBaseDOList.stream().collect(Collectors.groupingBy(SyncOrderBaseDO::getUniqueCode));
            }

            // 线上
//            List<SyncCulturalRelicsDO> syncCulturalRelicsDOList = syncCulturalRelicsMapper.listByCondition(startTime, endTime, null);
//            Map<String, List<SyncCulturalRelicsDO>> syncCulturalRelicsMap = new HashMap<>();
//            if (CollectionUtils.isNotEmpty(syncCulturalRelicsDOList)) {
//                syncCulturalRelicsMap = syncCulturalRelicsDOList.stream().collect(Collectors.groupingBy(SyncCulturalRelicsDO::getUniqueCode));
//            }

            List<StatisticVisitorDayDO> batchVisitorDayDOList = Lists.newArrayList();
            for (MuseumBaseInfoDO museumBaseInfoDO : museumBaseInfoDOList) {
                String museumId = museumBaseInfoDO.getId();
                // 单馆线下集合
                List<SyncOrderBaseDO> museumOfflineList = Lists.newArrayList();
                if (syncOrderBaseMap.containsKey(museumId)) {
                    museumOfflineList = syncOrderBaseMap.get(museumId);
                }
                // 单馆线上集合
                List<SyncCulturalRelicsDO> museumOnlineList = Lists.newArrayList();
//                if (syncCulturalRelicsMap.containsKey(museumId)) {
//                    museumOnlineList = syncCulturalRelicsMap.get(museumId);
//                }

                // 生成观众日统计数据
                StatisticVisitorDayDO statisticVisitorDayDO = new StatisticVisitorDayDO();
                statisticVisitorDayDO.setStatisticDay(dateStr);
                statisticVisitorDayDO.setMuseumId(museumId);
                statisticVisitorDayDO.setMuseumName(museumBaseInfoDO.getMuseumName());
                statisticVisitorDayDO.setRegionCode(museumBaseInfoDO.getRegionCode());
                statisticVisitorDayDO.setCityName(museumBaseInfoDO.getCityName());
                int holographicNum = 0;
                int overallAccessCount = 0;
                int onlineNum = 0;
                holographicNum = (int) museumOnlineList.stream().mapToLong(SyncCulturalRelicsDO::getHolographicAccessCount).sum();
                overallAccessCount = (int) museumOnlineList.stream().mapToLong(SyncCulturalRelicsDO::getOverallAccessCount).sum();
                onlineNum = holographicNum + overallAccessCount;
                statisticVisitorDayDO.setOnlineNum(onlineNum);
                statisticVisitorDayDO.setHolographicNum(holographicNum);
                statisticVisitorDayDO.setOverallViewNum(overallAccessCount);

                int personalNum = 0;
                int teamNum = 0;
                int teamManNum = 0;
                int offLineNum = 0;
                int orderNum = 0;
                int provinceNum = 0;
                int outsideNum = 0;
                int abroadNum = 0;
                int adultNum = 0;
                int childrenNum = 0;
                int manNum = 0;
                int womanNum = 0;

                personalNum = (int) museumOfflineList.stream().filter(orderBaseDO -> orderBaseDO.getOrderType() == 1 && orderBaseDO.getStatus() == 2)
                        .mapToLong(SyncOrderBaseDO::getTotalTicketCount).sum();
                teamManNum = (int) museumOfflineList.stream().filter(orderBaseDO -> orderBaseDO.getOrderType() == 2 && orderBaseDO.getStatus() == 2)
                        .mapToLong(SyncOrderBaseDO::getTotalTicketCount).sum();
                teamNum = (int) museumOfflineList.stream().filter(orderBaseDO -> orderBaseDO.getOrderType() == 2 && orderBaseDO.getStatus() == 2)
                        .count();
                offLineNum = personalNum + teamManNum;
                orderNum = (int) museumOfflineList.stream().mapToLong(SyncOrderBaseDO::getTotalTicketCount).sum();

                provinceNum = (int) museumOfflineList.stream().
                        filter(orderBaseDO -> orderBaseDO.getOrderType() == 1
                                && orderBaseDO.getStatus() == 2
                                && orderBaseDO.getTouristCertificateType() != 2
                                && orderBaseDO.getTouristCertificateNo() != null
                                && orderBaseDO.getTouristCertificateNo().length() == 18
                                && orderBaseDO.getTouristCertificateNo().startsWith(locationAdCode))
                        .mapToLong(SyncOrderBaseDO::getTotalTicketCount).sum();
                outsideNum = (int) museumOfflineList.stream().
                        filter(orderBaseDO -> orderBaseDO.getOrderType() == 1
                                && orderBaseDO.getStatus() == 2
                                && orderBaseDO.getTouristCertificateType() != 2
                                && orderBaseDO.getTouristCertificateNo() != null
                                && orderBaseDO.getTouristCertificateNo().length() == 18
                                && !orderBaseDO.getTouristCertificateNo().startsWith(locationAdCode))
                        .mapToLong(SyncOrderBaseDO::getTotalTicketCount).sum();
                abroadNum = (int) museumOfflineList.stream().
                        filter(orderBaseDO -> orderBaseDO.getOrderType() == 1
                                && orderBaseDO.getStatus() == 2
                                && orderBaseDO.getTouristCertificateType() == 2)
                        .mapToLong(SyncOrderBaseDO::getTotalTicketCount).sum();
                adultNum = (int) museumOfflineList.stream().
                        filter(orderBaseDO -> orderBaseDO.getOrderType() == 1
                                && orderBaseDO.getStatus() == 2
                                && orderBaseDO.getTouristCertificateType() == 1
                                && orderBaseDO.getTouristCertificateNo() != null
                                && orderBaseDO.getTouristCertificateNo().length() == 18
                                && DateUtils.getAge(orderBaseDO.getTouristCertificateNo()) >= 18)
                        .mapToLong(SyncOrderBaseDO::getTotalTicketCount).sum();
                childrenNum = (int) museumOfflineList.stream()
                        .filter(orderBaseDO -> orderBaseDO.getOrderType() == 1
                                && orderBaseDO.getStatus() == 2
                                && orderBaseDO.getTouristCertificateType() == 1
                                && orderBaseDO.getTouristCertificateNo() != null
                                && orderBaseDO.getTouristCertificateNo().length() == 18
                                && DateUtils.getAge(orderBaseDO.getTouristCertificateNo()) < 18)
                        .mapToLong(SyncOrderBaseDO::getTotalTicketCount).sum();
                manNum = (int) museumOfflineList.stream()
                        .filter(orderBaseDO -> orderBaseDO.getOrderType() == 1
                                && orderBaseDO.getStatus() == 2
                                && orderBaseDO.getTouristCertificateType() == 1
                                && orderBaseDO.getTouristCertificateNo() != null
                                && orderBaseDO.getTouristCertificateNo().length() == 18
                                && Integer.parseInt(orderBaseDO.getTouristCertificateNo().substring(16, 17)) / 2 > 0)
                        .mapToLong(SyncOrderBaseDO::getTotalTicketCount).sum();
                womanNum = (int) museumOfflineList.stream()
                        .filter(orderBaseDO -> orderBaseDO.getOrderType() == 1
                                && orderBaseDO.getStatus() == 2
                                && orderBaseDO.getTouristCertificateType() == 1
                                && orderBaseDO.getTouristCertificateNo() != null
                                && orderBaseDO.getTouristCertificateNo().length() == 18
                                && Integer.parseInt(orderBaseDO.getTouristCertificateNo().substring(16, 17)) / 2 == 0)
                        .mapToLong(SyncOrderBaseDO::getTotalTicketCount).sum();

                statisticVisitorDayDO.setOrderNum(orderNum);
                statisticVisitorDayDO.setOfflineNum(offLineNum);
                statisticVisitorDayDO.setPersonalNum(personalNum);
                statisticVisitorDayDO.setTeamNum(teamNum);
                statisticVisitorDayDO.setTeamManNum(teamManNum);
                statisticVisitorDayDO.setProvinceNum(provinceNum);
                statisticVisitorDayDO.setOutsideNum(outsideNum);
                statisticVisitorDayDO.setAbroadNum(abroadNum);
                statisticVisitorDayDO.setAdultNum(adultNum);
                statisticVisitorDayDO.setChildrenNum(childrenNum);
                statisticVisitorDayDO.setManNum(manNum);
                statisticVisitorDayDO.setWomanNum(womanNum);
                int totalNum = onlineNum + offLineNum;
                statisticVisitorDayDO.setTotalNum(totalNum);
                batchVisitorDayDOList.add(statisticVisitorDayDO);
            }

            statisticVisitorDayMapper.deleteByMuseumIdAndStatisticDay(null, dateStr);
            if (CollectionUtils.isNotEmpty(batchVisitorDayDOList)) {
                statisticVisitorDayMapper.batchInsert(batchVisitorDayDOList);
            }
        }

    }

    @Transactional
    public void calculateMuseumVisitorMonthService(){
        // 查询全省所有入驻博物馆
        List<MuseumBaseInfoDO> museumBaseInfoDOList = museumBaseInfoMapper.selectList(null);
        Map<String,MuseumBaseInfoDO> museumBaseInfoDOMap = new HashMap<>();
        if(CollectionUtils.isNotEmpty(museumBaseInfoDOList)){
            for(MuseumBaseInfoDO museumBaseInfoDO : museumBaseInfoDOList){
                museumBaseInfoDOMap.put(museumBaseInfoDO.getId(),museumBaseInfoDO);
            }
        }

        List<GroupVisitorMonthDataDO> groupVisitorMonthDataDOList = this.statisticVisitorDayMapper.groupVisitorMonthData();
        if(CollectionUtils.isNotEmpty(groupVisitorMonthDataDOList)){
           List<StatisticVisitorMonthDO> statisticVisitorMonthDOList = groupVisitorMonthDataDOList.stream().map(groupVisitorMonthDataDO -> {
               StatisticVisitorMonthDO statisticVisitorMonthDO = new StatisticVisitorMonthDO();
               BeanUtils.copyProperties(groupVisitorMonthDataDO,statisticVisitorMonthDO);
               if(museumBaseInfoDOMap.containsKey(groupVisitorMonthDataDO.getMuseumId())){
                   statisticVisitorMonthDO.setMuseumName(museumBaseInfoDOMap.get(groupVisitorMonthDataDO.getMuseumId()).getMuseumName());
                   statisticVisitorMonthDO.setRegionCode(museumBaseInfoDOMap.get(groupVisitorMonthDataDO.getMuseumId()).getRegionCode());
                   statisticVisitorMonthDO.setCityName(museumBaseInfoDOMap.get(groupVisitorMonthDataDO.getMuseumId()).getCityName());
               }
               return statisticVisitorMonthDO;
           }).collect(Collectors.toList());

            // 删除历史数据
            this.statisticVisitorMonthMapper.deleteAll();
            if(CollectionUtils.isNotEmpty(statisticVisitorMonthDOList)){
                this.statisticVisitorMonthMapper.batchInsert(statisticVisitorMonthDOList);
            }
        }
    }

    @Transactional
    public void calculateMuseumVisitorYearService(){
        // 查询全省所有入驻博物馆
        List<MuseumBaseInfoDO> museumBaseInfoDOList = museumBaseInfoMapper.selectList(null);
        Map<String,MuseumBaseInfoDO> museumBaseInfoDOMap = new HashMap<>();
        if(CollectionUtils.isNotEmpty(museumBaseInfoDOList)){
            for(MuseumBaseInfoDO museumBaseInfoDO : museumBaseInfoDOList){
                museumBaseInfoDOMap.put(museumBaseInfoDO.getId(),museumBaseInfoDO);
            }
        }

        //获取一年前的日期
        //String startDay = DateUtils.getOrderDay(new Date(),-365,1);
        //获取本年第一天
        String startDay = DateUtils.formatDateYYYMMDD(DateUtils.getTimesYearMorning());

        List<GroupVisitorYearDataDO> groupVisitorYearDataDOList = this.statisticVisitorDayMapper.groupVisitorYearData(startDay);
        if(CollectionUtils.isNotEmpty(groupVisitorYearDataDOList)){
            List<StatisticVisitorYearDO> statisticVisitorYearDOList = groupVisitorYearDataDOList.stream().map(groupVisitorYearDataDO -> {
                StatisticVisitorYearDO statisticVisitorYearDO = new StatisticVisitorYearDO();
                BeanUtils.copyProperties(groupVisitorYearDataDO,statisticVisitorYearDO);
                if(museumBaseInfoDOMap.containsKey(groupVisitorYearDataDO.getMuseumId())){
                    statisticVisitorYearDO.setMuseumName(museumBaseInfoDOMap.get(groupVisitorYearDataDO.getMuseumId()).getMuseumName());
                    statisticVisitorYearDO.setRegionCode(museumBaseInfoDOMap.get(groupVisitorYearDataDO.getMuseumId()).getRegionCode());
                    statisticVisitorYearDO.setCityName(museumBaseInfoDOMap.get(groupVisitorYearDataDO.getMuseumId()).getCityName());
                }
                return statisticVisitorYearDO;
            }).collect(Collectors.toList());

            // 删除历史数据
            this.statisticVisitorYearMapper.deleteAll();
            if(CollectionUtils.isNotEmpty(statisticVisitorYearDOList)){
                this.statisticVisitorYearMapper.batchInsert(statisticVisitorYearDOList);
            }
        }
    }

    @Transactional
    public void calculateCollectionChangeService(){
        // 加载临时表数据
        List<DataChangeDetailTemporaryDO> temporaryDOList = this.dataChangeDetailTemporaryMapper.selectList();
        if(CollectionUtils.isEmpty(temporaryDOList)){
            return;
        }

        Date happenTime = DateUtils.parseDateYYYMMDD(DateUtils.getOrderDay(new Date(),-1,0));

        // 藏品新增异动
        List<DataChangeDetailTemporaryDO> upChangeList = temporaryDOList.stream().filter(temporaryDO -> temporaryDO.getUpDown() == 1).collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(upChangeList)){
            int upNum = upChangeList.stream().mapToInt(DataChangeDetailTemporaryDO::getChangeData).sum();
            DataChangeInfoDO dataChangeInfoDO = new DataChangeInfoDO();
            dataChangeInfoDO.setChangeType((byte)1);
            dataChangeInfoDO.setChangeDescription("全省藏品总数新增"+upNum+"件/套");
            dataChangeInfoDO.setHappenTime(happenTime);
            this.dataChangeInfoMapper.insertSelective(dataChangeInfoDO);

            Long changeId = dataChangeInfoDO.getId();
            for(DataChangeDetailTemporaryDO temporaryDO : upChangeList){
                DataChangeDetailInfoDO dataChangeDetailInfoDO = new DataChangeDetailInfoDO();
                dataChangeDetailInfoDO.setChangeInfoId(changeId);
                dataChangeDetailInfoDO.setMuseumId(temporaryDO.getMuseumId());
                dataChangeDetailInfoDO.setChangeData(String.valueOf(temporaryDO.getChangeData()));
                dataChangeDetailInfoDO.setUpDown((byte)1);
                this.dataChangeDetailInfoMapper.insertSelective(dataChangeDetailInfoDO);
            }
        }

        // 藏品注销异动
        List<DataChangeDetailTemporaryDO> downChangeList = temporaryDOList.stream().filter(temporaryDO -> temporaryDO.getUpDown() == 2).collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(downChangeList)){
            int downNum = downChangeList.stream().mapToInt(DataChangeDetailTemporaryDO::getChangeData).sum();
            DataChangeInfoDO dataChangeInfoDO = new DataChangeInfoDO();
            dataChangeInfoDO.setChangeType((byte)1);
            dataChangeInfoDO.setChangeDescription("全省藏品新增注销"+downNum+"件/套");
            dataChangeInfoDO.setHappenTime(happenTime);
            this.dataChangeInfoMapper.insertSelective(dataChangeInfoDO);

            Long changeId = dataChangeInfoDO.getId();
            for(DataChangeDetailTemporaryDO temporaryDO : downChangeList){
                DataChangeDetailInfoDO dataChangeDetailInfoDO = new DataChangeDetailInfoDO();
                dataChangeDetailInfoDO.setChangeInfoId(changeId);
                dataChangeDetailInfoDO.setMuseumId(temporaryDO.getMuseumId());
                dataChangeDetailInfoDO.setChangeData(String.valueOf(temporaryDO.getChangeData()));
                dataChangeDetailInfoDO.setUpDown((byte)2);
                this.dataChangeDetailInfoMapper.insertSelective(dataChangeDetailInfoDO);
            }
        }

        // 删除临时表数据
        this.dataChangeDetailTemporaryMapper.deleteAll();
    }

    @Transactional
    public void calculateMonthVisitorChangeService(){
        // 获取当前月份
        String thisMonth = DateUtils.formatDateYYYMM();
        // 获取去年同期月份
        String lastMonth = (Integer.parseInt(thisMonth.substring(0,4))-1)+thisMonth.substring(4,7);

        // 获取去年同期整月观众
        List<StatisticVisitorMonthDO> lastVisitorMonthDOList =  this.statisticVisitorMonthMapper.selectListByMonth(lastMonth);
        if(CollectionUtils.isEmpty(lastVisitorMonthDOList)){
            return;
        }
        Map<String,List<StatisticVisitorMonthDO>> lastVisitorMonthMap = lastVisitorMonthDOList.stream().collect(Collectors.groupingBy(StatisticVisitorMonthDO::getMuseumId));

        // 获取当前月份观众
        List<StatisticVisitorMonthDO> thisVisitorMonthDOList =  this.statisticVisitorMonthMapper.selectListByMonth(thisMonth);
        if(CollectionUtils.isEmpty(thisVisitorMonthDOList)){
            return;
        }
        Map<String,List<StatisticVisitorMonthDO>> thisVisitorMonthMap = thisVisitorMonthDOList.stream().collect(Collectors.groupingBy(StatisticVisitorMonthDO::getMuseumId));

        // 记录新增超30%的博物馆
        List<DataChangeDetailInfoDO> upList = Lists.newArrayList();
        // 记录下降超30%的博物馆
        List<DataChangeDetailInfoDO> downList = Lists.newArrayList();

        // 循环计算
        for (String key:lastVisitorMonthMap.keySet()) {
            if(thisVisitorMonthMap.containsKey(key)){
                int lastTotalNum = lastVisitorMonthMap.get(key).stream().mapToInt(StatisticVisitorMonthDO::getTotalNum).sum();
                int thisTotalNum = thisVisitorMonthMap.get(key).stream().mapToInt(StatisticVisitorMonthDO::getTotalNum).sum();
                if(lastTotalNum == 0){
                    continue;
                }
                DataChangeDetailInfoDO dataChangeDetailInfoDO = new DataChangeDetailInfoDO();
                dataChangeDetailInfoDO.setMuseumId(key);

                float difference = thisTotalNum - lastTotalNum;
                float differenceRate = difference/lastTotalNum;
                if(differenceRate > 0.3){
                    dataChangeDetailInfoDO.setChangeData(String.valueOf(differenceRate));
                    dataChangeDetailInfoDO.setUpDown((byte)1);
                    upList.add(dataChangeDetailInfoDO);
                }else if(differenceRate < -0.3){
                    dataChangeDetailInfoDO.setChangeData(String.valueOf(differenceRate).replace("-",""));
                    dataChangeDetailInfoDO.setUpDown((byte)1);
                    downList.add(dataChangeDetailInfoDO);
                }
            }
        }

        Date happenTime = DateUtils.parseDateYYYMMDD(DateUtils.getOrderDay(new Date(),-1,0));
        DataChangeInfoDO dataChangeInfoDO = new DataChangeInfoDO();
        dataChangeInfoDO.setChangeType((byte)2);
        dataChangeInfoDO.setHappenTime(happenTime);
        if(CollectionUtils.isNotEmpty(upList)){
            dataChangeInfoDO.setChangeDescription(upList.size()+"馆"+thisMonth+"月观众人次同比↑超30%");
            dataChangeInfoMapper.insertSelective(dataChangeInfoDO);
            Long changeId = dataChangeInfoDO.getId();
            for(DataChangeDetailInfoDO detailInfoDO : upList){
                detailInfoDO.setChangeInfoId(changeId);
                dataChangeDetailInfoMapper.insertSelective(detailInfoDO);
            }
        }

        if(CollectionUtils.isNotEmpty(downList)){
            dataChangeInfoDO.setChangeDescription(downList.size()+"馆"+thisMonth+"月观众人次同比↓超30%");
            dataChangeInfoMapper.insertSelective(dataChangeInfoDO);
            Long changeId = dataChangeInfoDO.getId();
            for(DataChangeDetailInfoDO detailInfoDO : downList){
                detailInfoDO.setChangeInfoId(changeId);
                dataChangeDetailInfoMapper.insertSelective(detailInfoDO);
            }
        }
    }

    @Transactional
    public void calculateYearVisitorChangeService(){
        // 获取当前年份
        String thisYear = DateUtils.formatDateYYY();
        // 获取去年年份
        String lastYear = String.valueOf(Integer.parseInt(thisYear.substring(0,4))-1);

        List<GroupMuseumByYearDO> lastYearList = statisticVisitorMonthMapper.groupMuseumByYear(lastYear);
        Map<String,Integer> lastYearMap = new HashMap<>();
        for(GroupMuseumByYearDO groupMuseumByYearDO : lastYearList){
            lastYearMap.put(groupMuseumByYearDO.getMuseumId(),groupMuseumByYearDO.getTotalNum());
        }

        List<GroupMuseumByYearDO> thisYearList = statisticVisitorMonthMapper.groupMuseumByYear(thisYear);
        Map<String,Integer> thisYearMap = new HashMap<>();
        for(GroupMuseumByYearDO groupMuseumByYearDO : thisYearList){
            thisYearMap.put(groupMuseumByYearDO.getMuseumId(),groupMuseumByYearDO.getTotalNum());
        }

        // 记录新增超30%的博物馆
        List<DataChangeDetailInfoDO> upList = Lists.newArrayList();
        // 记录下降超30%的博物馆
        List<DataChangeDetailInfoDO> downList = Lists.newArrayList();

        // 循环计算
        for (String key:lastYearMap.keySet()) {
            if(thisYearMap.containsKey(key)){
                int lastTotalNum = lastYearMap.get(key);
                int thisTotalNum = thisYearMap.get(key);
                if(lastTotalNum == 0){
                    continue;
                }
                DataChangeDetailInfoDO dataChangeDetailInfoDO = new DataChangeDetailInfoDO();
                dataChangeDetailInfoDO.setMuseumId(key);

                float difference = thisTotalNum - lastTotalNum;
                float differenceRate = difference/lastTotalNum;
                if(differenceRate > 0.3){
                    dataChangeDetailInfoDO.setChangeData(String.valueOf(differenceRate));
                    dataChangeDetailInfoDO.setUpDown((byte)1);
                    upList.add(dataChangeDetailInfoDO);
                }else if(differenceRate < -0.3){
                    dataChangeDetailInfoDO.setChangeData(String.valueOf(differenceRate).replace("-",""));
                    dataChangeDetailInfoDO.setUpDown((byte)1);
                    downList.add(dataChangeDetailInfoDO);
                }
            }
        }

        Date happenTime = DateUtils.parseDateYYYMMDD(DateUtils.getOrderDay(new Date(),-1,0));
        DataChangeInfoDO dataChangeInfoDO = new DataChangeInfoDO();
        dataChangeInfoDO.setChangeType((byte)2);
        dataChangeInfoDO.setHappenTime(happenTime);
        if(CollectionUtils.isNotEmpty(upList)){
            dataChangeInfoDO.setChangeDescription(upList.size()+"馆"+thisYear+"年观众人次同比↑超30%");
            dataChangeInfoMapper.insertSelective(dataChangeInfoDO);
            Long changeId = dataChangeInfoDO.getId();
            for(DataChangeDetailInfoDO detailInfoDO : upList){
                detailInfoDO.setChangeInfoId(changeId);
                dataChangeDetailInfoMapper.insertSelective(detailInfoDO);
            }
        }

        if(CollectionUtils.isNotEmpty(downList)){
            dataChangeInfoDO.setChangeDescription(downList.size()+"馆"+thisYear+"年观众人次同比↓超30%");
            dataChangeInfoMapper.insertSelective(dataChangeInfoDO);
            Long changeId = dataChangeInfoDO.getId();
            for(DataChangeDetailInfoDO detailInfoDO : downList){
                detailInfoDO.setChangeInfoId(changeId);
                dataChangeDetailInfoMapper.insertSelective(detailInfoDO);
            }
        }
    }

    @Transactional
    public void calculateHolidayVisitorChangeService(){
        // 初始化节假日
        holidayInit();
        // 获取当前年份
        String thisYear = DateUtils.formatDateYYY();
        // 获取今日日期
        String today = DateUtils.formatDateYYYMMDD();
        // 今年节假日列表
        List<HolidayBO> thisHolidayList =  holidaysMap.get(Integer.parseInt(thisYear));
        if(CollectionUtils.isEmpty(thisHolidayList)){
            return;
        }
        // 获取去年节假日列表
        String lastYear = String.valueOf(Integer.parseInt(thisYear.substring(0,4))-1);
        List<HolidayBO> lastHolidayList =  holidaysMap.get(Integer.parseInt(lastYear));
        if(CollectionUtils.isEmpty(lastHolidayList)){
            return;
        }

        // 保存所有节假日结束时间
        Map<String,String> endDayMap = new HashMap<>();
        // 保存今年所有节假日
        Map<String,HolidayBO> thisHolidayBOMap = new HashMap<>();
        for(HolidayBO holidayBO : thisHolidayList){
            endDayMap.put(DateUtils.formatDateYYYMMDD(holidayBO.getEndTime()),holidayBO.getName());
            thisHolidayBOMap.put(holidayBO.getName(),holidayBO);
        }

        // 保存去年所有节假日
        Map<String,HolidayBO> lastHolidayMap = new HashMap<>();
        for(HolidayBO holidayBO : lastHolidayList){
            lastHolidayMap.put(holidayBO.getName(),holidayBO);
        }

        // 如果今天是节假日得结束时间
        if(endDayMap.containsKey(today)){
            String name = endDayMap.get(today);
            // 获取今年节假日时间
            HolidayBO thisHolidayBO = thisHolidayBOMap.get(name);
            List<GroupMuseumByHolidayDO>  thisYearList = statisticVisitorDayMapper.groupMuseumByHoliday(DateUtils.formatDateYYYMMDD(thisHolidayBO.getStartTime()),DateUtils.formatDateYYYMMDD(thisHolidayBO.getStartTime()));
            Map<String,Integer> thisYearMap = new HashMap<>();
            for(GroupMuseumByHolidayDO groupMuseumByHolidayDO : thisYearList){
                thisYearMap.put(groupMuseumByHolidayDO.getMuseumId(),groupMuseumByHolidayDO.getTotalNum());
            }

            // 获取去年节假日时间
            HolidayBO lastHolidayBO = lastHolidayMap.get(name);
            List<GroupMuseumByHolidayDO>  lastYearList = statisticVisitorDayMapper.groupMuseumByHoliday(DateUtils.formatDateYYYMMDD(lastHolidayBO.getStartTime()),DateUtils.formatDateYYYMMDD(lastHolidayBO.getStartTime()));
            Map<String,Integer> lastYearMap = new HashMap<>();
            for(GroupMuseumByHolidayDO groupMuseumByHolidayDO : lastYearList){
                lastYearMap.put(groupMuseumByHolidayDO.getMuseumId(),groupMuseumByHolidayDO.getTotalNum());
            }

            // 记录新增超30%的博物馆
            List<DataChangeDetailInfoDO> upList = Lists.newArrayList();
            // 记录下降超30%的博物馆
            List<DataChangeDetailInfoDO> downList = Lists.newArrayList();

            // 循环计算
            for (String key:lastYearMap.keySet()) {
                if(thisYearMap.containsKey(key)){
                    int lastTotalNum = lastYearMap.get(key);
                    int thisTotalNum = thisYearMap.get(key);
                    if(lastTotalNum == 0){
                        continue;
                    }
                    DataChangeDetailInfoDO dataChangeDetailInfoDO = new DataChangeDetailInfoDO();
                    dataChangeDetailInfoDO.setMuseumId(key);

                    float difference = thisTotalNum - lastTotalNum;
                    float differenceRate = difference/lastTotalNum;
                    if(differenceRate > 0.3){
                        dataChangeDetailInfoDO.setChangeData(String.valueOf(differenceRate));
                        dataChangeDetailInfoDO.setUpDown((byte)1);
                        upList.add(dataChangeDetailInfoDO);
                    }else if(differenceRate < -0.3){
                        dataChangeDetailInfoDO.setChangeData(String.valueOf(differenceRate).replace("-",""));
                        dataChangeDetailInfoDO.setUpDown((byte)1);
                        downList.add(dataChangeDetailInfoDO);
                    }
                }
            }

            Date happenTime = DateUtils.parseDateYYYMMDD(DateUtils.getOrderDay(new Date(),-1,0));
            DataChangeInfoDO dataChangeInfoDO = new DataChangeInfoDO();
            dataChangeInfoDO.setChangeType((byte)2);
            dataChangeInfoDO.setHappenTime(happenTime);
            if(CollectionUtils.isNotEmpty(upList)){
                dataChangeInfoDO.setChangeDescription(upList.size()+"馆"+thisYear+"年观众人次同比↑超30%");
                dataChangeInfoMapper.insertSelective(dataChangeInfoDO);
                Long changeId = dataChangeInfoDO.getId();
                for(DataChangeDetailInfoDO detailInfoDO : upList){
                    detailInfoDO.setChangeInfoId(changeId);
                    dataChangeDetailInfoMapper.insertSelective(detailInfoDO);
                }
            }

            if(CollectionUtils.isNotEmpty(downList)){
                dataChangeInfoDO.setChangeDescription(downList.size()+"馆"+thisYear+"年观众人次同比↓超30%");
                dataChangeInfoMapper.insertSelective(dataChangeInfoDO);
                Long changeId = dataChangeInfoDO.getId();
                for(DataChangeDetailInfoDO detailInfoDO : downList){
                    detailInfoDO.setChangeInfoId(changeId);
                    dataChangeDetailInfoMapper.insertSelective(detailInfoDO);
                }
            }
        }
    }

}
