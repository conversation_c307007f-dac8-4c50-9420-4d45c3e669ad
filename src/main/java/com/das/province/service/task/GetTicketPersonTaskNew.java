package com.das.province.service.task;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.das.province.common.builder.HttpClientCustomizeBuilder;
import com.das.province.common.exception.CommonErrorCodeEnum;
import com.das.province.common.exception.CommonException;
import com.das.province.common.http.HttpConfig;
import com.das.province.common.http.HttpHeader;
import com.das.province.common.http.HttpMethods;
import com.das.province.common.http.HttpResult;
import com.das.province.common.utils.DateUtils;
import com.das.province.common.utils.HttpClientUtil;
import com.das.province.common.utils.UniqueIDUtil;
import com.das.province.dependency.bo.HttpResultBO;
import com.das.province.infr.dataobject.SyncOrderBaseDO;
import com.das.province.infr.mapper.SyncOrderBaseMapper;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

@Slf4j
@Service
public class GetTicketPersonTaskNew {

    @Resource(name = "provinceHttpClientCustomizeBuilder")
    private HttpClientCustomizeBuilder httpClientCustomizeBuilder;

    /**
     * 伪满闸机历史数据接口（新）
     */
    private final String weiManTicketHistoryUrl = "https://index.wmhg.com.cn/api/get_id_card_list_new";

    /**
     * 伪满闸机实时数据接口（新）
     */
    private final String weiManTicketRealUrl = "https://index.wmhg.com.cn/api/get_real_time_list";

    /**
     * 东北沦陷票务数据接口（新）
     */
    private final String lunXianTicketRealUrl = "https://index.wmhg.com.cn/api/SdTicket/getTcWholeTicketYsgData";

    @Resource
    private SyncOrderBaseMapper syncOrderBaseMapper;

    /**
     * 伪满皇宫观众历史数据获取入库
     */
    @Transactional
    public Integer setWeiManTicketHistoryData(Long startTime, Long endTime) {
        int num = 0;
        try {
            String url = weiManTicketHistoryUrl + "?start_time=" + startTime + "&end_time=" + endTime;
            HttpConfig config = HttpConfig.custom()
                    .headers(HttpHeader.custom().build())
                    .url(url)
                    .encoding("utf-8")
                    .method(HttpMethods.GET)
                    .client(httpClientCustomizeBuilder.build());
            HttpResult result = HttpClientUtil.sendAndGetResp(config);
            if (HttpStatus.SC_OK == result.getStatusCode()) {

                HttpResultBO httpResultBO = JSON.parseObject(result.getResult(), HttpResultBO.class);
                if (StringUtils.isNotBlank(httpResultBO.getData())) {
                    JSONArray wmDataArray = JSONObject.parseArray(httpResultBO.getData());
                    num = wmDataArray.size();
                    dealHistoryData(wmDataArray,startTime,endTime);
                }else {
                    throw new Exception( "获取伪满皇宫闸机历史数据失败：" + result.getResult());
                }
            }
        } catch(Exception e){
            log.error("从伪满票务接口获取数据失败："+ e.getMessage());
        }
        return num;
    }

    /**
     * 伪满皇宫观众实时数据获取入库
     */
    @Transactional
    public Integer setWeiManTicketRealData(Long startTime, Long endTime) {
        int num = 0;
        try {
            HttpConfig config = HttpConfig.custom()
                    .headers(HttpHeader.custom().build())
                    .url(weiManTicketRealUrl)
                    .encoding("utf-8")
                    .method(HttpMethods.GET)
                    .client(httpClientCustomizeBuilder.build());
            HttpResult result = HttpClientUtil.sendAndGetResp(config);
            if (HttpStatus.SC_OK == result.getStatusCode()) {
                HttpResultBO httpResultBO = JSON.parseObject(result.getResult(), HttpResultBO.class);
                if (StringUtils.isNotBlank(httpResultBO.getData())) {
                    JSONArray wmDataArray = JSONObject.parseArray(httpResultBO.getData());
                    dealRealtimeData(wmDataArray);
                    num = wmDataArray.size();
                }else {
                    throw new CommonException(CommonErrorCodeEnum.UNKNOWN_ERROR, "获取伪满皇宫闸机历史数据失败");
                }
            }
        } catch(CommonException e){
            log.error("从伪满票务接口获取数据失败：{}", e.getMessage());
        }
        log.error("{}--{}从接口获取数据量：{}", DateUtils.formatDateYYYMMDDHHmmss(new Date(startTime * 1000)), DateUtils.formatDateYYYMMDDHHmmss(new Date(endTime * 1000)), num);
        return num;
    }

    /**
     * 东北沦陷陈列馆观众数据获取入库
     */
    @Transactional
    public Integer setLunXianTicketRealData(Long startTime, Long endTime) {
        int num = 0;
        String url = lunXianTicketRealUrl + "?start_time=" + startTime + "&end_time=" + endTime;
        try {
            HttpConfig config = HttpConfig.custom()
                    .headers(HttpHeader.custom().build())
                    .url(url)
                    .encoding("utf-8")
                    .method(HttpMethods.GET)
                    .timeout(10000)
                    .client(httpClientCustomizeBuilder.build());
            HttpResult result = HttpClientUtil.sendAndGetResp(config);
            if (HttpStatus.SC_OK == result.getStatusCode()) {
                HttpResultBO httpResultBO = JSON.parseObject(result.getResult(), HttpResultBO.class);
                if (StringUtils.isNotBlank(httpResultBO.getData())) {
                    JSONArray lxDataArray = JSONObject.parseArray(httpResultBO.getData());
                    dealLxRealtimeData(lxDataArray,startTime,endTime);
                    num = lxDataArray.size();
                }else {
                    throw new CommonException(CommonErrorCodeEnum.UNKNOWN_ERROR, "获取东北沦陷陈列馆数据失败");
                }
            }
        } catch(CommonException e){
            log.error("从东北沦陷陈列馆务接口获取数据失败：{}", e.getMessage());
        }
        return num;
    }

    @Transactional
    public void dealLxRealtimeData(JSONArray lxDataArray,Long startTime, Long endTime) {
        // 伪满票务
        List<SyncOrderBaseDO> cachedDataList = new ArrayList<>();
        Date date = new Date();
        if (!lxDataArray.isEmpty()) {
            for (int i = 0; i < lxDataArray.size(); i++) {
                JSONObject weiManJson = lxDataArray.getJSONObject(i);
                String cardName = weiManJson.getString("card_name");
                String cardNumber = weiManJson.getString("card_number");
                String courtyardDate = weiManJson.getString("courtyard_date");
                Byte cardType = weiManJson.getByte("card_type");
                if (StringUtils.isBlank(cardName)) {
                    cardName = "无";
                }
                if (StringUtils.isBlank(cardNumber)) {
                    cardNumber = "0000";
                }
                if (cardNumber.length() != 18) {
                    cardType = 4;
                }
                if(cardType == 0){
                    cardType = 1;
                }
                String str = "^[1-9]\\d{5}(18|19|20)?\\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\\d{3}([0-9]|X)$";
                if(cardType == 1){
                    if(!cardNumber.matches(str)){
                        cardType = 4;
                    }
                }
                // 东北沦陷
                SyncOrderBaseDO orderBO = new SyncOrderBaseDO();
                orderBO.setUniqueCode("1702249354724773888");
                orderBO.setVenueName("东北沦陷史陈列馆");
                orderBO.setOrderNo(UniqueIDUtil.getUniqueID());
                orderBO.setTotalTicketCount(1L);
                orderBO.setTotalPeopleCount(1L);
                orderBO.setOrderType((byte)1);
                orderBO.setReserveType((byte)1);
                orderBO.setStatus((byte)2);
                orderBO.setTouristName(cardName);
                orderBO.setTouristCertificateType(cardType);
                orderBO.setTouristCertificateNo(cardNumber);
                orderBO.setReserveDate(DateUtils.parseDateYYYMMDDHHmmss(courtyardDate));
                orderBO.setGmtCreate(date);
                orderBO.setGmtModified(date);
                cachedDataList.add(orderBO);
            }
        }

        if(!CollectionUtils.isEmpty(cachedDataList)){
            lxOrderBaseOperate(cachedDataList,startTime,endTime);
        }
    }


    @Transactional
    public void dealRealtimeData(JSONArray wmDataArray) {
        // 伪满票务
        List<SyncOrderBaseDO> cachedDataList = new ArrayList<>();
        Date date = new Date();
        if (!wmDataArray.isEmpty()) {
            for (int i = 0; i < wmDataArray.size(); i++) {
                SyncOrderBaseDO orderBO = new SyncOrderBaseDO();
                JSONObject weiManJson = wmDataArray.getJSONObject(i);
                String cardName = weiManJson.getString("card_name");
                String cardNumber = weiManJson.getString("card_number");
                String courtyardDate = weiManJson.getString("courtyard_date");
                Byte cardType = weiManJson.getByte("card_type");
                Integer ticketSource = weiManJson.getInteger("ticket_source");
                String ticketType = weiManJson.getString("ticket_type");

                if(courtyardDate.length() == 10){
                    courtyardDate = courtyardDate + " 00:00:00";
                }
                if (StringUtils.isBlank(cardName)) {
                    cardName = "无";
                }
                if (StringUtils.isBlank(cardNumber)) {
                    cardNumber = "0000";
                }
                if(cardType == 4){
                    cardType = 5;
                }

                if (cardType == 1 && cardNumber.length() != 18) {
                    cardType = 4;
                }

                if(cardType == 0){
                    cardType = 1;
                }

                String str = "^[1-9]\\d{5}(18|19|20)?\\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\\d{3}([0-9]|X)$";
                if(cardType == 1){
                    if(!cardNumber.matches(str)){
                        cardType = 4;
                    }
                }

                if("67".equals(ticketType)){
                    // 东北沦陷(已废弃)
//                    orderBO.setUniqueCode("1702249354724773888");
//                    orderBO.setVenueName("东北沦陷史陈列馆");
//                    orderBO.setOrderNo(UniqueIDUtil.getUniqueID());
//                    orderBO.setTotalTicketCount(1L);
//                    orderBO.setTotalPeopleCount(1L);
//                    orderBO.setOrderType((byte)1);
//                    orderBO.setReserveType((byte)1);
//                    orderBO.setStatus((byte)2);
//                    orderBO.setTouristName(cardName);
//                    orderBO.setTouristCertificateType(cardType);
//                    orderBO.setTouristCertificateNo(cardNumber);
//                    orderBO.setReserveDate(DateUtils.parseDateYYYMMDDHHmmss(courtyardDate));
//                    orderBO.setGmtCreate(date);
//                    orderBO.setGmtModified(date);
//                    cachedDataList.add(orderBO);
                }else{
                    // 伪满皇宫
                    orderBO.setUniqueCode("1684468846498549760");
                    orderBO.setVenueName("伪满皇宫博物院");
                    orderBO.setOrderNo(UniqueIDUtil.getUniqueID());
                    orderBO.setTotalTicketCount(1L);
                    orderBO.setTotalPeopleCount(1L);
                    orderBO.setOrderType((byte)1);
                    orderBO.setReserveType((byte)1);
                    orderBO.setStatus((byte)2);
                    orderBO.setTouristName(cardName);
                    orderBO.setTouristCertificateType(cardType);
                    orderBO.setTouristCertificateNo(cardNumber);
                    orderBO.setReserveDate(DateUtils.parseDateYYYMMDDHHmmss(courtyardDate));
                    orderBO.setGmtCreate(date);
                    orderBO.setGmtModified(date);
                    cachedDataList.add(orderBO);
                }

                if(ticketSource == 11 && "1".equals(ticketType)){
                    // 长影
                    SyncOrderBaseDO cyOrder = new SyncOrderBaseDO();
                    cyOrder.setUniqueCode("1678320746423783424");
                    cyOrder.setVenueName("长影旧址博物馆");
                    cyOrder.setOrderNo(UniqueIDUtil.getUniqueID());
                    cyOrder.setTotalTicketCount(1L);
                    cyOrder.setTotalPeopleCount(1L);
                    cyOrder.setOrderType((byte)1);
                    cyOrder.setReserveType((byte)1);
                    cyOrder.setStatus((byte)2);
                    cyOrder.setTouristName(cardName);
                    cyOrder.setTouristCertificateType(cardType);
                    cyOrder.setTouristCertificateNo(cardNumber);
                    cyOrder.setReserveDate(DateUtils.parseDateYYYMMDDHHmmss(courtyardDate));
                    cyOrder.setGmtCreate(date);
                    cyOrder.setGmtModified(date);
                    cachedDataList.add(cyOrder);
                }
            }
        }
        if(!CollectionUtils.isEmpty(cachedDataList)){
            orderBaseOperate(cachedDataList);
        }
    }

    @Transactional
    public void dealHistoryData(JSONArray wmDataArray,Long startTime, Long endTime) {
        // 伪满票务
        List<SyncOrderBaseDO> cachedDataList = new ArrayList<>();

        Date date = new Date();
        if (!wmDataArray.isEmpty()) {
            for (int i = 0; i < wmDataArray.size(); i++) {
                SyncOrderBaseDO orderBO = new SyncOrderBaseDO();
                JSONObject weiManJson = wmDataArray.getJSONObject(i);
                String cardName = weiManJson.getString("card_name");
                String cardNumber = weiManJson.getString("card_number");
                String courtyardDate = weiManJson.getString("courtyard_date");
                Byte cardType = weiManJson.getByte("card_type");
                Integer ticketSource = weiManJson.getInteger("ticket_source");
                String ticketType = weiManJson.getString("ticket_type");

                if(courtyardDate.length() == 10){
                    courtyardDate = courtyardDate + " 00:00:00";
                }
                if (StringUtils.isBlank(cardName)) {
                    cardName = "无";
                }
                if (StringUtils.isBlank(cardNumber)) {
                    cardNumber = "0000";
                }
                if(cardType == 4){
                    cardType = 5;
                }

                if (cardType == 1 && cardNumber.length() != 18) {
                    cardType = 4;
                }

                String str = "^[1-9]\\d{5}(18|19|20)?\\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\\d{3}([0-9]|X)$";
                if(cardType == 1){
                    if(!cardNumber.matches(str)){
                        cardType = 4;
                    }
                }

                if("67".equals(ticketType)){
                    // 东北沦陷 （已废弃）
//                    orderBO.setUniqueCode("1702249354724773888");
//                    orderBO.setVenueName("东北沦陷史陈列馆");
                }else{
                    // 伪满皇宫
                    orderBO.setUniqueCode("1684468846498549760");
                    orderBO.setVenueName("伪满皇宫博物院");
                    orderBO.setOrderNo(UniqueIDUtil.getUniqueID());
                    orderBO.setTotalTicketCount(1L);
                    orderBO.setTotalPeopleCount(1L);
                    orderBO.setOrderType((byte)1);
                    orderBO.setReserveType((byte)1);
                    orderBO.setStatus((byte)2);
                    orderBO.setTouristName(cardName);
                    orderBO.setTouristCertificateType(cardType);
                    orderBO.setTouristCertificateNo(cardNumber);
                    orderBO.setReserveDate(DateUtils.parseDateYYYMMDDHHmmss(courtyardDate));
                    orderBO.setGmtCreate(date);
                    orderBO.setGmtModified(date);
                    cachedDataList.add(orderBO);
                }

                if(ticketSource == 11 && "1".equals(ticketType)){
                    SyncOrderBaseDO cyOrder = new SyncOrderBaseDO();
                    // 长影
                    cyOrder.setUniqueCode("1678320746423783424");
                    cyOrder.setVenueName("长影旧址博物馆");
                    cyOrder.setOrderNo(UniqueIDUtil.getUniqueID());
                    cyOrder.setTotalTicketCount(1L);
                    cyOrder.setTotalPeopleCount(1L);
                    cyOrder.setOrderType((byte)1);
                    cyOrder.setReserveType((byte)1);
                    cyOrder.setStatus((byte)2);
                    cyOrder.setTouristName(cardName);
                    cyOrder.setTouristCertificateType(cardType);
                    cyOrder.setTouristCertificateNo(cardNumber);
                    cyOrder.setReserveDate(DateUtils.parseDateYYYMMDDHHmmss(courtyardDate));
                    cyOrder.setGmtCreate(date);
                    cyOrder.setGmtModified(date);
                    cachedDataList.add(cyOrder);
                }
            }
        }
        if(!CollectionUtils.isEmpty(cachedDataList)){
            historyOrderBaseOperate(cachedDataList, startTime, endTime);
        }
    }

    /**
     * 东北沦陷票务数据入库操作
     */
    @Transactional
    public void lxOrderBaseOperate(List<SyncOrderBaseDO> cachedDataList,Long startTime, Long endTime){
        if(CollectionUtils.isEmpty(cachedDataList)){
            return;
        }
        // 删除时间段内已导入数据
        syncOrderBaseMapper.deleteLxByTime(new Date(startTime*1000),new Date(endTime*1000));
        //批量插入新数据
        int num = cachedDataList.size() / 100;
        List<SyncOrderBaseDO> insertList;
        for (int i = 0; i < num + 1; i++) {
            int endIndex = (i + 1) * 100;
            if (endIndex > cachedDataList.size()) {
                endIndex = cachedDataList.size();
            }
            insertList = cachedDataList.subList(i * 100, endIndex);
            if(org.apache.commons.collections.CollectionUtils.isNotEmpty(insertList)){
                syncOrderBaseMapper.batchInsert(insertList);
            }
        }
    }

    /**
     * 伪满今日观众数据入库操作
     */
    @Transactional
    public void orderBaseOperate(List<SyncOrderBaseDO> cachedDataList){
        if(CollectionUtils.isEmpty(cachedDataList)){
            return;
        }
        // 删除今日已导入数据
        syncOrderBaseMapper.deleteWmByTime(DateUtils.getTimesDayMorning(),new Date());
        //批量插入新数据
        int num = cachedDataList.size() / 100;
        List<SyncOrderBaseDO> insertList;
        for (int i = 0; i < num + 1; i++) {
            int endIndex = (i + 1) * 100;
            if (endIndex > cachedDataList.size()) {
                endIndex = cachedDataList.size();
            }
            insertList = cachedDataList.subList(i * 100, endIndex);
            if(org.apache.commons.collections.CollectionUtils.isNotEmpty(insertList)){
                syncOrderBaseMapper.batchInsert(insertList);
            }
        }
    }

    /**
     * 伪满历史观众数据入库操作
     */
    @Transactional
    public void historyOrderBaseOperate(List<SyncOrderBaseDO> cachedDataList,Long startTime, Long endTime){
        if(CollectionUtils.isEmpty(cachedDataList)){
            return;
        }
        // 删除该时间段已导入数据
        //syncOrderBaseMapper.deleteWmByTime(new Date(startTime*1000),new Date(endTime*1000));
        //批量插入新数据
        int num = cachedDataList.size() / 100;
        List<SyncOrderBaseDO> insertList;
        for (int i = 0; i < num + 1; i++) {
            int endIndex = (i + 1) * 100;
            if (endIndex > cachedDataList.size()) {
                endIndex = cachedDataList.size();
            }
            insertList = cachedDataList.subList(i * 100, endIndex);
            if(org.apache.commons.collections.CollectionUtils.isNotEmpty(insertList)){
                syncOrderBaseMapper.batchInsert(insertList);
            }
        }
    }


    @Data
    class GetTicketPersonBO {

        private Long start_time;

        private Long end_time;
    }

    public static void main(String[] args) {
        String str = "^[1-9]\\d{5}(18|19|20)?\\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\\d{3}([0-9]|X)$";
        if ("22018219950226552X".matches(str)){
            System.out.println("true");
        }else{
            System.out.println("false");
        }
    }

}
