package com.das.province.service.task;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.das.province.common.builder.HttpClientCustomizeBuilder;
import com.das.province.common.exception.CommonErrorCodeEnum;
import com.das.province.common.exception.CommonException;
import com.das.province.common.http.HttpConfig;
import com.das.province.common.http.HttpHeader;
import com.das.province.common.http.HttpMethods;
import com.das.province.common.http.HttpResult;
import com.das.province.common.utils.DateUtils;
import com.das.province.common.utils.HttpClientUtil;
import com.das.province.common.utils.UniqueIDUtil;
import com.das.province.dependency.bo.HttpResultBO;
import com.das.province.infr.dataobject.SyncOrderBaseDO;
import com.das.province.infr.mapper.SyncOrderBaseMapper;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpStatus;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 定时获取闸机观众
 */

@Slf4j
@Service
public class GetTicketPersonTask {

    @Resource(name = "provinceHttpClientCustomizeBuilder")
    private HttpClientCustomizeBuilder httpClientCustomizeBuilder;

    /**
     * 伪满闸机接口
     */
    private final String weiManTicketUrl = "https://index.wmhg.com.cn/api/get_id_card_list";


    @Resource
    private SyncOrderBaseMapper syncOrderBaseMapper;

    /**
     * 伪满皇宫观众数据获取入库
     */
    public void setWeiManTicketData(Long startTime, Long endTime) {
        GetTicketPersonBO getTicketPersonBO = new GetTicketPersonBO();
        getTicketPersonBO.setStart_time(startTime);
        getTicketPersonBO.setEnd_time(endTime);
        int num = 0;
        try {
            HttpConfig config = HttpConfig.custom()
                    .headers(HttpHeader.custom().build())
                    .url(weiManTicketUrl)
                    .encoding("utf-8")
                    .method(HttpMethods.POST)
                    .json(JSON.toJSONString(getTicketPersonBO))
                    .client(httpClientCustomizeBuilder.build());
            HttpResult result = HttpClientUtil.sendAndGetResp(config);
            if (HttpStatus.SC_OK == result.getStatusCode()) {
                HttpResultBO httpResultBO = JSON.parseObject(result.getResult(), HttpResultBO.class);
                if (StringUtils.isNotBlank(httpResultBO.getData())) {
                    JSONObject jsonObject = JSONObject.parseObject(httpResultBO.getData());

                    List<SyncOrderBaseDO> cachedDataList = new ArrayList<>();
                    //伪满皇宫
                    JSONArray wmDataArray = JSONArray.parseArray(jsonObject.getString("all"));
                    num += wmDataArray.size();
                    Date date = new Date();
                    if (wmDataArray.size() > 0) {
                        for (int i = 0; i < wmDataArray.size(); i++) {
                            SyncOrderBaseDO orderBO = new SyncOrderBaseDO();
                            JSONObject weiManJson = wmDataArray.getJSONObject(i);
                            String cardName = weiManJson.getString("card_name");
                            String cardNumber = weiManJson.getString("card_number");
                            String courtyardDate = weiManJson.getString("courtyard_date");
                            String cardType = weiManJson.getString("card_type");
                            if (StringUtils.isBlank(cardName)) {
                                cardName = "无";
                            }

                            if (StringUtils.isBlank(cardNumber)) {
                                cardNumber = "0000";
                            }

                            if (Byte.parseByte(cardType) == 1 && cardNumber.length() != 18) {
                                cardType = "4";
                            }
                            orderBO.setUniqueCode("1684468846498549760");
                            orderBO.setVenueName("伪满皇宫博物院");
                            orderBO.setOrderNo(UniqueIDUtil.getUniqueID());
                            orderBO.setTotalTicketCount(1L);
                            orderBO.setTotalPeopleCount(1L);
                            orderBO.setOrderType((byte)1);
                            orderBO.setReserveType((byte)1);
                            orderBO.setStatus((byte)2);
                            orderBO.setTouristName(cardName);
                            orderBO.setTouristCertificateType(Byte.parseByte(cardType));
                            orderBO.setTouristCertificateNo(cardNumber);
                            orderBO.setReserveDate(DateUtils.parseDateYYYMMDDHHmmss(courtyardDate));
                            orderBO.setGmtCreate(date);
                            orderBO.setGmtModified(date);
                            cachedDataList.add(orderBO);
                        }
                    }


                    // 东北沦陷史陈列馆
                    JSONArray lxDataArray = JSONArray.parseArray(jsonObject.getString("918"));
                    num += lxDataArray.size();
                    if (lxDataArray.size() > 0) {
                        for (int i = 0; i < lxDataArray.size(); i++) {
                            SyncOrderBaseDO orderBO = new SyncOrderBaseDO();
                            JSONObject weiManJson = lxDataArray.getJSONObject(i);
                            String cardName = weiManJson.getString("card_name");
                            String cardNumber = weiManJson.getString("card_number");
                            String courtyardDate = weiManJson.getString("courtyard_date");
                            String cardType = weiManJson.getString("card_type");

                            if (StringUtils.isBlank(cardName)) {
                                cardName = "无";
                            }

                            if (StringUtils.isBlank(cardNumber)) {
                                cardNumber = "0000";
                            }

                            if (Byte.parseByte(cardType) == 1 && cardNumber.length() != 18) {
                                cardType = "4";
                            }
                            orderBO.setUniqueCode("1702249354724773888");
                            orderBO.setVenueName("东北沦陷史陈列馆");
                            orderBO.setOrderNo(UniqueIDUtil.getUniqueID());
                            orderBO.setTotalTicketCount(1L);
                            orderBO.setTotalPeopleCount(1L);
                            orderBO.setOrderType((byte)1);
                            orderBO.setReserveType((byte)1);
                            orderBO.setStatus((byte)2);
                            orderBO.setTouristName(cardName);
                            orderBO.setTouristCertificateType(Byte.parseByte(cardType));
                            orderBO.setTouristCertificateNo(cardNumber);
                            orderBO.setReserveDate(DateUtils.parseDateYYYMMDDHHmmss(courtyardDate));
                            orderBO.setGmtCreate(date);
                            orderBO.setGmtModified(date);
                            cachedDataList.add(orderBO);
                        }
                    }

                    //长影旧址
                    JSONArray cyDataArray = JSONArray.parseArray(jsonObject.getString("union"));
                    num += cyDataArray.size();
                    if (cyDataArray.size() > 0) {
                        for (int i = 0; i < cyDataArray.size(); i++) {
                            SyncOrderBaseDO orderBO = new SyncOrderBaseDO();
                            JSONObject weiManJson = cyDataArray.getJSONObject(i);
                            String cardName = weiManJson.getString("card_name");
                            String cardNumber = weiManJson.getString("card_number");
                            String courtyardDate = weiManJson.getString("courtyard_date");
                            String cardType = weiManJson.getString("card_type");

                            if (StringUtils.isBlank(cardName)) {
                                cardName = "无";
                            }

                            if (StringUtils.isBlank(cardNumber)) {
                                cardNumber = "0000";
                            }

                            if (Byte.parseByte(cardType) == 1 && cardNumber.length() != 18) {
                                cardType = "4";
                            }

                            orderBO.setUniqueCode("1678320746423783424");
                            orderBO.setVenueName("长影旧址博物馆");
                            orderBO.setOrderNo(UniqueIDUtil.getUniqueID());
                            orderBO.setTotalTicketCount(1L);
                            orderBO.setTotalPeopleCount(1L);
                            orderBO.setOrderType((byte)1);
                            orderBO.setReserveType((byte)1);
                            orderBO.setStatus((byte)2);
                            orderBO.setTouristName(cardName);
                            orderBO.setTouristCertificateType(Byte.parseByte(cardType));
                            orderBO.setTouristCertificateNo(cardNumber);
                            orderBO.setReserveDate(DateUtils.parseDateYYYMMDDHHmmss(courtyardDate));
                            orderBO.setGmtCreate(date);
                            orderBO.setGmtModified(date);
                            cachedDataList.add(orderBO);
                        }
                    }
                    // 数据入库操作
                    if(!CollectionUtils.isEmpty(cachedDataList)){
                        orderBaseOperate(cachedDataList);
                    }
                }else {
                    throw new CommonException(CommonErrorCodeEnum.UNKNOWN_ERROR, "获取伪满皇宫闸机数据失败");
                }
            }
        } catch(CommonException e){
            throw new CommonException(CommonErrorCodeEnum.UNKNOWN_ERROR);
        }
        log.info(startTime+"--"+endTime+"从接口获取数据量：" + num);
    }

    /**
     * 伪满今日观众数据入库操作
     */
    @Transactional
    public void orderBaseOperate(List<SyncOrderBaseDO> cachedDataList){
        if(CollectionUtils.isEmpty(cachedDataList)){
            return;
        }
        // 删除今日已导入数据
        syncOrderBaseMapper.deleteWmByTime(DateUtils.getTimesDayMorning(),new Date());
        //批量插入新数据
        int num = cachedDataList.size() / 100;
        List<SyncOrderBaseDO> insertList;
        for (int i = 0; i < num + 1; i++) {
            int endIndex = (i + 1) * 100;
            if (endIndex > cachedDataList.size()) {
                endIndex = cachedDataList.size();
            }
            insertList = cachedDataList.subList(i * 100, endIndex);
            if(org.apache.commons.collections.CollectionUtils.isNotEmpty(insertList)){
                syncOrderBaseMapper.batchInsert(insertList);
            }
        }
    }


    /**
     * 伪满皇宫导入历史数据
     */
    public void setWeiManHistoryTicketData(Long startTime, Long endTime){
        GetTicketPersonBO getTicketPersonBO = new GetTicketPersonBO();
        getTicketPersonBO.setStart_time(startTime);
        getTicketPersonBO.setEnd_time(endTime);
        int num = 0;
        Date date = new Date();
        try {
            HttpConfig config = HttpConfig.custom()
                    .headers(HttpHeader.custom().build())
                    .url(weiManTicketUrl)
                    .encoding("utf-8")
                    .method(HttpMethods.POST)
                    .json(JSON.toJSONString(getTicketPersonBO))
                    .client(httpClientCustomizeBuilder.build());
            HttpResult result = HttpClientUtil.sendAndGetResp(config);
            if (HttpStatus.SC_OK == result.getStatusCode()) {
                HttpResultBO httpResultBO = JSON.parseObject(result.getResult(), HttpResultBO.class);
                if (StringUtils.isNotBlank(httpResultBO.getData())) {
                    JSONObject jsonObject = JSONObject.parseObject(httpResultBO.getData());

                    List<SyncOrderBaseDO> cachedDataList = new ArrayList<>();
                    //伪满皇宫
                    JSONArray wmDataArray = JSONArray.parseArray(jsonObject.getString("all"));
                    num += wmDataArray.size();
                    if (wmDataArray.size() > 0) {
                        for (int i = 0; i < wmDataArray.size(); i++) {
                            SyncOrderBaseDO orderBO = new SyncOrderBaseDO();
                            JSONObject weiManJson = wmDataArray.getJSONObject(i);
                            String cardName = weiManJson.getString("card_name");
                            String cardNumber = weiManJson.getString("card_number");
                            String courtyardDate = weiManJson.getString("courtyard_date");
                            String cardType = weiManJson.getString("card_type");
                            if (StringUtils.isBlank(cardName)) {
                                cardName = "无";
                            }

                            if (StringUtils.isBlank(cardNumber)) {
                                cardNumber = "0000";
                            }

                            if (Byte.parseByte(cardType) == 1 && cardNumber.length() != 18) {
                                cardType = "4";
                            }
                            orderBO.setUniqueCode("1684468846498549760");
                            orderBO.setVenueName("伪满皇宫博物院");
                            orderBO.setOrderNo(UniqueIDUtil.getUniqueID());
                            orderBO.setTotalTicketCount(1L);
                            orderBO.setTotalPeopleCount(1L);
                            orderBO.setOrderType((byte)1);
                            orderBO.setReserveType((byte)1);
                            orderBO.setStatus((byte)2);
                            orderBO.setTouristName(cardName);
                            orderBO.setTouristCertificateType(Byte.parseByte(cardType));
                            orderBO.setTouristCertificateNo(cardNumber);
                            orderBO.setReserveDate(DateUtils.parseDateYYYMMDDHHmmss(courtyardDate));
                            orderBO.setGmtCreate(date);
                            orderBO.setGmtModified(date);
                            cachedDataList.add(orderBO);
                        }
                    }


                    // 东北沦陷史陈列馆
                    JSONArray lxDataArray = JSONArray.parseArray(jsonObject.getString("918"));
                    num += lxDataArray.size();
                    if (lxDataArray.size() > 0) {
                        for (int i = 0; i < lxDataArray.size(); i++) {
                            SyncOrderBaseDO orderBO = new SyncOrderBaseDO();
                            JSONObject weiManJson = lxDataArray.getJSONObject(i);
                            String cardName = weiManJson.getString("card_name");
                            String cardNumber = weiManJson.getString("card_number");
                            String courtyardDate = weiManJson.getString("courtyard_date");
                            String cardType = weiManJson.getString("card_type");

                            if (StringUtils.isBlank(cardName)) {
                                cardName = "无";
                            }

                            if (StringUtils.isBlank(cardNumber)) {
                                cardNumber = "0000";
                            }

                            if (Byte.parseByte(cardType) == 1 && cardNumber.length() != 18) {
                                cardType = "4";
                            }
                            orderBO.setUniqueCode("1702249354724773888");
                            orderBO.setVenueName("东北沦陷史陈列馆");
                            orderBO.setOrderNo(UniqueIDUtil.getUniqueID());
                            orderBO.setTotalTicketCount(1L);
                            orderBO.setTotalPeopleCount(1L);
                            orderBO.setOrderType((byte)1);
                            orderBO.setReserveType((byte)1);
                            orderBO.setStatus((byte)2);
                            orderBO.setTouristName(cardName);
                            orderBO.setTouristCertificateType(Byte.parseByte(cardType));
                            orderBO.setTouristCertificateNo(cardNumber);
                            orderBO.setReserveDate(DateUtils.parseDateYYYMMDDHHmmss(courtyardDate));
                            orderBO.setGmtCreate(date);
                            orderBO.setGmtModified(date);
                            cachedDataList.add(orderBO);
                        }
                    }

                    //长影旧址
                    JSONArray cyDataArray = JSONArray.parseArray(jsonObject.getString("union"));
                    num += cyDataArray.size();
                    if (cyDataArray.size() > 0) {
                        for (int i = 0; i < cyDataArray.size(); i++) {
                            SyncOrderBaseDO orderBO = new SyncOrderBaseDO();
                            JSONObject weiManJson = cyDataArray.getJSONObject(i);
                            String cardName = weiManJson.getString("card_name");
                            String cardNumber = weiManJson.getString("card_number");
                            String courtyardDate = weiManJson.getString("courtyard_date");
                            String cardType = weiManJson.getString("card_type");

                            if (StringUtils.isBlank(cardName)) {
                                cardName = "无";
                            }

                            if (StringUtils.isBlank(cardNumber)) {
                                cardNumber = "0000";
                            }

                            if (Byte.parseByte(cardType) == 1 && cardNumber.length() != 18) {
                                cardType = "4";
                            }

                            orderBO.setUniqueCode("1678320746423783424");
                            orderBO.setVenueName("长影旧址博物馆");
                            orderBO.setOrderNo(UniqueIDUtil.getUniqueID());
                            orderBO.setTotalTicketCount(1L);
                            orderBO.setTotalPeopleCount(1L);
                            orderBO.setOrderType((byte)1);
                            orderBO.setReserveType((byte)1);
                            orderBO.setStatus((byte)2);
                            orderBO.setTouristName(cardName);
                            orderBO.setTouristCertificateType(Byte.parseByte(cardType));
                            orderBO.setTouristCertificateNo(cardNumber);
                            orderBO.setReserveDate(DateUtils.parseDateYYYMMDDHHmmss(courtyardDate));
                            orderBO.setGmtCreate(date);
                            orderBO.setGmtModified(date);
                            cachedDataList.add(orderBO);
                        }
                    }
                    // 数据入库操作
                    if(!CollectionUtils.isEmpty(cachedDataList)){
                        historyOrderBaseOperate(cachedDataList,startTime,endTime);
                    }
                }else {
                    throw new CommonException(CommonErrorCodeEnum.UNKNOWN_ERROR, "获取伪满皇宫闸机数据失败");
                }
            }
        } catch(CommonException e){
            throw new CommonException(CommonErrorCodeEnum.UNKNOWN_ERROR);
        }
        log.info("本次从接口获取数据量：" + num);
    }

    /**
     * 伪满历史观众数据入库操作
     */
    @Transactional
    public void historyOrderBaseOperate(List<SyncOrderBaseDO> cachedDataList,Long startTime, Long endTime){
        if(CollectionUtils.isEmpty(cachedDataList)){
            return;
        }
        // 删除该时间段已导入数据
        syncOrderBaseMapper.deleteWmByTime(new Date(startTime*1000),new Date(endTime*1000));
        //批量插入新数据
        int num = cachedDataList.size() / 100;
        List<SyncOrderBaseDO> insertList;
        for (int i = 0; i < num + 1; i++) {
            int endIndex = (i + 1) * 100;
            if (endIndex > cachedDataList.size()) {
                endIndex = cachedDataList.size();
            }
            insertList = cachedDataList.subList(i * 100, endIndex);
            if(org.apache.commons.collections.CollectionUtils.isNotEmpty(insertList)){
                syncOrderBaseMapper.batchInsert(insertList);
            }
        }
    }


    @Data
    class GetTicketPersonBO {

        private Long start_time;

        private Long end_time;
    }

}
