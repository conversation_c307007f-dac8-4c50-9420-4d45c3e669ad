package com.das.province.service.biz.user;

import com.das.province.common.bo.SimplePageInfo;
import com.das.province.common.exception.CommonErrorCodeEnum;
import com.das.province.common.exception.CommonException;
import com.das.province.common.utils.BeanCopyUtils;
import com.das.province.common.utils.SimplePageInfoUtils;
import com.das.province.infr.dataobject.AccPositionDO;
import com.das.province.infr.dataobject.AccUserDO;
import com.das.province.infr.dataobject.CommClassificationDO;
import com.das.province.infr.entity.PositionEntity;
import com.das.province.infr.mapper.AccPositionMapper;
import com.das.province.infr.mapper.AccUserMapper;
import com.das.province.infr.mapper.CommClassificationMapper;
import com.das.province.infr.repository.PositionRepository;
import com.das.province.service.biz.user.action.*;
import com.das.province.service.biz.user.dto.PositionDTO;
import com.das.province.service.biz.user.dto.PositionDepartmentDTO;
import com.das.province.service.biz.user.dto.PositionPageDTO;
import com.das.province.service.biz.user.dto.PositionUserDTO;
import com.das.province.service.enums.ClassificationBizTypeEnum;
import com.das.province.service.enums.DeleteStatusEnum;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@Service
public class PositionServiceImpl implements PositionService {

    @Resource
    private AccPositionMapper accPositionMapper;

    @Resource
    private PositionRepository positionRepository;

    @Resource
    private AccUserMapper accUserMapper;

    @Resource
    private CommClassificationMapper commClassificationMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long addPosition(AddPositionAction action) {
        Long companyId = action.getCompanyId();
        Long departmentId = action.getDepartmentId();
        AccPositionDO positionDO = this.accPositionMapper.selectByDepartmentId(companyId, departmentId, action.getPositionName(),
                DeleteStatusEnum.否.getCode().byteValue());
        if (Objects.nonNull(positionDO)) {
            throw new CommonException(CommonErrorCodeEnum.VALIDATE_ERROR, "该部门[" + positionDO.getPositionName() + "]职位已存在");
        }
        PositionEntity positionEntity = new PositionEntity();
        positionEntity.setCompanyId(companyId);
        positionEntity.setPositionName(action.getPositionName());
        positionEntity.setDepartmentId(departmentId);
        positionEntity.setIsDelete(DeleteStatusEnum.否.getCode().byteValue());
        positionEntity.setCreator(action.getCreator());
        positionEntity.setModifier(action.getModifier());
        this.positionRepository.savePositionEntity(positionEntity);
        return positionEntity.getPositionId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void editPositionById(EditPositionByIdAction action) {
        Long companyId = action.getCompanyId();
        String positionName = action.getPositionName();
        AccPositionDO currPositionDO = this.accPositionMapper.selectByPositionId(companyId, action.getPositionId(),
                DeleteStatusEnum.否.getCode().byteValue());
        if (Objects.isNull(currPositionDO)) {
            throw new CommonException(CommonErrorCodeEnum.VALIDATE_ERROR, "该[" + positionName + "]职位信息不存在");
        }
        Long departmentId = currPositionDO.getDepartmentId();
        AccPositionDO positionDO = this.accPositionMapper.selectByDepartmentId(companyId, departmentId, positionName,
                DeleteStatusEnum.否.getCode().byteValue());
        if (Objects.nonNull(positionDO) && !action.getPositionId().equals(positionDO.getId())) {
            throw new CommonException(CommonErrorCodeEnum.VALIDATE_ERROR, "该部门[" + positionName + "]职位已存在");
        }
        PositionEntity positionEntity = new PositionEntity();
        positionEntity.setPositionId(action.getPositionId());
        positionEntity.setPositionName(positionName);
        positionEntity.setModifier(action.getModifier());
        this.positionRepository.editPositionEntity(positionEntity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delPositionById(DelPositionByIdAction action) {
        Long companyId = action.getCompanyId();
        Long positionId = action.getPositionId();
        AccPositionDO currPositionDO = this.accPositionMapper.selectByPositionId(companyId, positionId, DeleteStatusEnum.否.getCode().byteValue());
        if (Objects.isNull(currPositionDO)) {
            throw new CommonException(CommonErrorCodeEnum.VALIDATE_ERROR, "该职位信息不存在");
        }
        List<AccUserDO> userDOList = this.accUserMapper.listByPositionId(companyId, positionId, DeleteStatusEnum.否.getCode().byteValue());
        if (CollectionUtils.isNotEmpty(userDOList)) {
            throw new CommonException(CommonErrorCodeEnum.VALIDATE_ERROR, "该职位下面存在用户信息, 不能删除");
        }
        PositionEntity positionEntity = new PositionEntity();
        positionEntity.setPositionId(positionId);
        positionEntity.setIsDelete(DeleteStatusEnum.是.getCode().byteValue());
        positionEntity.setModifier(action.getModifier());
        this.positionRepository.editPositionEntity(positionEntity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchDelPositionByIds(DelPositionByIdsAction action) {
        Long companyId = action.getCompanyId();
        List<Long> positionIds = action.getPositionIds();
        List<AccPositionDO> currPositionDOList = this.accPositionMapper.listByPositionIds(companyId, positionIds, DeleteStatusEnum.否.getCode().byteValue());
        if (CollectionUtils.isEmpty(currPositionDOList) || currPositionDOList.size() != positionIds.size()) {
            throw new CommonException(CommonErrorCodeEnum.VALIDATE_ERROR, "部分职位信息不存在");
        }
        List<AccUserDO> userDOList = this.accUserMapper.listByPositionIds(companyId, positionIds, DeleteStatusEnum.否.getCode().byteValue());
        if (CollectionUtils.isNotEmpty(userDOList)) {
            throw new CommonException(CommonErrorCodeEnum.VALIDATE_ERROR, "该职位下面存在用户信息, 不能删除");
        }
        AccPositionDO positionDO = new AccPositionDO();
        positionDO.setIsDelete(DeleteStatusEnum.是.getCode().byteValue());
        positionDO.setModifier(action.getModifier());
        this.accPositionMapper.updateByPositionIds(positionDO, companyId, positionIds);
    }

    @Override
    public SimplePageInfo<PositionPageDTO> queryPageByCondition(QueryPositionPageByConditionAction action) {
        Long companyId = action.getCompanyId();
        Long departmentId = action.getDepartmentId();
        if(Objects.isNull(departmentId)){
            CommClassificationDO commClassificationDO = commClassificationMapper.selectRootByCompanyIdAndType(companyId, ClassificationBizTypeEnum.DPTMT.getCode().byteValue(),null,null);
            if(Objects.isNull(commClassificationDO)){
                return new SimplePageInfo<>();
            }
            departmentId = commClassificationDO.getId();
        }
        List<CommClassificationDO> departmentList = this.commClassificationMapper.selectIncludeChildById(companyId, departmentId);
        if (CollectionUtils.isEmpty(departmentList)) {
            return new SimplePageInfo<>();
        }
        List<Long> departmentIds = departmentList.stream().map(CommClassificationDO::getId).collect(Collectors.toList());
        action.startPage();
        List<AccPositionDO> positionDOList = this.accPositionMapper.listByCondition(companyId, departmentIds, DeleteStatusEnum.否.getCode().byteValue());
        if (CollectionUtils.isEmpty(positionDOList)) {
            return new SimplePageInfo<>();
        }
        List<Long> positionIds = positionDOList.stream().map(AccPositionDO::getId).collect(Collectors.toList());
        List<AccUserDO> userDOList = this.accUserMapper.listByPositionIds(companyId, positionIds, DeleteStatusEnum.否.getCode().byteValue());
        Map<Long, Long> userCountMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(userDOList)) {
            userCountMap = userDOList.stream().collect(Collectors.groupingBy(AccUserDO::getPositionId, Collectors.counting()));
        }
        Map<Long, String> departmentMap = departmentList.stream().collect(Collectors.toMap(CommClassificationDO::getId,
                CommClassificationDO::getName, (key1, key2) -> key2));
        SimplePageInfo<PositionPageDTO> pageInfo = SimplePageInfoUtils
                .convertSimplePageInfoResult(positionDOList, PositionPageDTO.class);
        Map<Long, Long> finalUserCountMap = userCountMap;
        List<PositionPageDTO> pageDTOList = positionDOList.stream().map(positionDO -> {
            PositionPageDTO positionPageDTO = BeanCopyUtils.copyByJSON(positionDO, PositionPageDTO.class);
            positionPageDTO.setPositionId(positionDO.getId());
            positionPageDTO.setDepartmentName(departmentMap.get(positionDO.getDepartmentId()));
            positionPageDTO.setUserCount(finalUserCountMap.get(positionDO.getId()));
            return positionPageDTO;
        }).collect(Collectors.toList());
        pageInfo.setList(pageDTOList);
        return pageInfo;
    }

    @Override
    public PositionDTO queryByPositionId(Long companyId, Long positionId) {
        AccPositionDO positionDO = this.accPositionMapper.selectByPositionId(companyId, positionId, DeleteStatusEnum.否.getCode().byteValue());
        if (Objects.isNull(positionDO)) {
            return null;
        }
        CommClassificationDO departmentDO = this.commClassificationMapper.selectById(companyId, positionDO.getDepartmentId());
        if (Objects.isNull(departmentDO)) {
            return null;
        }
        List<AccUserDO> userDOList = this.accUserMapper.listByPositionId(companyId, positionId, DeleteStatusEnum.否.getCode().byteValue());
        PositionDTO positionDTO = BeanCopyUtils.copyByJSON(positionDO, PositionDTO.class);
        positionDTO.setDepartmentName(departmentDO.getName());
        if (CollectionUtils.isEmpty(userDOList)) {
            return positionDTO;
        }
        List<PositionUserDTO> positionUserList = userDOList.stream().map(userDO -> {
            PositionUserDTO positionUserDTO = BeanCopyUtils.copyByJSON(userDO, PositionUserDTO.class);
            positionUserDTO.setPositionId(positionId);
            positionUserDTO.setPositionName(positionDO.getPositionName());
            return positionUserDTO;
        }).collect(Collectors.toList());
        positionDTO.setPositionUserList(positionUserList);
        return positionDTO;
    }

    @Override
    public List<PositionDepartmentDTO> queryByPositionIds(Long companyId, List<Long> positionIds){
        List<AccPositionDO> accPositionDOList = this.accPositionMapper.listByPositionIds(companyId,positionIds,DeleteStatusEnum.否.getCode().byteValue());
        if(CollectionUtils.isEmpty(accPositionDOList)){
            return Lists.newArrayList();
        }
        List<Long> departmentIds = accPositionDOList.stream().map(AccPositionDO::getDepartmentId).collect(Collectors.toList());
        Map<Long,CommClassificationDO> departmentMap = new HashMap<>();
        List<CommClassificationDO> departmentDOList = this.commClassificationMapper.listByIds(companyId, departmentIds);
        if(CollectionUtils.isNotEmpty(departmentDOList)){
            for(CommClassificationDO commClassificationDO : departmentDOList){
                departmentMap.put(commClassificationDO.getId(),commClassificationDO);
            }
        }
        return accPositionDOList.stream().map(accPositionDO -> {
            PositionDepartmentDTO positionDepartmentDTO = new PositionDepartmentDTO();
            positionDepartmentDTO.setJobId(accPositionDO.getId());
            positionDepartmentDTO.setJobName(accPositionDO.getPositionName());
            positionDepartmentDTO.setDepartmentId(accPositionDO.getDepartmentId());
            if(departmentMap.containsKey(accPositionDO.getDepartmentId())){
                positionDepartmentDTO.setDepartmentName(departmentMap.get(accPositionDO.getDepartmentId()).getName());
            }
            return positionDepartmentDTO;
        }).collect(Collectors.toList());
    }

    @Override
    public List<PositionDTO> queryByDepartmentId(Long companyId, Long departmentId) {
        CommClassificationDO departmentDO = this.commClassificationMapper.selectById(companyId, departmentId);
        if (Objects.isNull(departmentDO)) {
            return Lists.newArrayList();
        }
        List<AccPositionDO> positionDOList = this.accPositionMapper.listByCondition(companyId, Lists.newArrayList(departmentId),
                DeleteStatusEnum.否.getCode().byteValue());
        if (CollectionUtils.isEmpty(positionDOList)) {
            return Lists.newArrayList();
        }
        return BeanCopyUtils.copyArrayByJSON(positionDOList, PositionDTO.class);
    }
}
