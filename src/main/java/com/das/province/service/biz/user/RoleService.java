package com.das.province.service.biz.user;

import com.das.province.service.biz.user.action.*;
import com.das.province.service.biz.user.dto.*;
import com.das.province.common.bo.SimplePageInfo;

import java.util.List;

public interface RoleService {
    Long addRole(AddRoleAction action);
    void editRoleById(EditRoleByIdAction action);
    void delRoleById(DelRoleByIdAction action);
    void batchDelRoleByIds(BatchDelRoleByIdsAction action);
    void enableRole(EnableRoleAction action);
    void batchEnableRole(BatchEnableRoleAction action);
    void disabledRole(DisabledRoleAction action);
    void batchDisabledRole(BatchDisabledRoleAction action);
    SimplePageInfo<RolePageDTO> queryPageByCondition(QueryRolePageByCondition action);
    RoleDTO queryByRoleId(Long companyId, Long roleId);
    List<RoleDTO> queryByRoleIds(Long companyId, List<Long> roleIds);
    RolePermissionDTO queryPermissionByRoleId(Long companyId, Long roleId);
    void roleAuthByRoleId(RoleAuthByRoleIdAction action);
    List<RoleDTO> listRole(Long companyId);
}
