package com.das.province.service.biz.collection.dto;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2023/02/09
 */
@Data
public class AudiencePortraitAreaDTO {

    /**
     * 个人入馆人数
     *
     */
    private Long inMuseumNum;

    /**
     * 省内
     */
    private Long provinceInNum;

    /**
     * 省内数量占比
     */
    private BigDecimal provinceInNumRatio;

    /**
     * 省内环比
     */
    private BigDecimal provinceInRingRatio;

    /**
     * 省内环比
     */
    private String provinceInRingRatioStr;

    /**
     * 省内同比
     */
    private BigDecimal provinceInYoy;

    /**
     * 省内同比
     */
    private String provinceInYoyStr;

    /**
     * 省外
     */
    private Long provinceOutNum;

    /**
     * 省外数量占比
     */
    private BigDecimal provinceOutNumRatio;

    /**
     * 省外环比
     */
    private BigDecimal provinceOutRingRatio;

    /**
     * 省外环比
     */
    private String provinceOutRingRatioStr;

    /**
     * 省外同比
     */
    private BigDecimal provinceOutYoy;

    /**
     * 省外同比
     */
    private String provinceOutYoyStr;

    /**
     * 境外
     */
    private Long overseasNum;

    /**
     * 境外数量占比
     */
    private BigDecimal overseasNumRatio;

    /**
     * 境外环比
     */
    private BigDecimal overseasRingRatio;

    /**
     * 境外环比
     */
    private String overseasRingRatioStr;

    /**
     * 境外同比
     */
    private BigDecimal overseasYoy;

    /**
     * 境外同比
     */
    private String overseasYoyStr;
}
