package com.das.province.service.biz.collection.dto;

import lombok.Data;

import java.math.BigDecimal;

/**
 * @Author: 1
 * @Date: 2023-01-05
 */
@Data
public class VisitorStatisticsDTO {

    /**
     * 总人数（观众总览）
     */
    private Long allTotalNum;

    /**
     * 总人数环比
     */
    private BigDecimal allTotalRingRatio;

    /**
     * 总人数环比
     */
    private String allTotalRingRatioStr;

    /**
     * 总人数同比
     */
    private BigDecimal allTotalYoY;

    /**
     * 总人数同比
     */
    private String allTotalYoyStr;

    /**
     * 入馆人数（线下）
     */
    private Long allInNum;

    /**
     * 入馆人数（线下）环比
     */
    private BigDecimal allInNumRingRatio;

    /**
     * 入馆人数（线下）环比
     */
    private String allInNumRingRatioStr;

    /**
     * 入馆人数（线下）同比
     */
    private BigDecimal allInNumYoY;

    /**
     * 入馆人数（线下）同比
     */
    private String allInNumYoyStr;

    /**
     * 入馆人数（线下个人）
     */
    private Long personalInNum;

    /**
     * 入馆团队个数（线下团队）
     */
    private Long teamInNum;

    /**
     * 入馆团队人数（线下团队）
     */
    private Long teamManInNum;

    /**
     * 入馆人数（线下）新增
     */
    private Long addMan;

    /**
     * 入馆人数（线上）
     */
    private Long allOnlineInNum;

    /**
     * 入馆人数（线上）环比
     */
    private BigDecimal allOnlineInNumRingRatio;

    /**
     * 入馆人数（线上）环比
     */
    private String allOnlineInNumRingRatioStr;

    /**
     * 入馆人数（线上）同比
     */
    private BigDecimal allOnlineInNumYoY;

    /**
     * 入馆人数（线上）同比
     */
    private String allOnlineInNumYoyStr;

}
