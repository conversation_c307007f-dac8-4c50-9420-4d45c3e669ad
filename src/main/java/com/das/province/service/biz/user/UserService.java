package com.das.province.service.biz.user;

import com.das.province.service.biz.common.dto.UserClassificationDTO;
import com.das.province.service.biz.user.action.*;
import com.das.province.service.biz.user.dto.*;
import com.das.province.service.enums.LoginClientTypeEnum;
import com.das.province.common.bo.SimplePageInfo;

import java.util.List;
import java.util.Map;


public interface UserService {
    Long addUser(AddUserAction addUserAction);
    void editUserByUserId(EditUserByUserIdAction editUserByUserIdAction);
    void delByUserId(DelByUserIdAction delByUserIdAction);
    void batchDelByUserIds(BatchDelByUserIdsAction action);
    void enableLoginAccount(Long companyId, Long userId, Long modifier);
    void batchEnableLoginAccount(Long companyId, List<Long> userIds, Long modifier);
    void disabledLoginAccount(Long companyId, Long userId, Long modifier);
    void batchDisabledLoginAccount(Long companyId, List<Long> userIds, Long modifier);
    SimplePageInfo<UserPageDTO> queryPageByCondition(QueryPageByConditionAction action);
    List<UserListDTO> queryUserList(Long companyId,String userName);
    UserDTO queryByLoginAccount(Long companyId, Long userId);
    UserInfoDTO queryLoginUserInfo(Long companyId, Long userId);
    UserDTO queryByLoginAccount(Long companyId, String loginAccount, LoginClientTypeEnum loginClientType);
    Boolean verifyByLoginAccount(Long companyId, String loginAccount, LoginClientTypeEnum loginClientType);
    UserDTO queryUserByLoginAccount(QueryUserByLoginAccountAction action);
    List<RoleDTO> queryUserNoHaveRoleList(Long companyId,Long copyUserId,Long userId);
    List<RoleDTO> queryUserHaveRoleList(Long companyId,Long userId);
    void assignRoleByUserId(Long companyId,Long userId,List<Long> roleIds,Long modifier);
    void batchAssignRoleByUserId(Long companyId,List<Long> userIds,List<Long> roleIds,Long modifier);
    UserJobCountDTO userJobCount(Long companyId);
    List<UserStatusCountDTO> userStatusCount(Long companyId);
    List<UserDeptCountDTO> userDeptCount(Long companyId);
    List<UserPermissionDTO> queryUserPermissionList(Long companyId,Long userId);
    List<UserPermissionButtonDTO> queryUserPermissionButtonList(Long companyId,Long userId);
    Boolean checkUsersRole(Long companyId,List<Long> userIds);
    Map<Long, String> queryUserNameMap(Long companyId, List<Long> userIds);

    /**
     * 用户修改密码
     */
    void updatePassword(Long companyId, Long userId, String oldPassword, String newPassword, String confirmNewPassword);

    /**
     * 用户修改头像
     */
    void updateAvatar(Long companyId, Long userId, Long documentId);

    UserClassificationDTO queryUserTree(String userName);
}
