package com.das.province.service.biz.safe;

import com.das.province.common.bo.SimplePageInfo;
import com.das.province.service.biz.safe.dto.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/7/12
 */
public interface DeviceMonitorExceptionService {

    DeviceMonitorExceptionInfoDTO getDeviceMonitorExceptionInfo(String museumId, String regionCode, String startTime, String endTime, Integer statisticsWay);

    List<DeviceMonitorExceptionTrend> getDeviceMonitorExceptionTrend(String museumId, String regionCode, String startTime, String endTime, Integer statisticsWay);

    SimplePageInfo<DeviceMonitorThExceptionDTO> pageDeviceMonitorThException(String museumId, String regionCode, String regionName, String deviceAddress, String exceptionType,
                                                                             String exceptionDegree, String startTime, String endTime, Integer statisticsWay, Integer pageNum, Integer pageSize);

    MonitorThExceptionInfoDTO getMonitorThExceptionInfo(String museumId, String regionCode, String regionName, String deviceAddress, String exceptionType,
                                                           String exceptionDegree, String startTime, String endTime, Integer statisticsWay);

    SimplePageInfo<MonitorOfflineExceptionDTO> pageMonitorOfflineExceptionList(String museumId, String regionCode, String regionName, String deviceAddress, String exceptionDegree,
                                                                              String startTime, String endTime, Integer statisticsWay, Integer pageNum, Integer pageSize);

    MonitorOfflineExceptionInfoDTO getMonitorOfflineExceptionInfo(String museumId, String regionCode, String regionName, String deviceAddress, String exceptionDegree,
                                                                  String startTime, String endTime, Integer statisticsWay);

    List<DeviceMonitorThExceptionDTO> monitorThExceptionExport(String museumId, String regionCode, String regionName, String deviceAddress, String exceptionType,
                                                                             String exceptionDegree, String startTime, String endTime, Integer statisticsWay);

    List<MonitorOfflineExceptionDTO> monitorOfflineExceptionExport(String museumId, String regionCode, String regionName, String deviceAddress, String exceptionDegree,
                                                                               String startTime, String endTime, Integer statisticsWay);
}
