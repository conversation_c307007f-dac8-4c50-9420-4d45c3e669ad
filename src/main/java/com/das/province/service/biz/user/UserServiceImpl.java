package com.das.province.service.biz.user;

import cn.dev33.satoken.secure.SaSecureUtil;
import cn.dev33.satoken.stp.StpUtil;
import com.das.province.common.bo.SimplePageInfo;
import com.das.province.common.exception.CommonErrorCodeEnum;
import com.das.province.common.exception.CommonException;
import com.das.province.common.utils.BeanCopyUtils;
import com.das.province.common.utils.RegexUtil;
import com.das.province.common.utils.SimplePageInfoUtils;
import com.das.province.infr.dataobject.*;
import com.das.province.infr.entity.UserEntity;
import com.das.province.infr.mapper.*;
import com.das.province.infr.repository.UserRepository;
import com.das.province.service.biz.common.dto.ClassificationDTO;
import com.das.province.service.biz.common.dto.UserClassificationDTO;
import com.das.province.service.biz.user.action.*;
import com.das.province.service.biz.user.dto.*;
import com.das.province.service.enums.*;
import com.das.province.web.controller.bo.LoginUserInfoBO;
import com.das.province.web.controller.utils.LoginUserInfoUtils;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class UserServiceImpl implements UserService {

    @Resource
    private UserRepository userRepository;

    @Resource
    private AccUserMapper accUserMapper;

    @Resource
    private AccRoleMapper accRoleMapper;

    @Resource
    private CommClassificationMapper commClassificationMapper;

    @Resource
    private AccPositionMapper accPositionMapper;

    @Resource
    private AccUserRoleRelMapper accUserRoleRelMapper;

    @Resource
    private AccRolePermissionRelMapper accRolePermissionRelMapper;

    @Resource
    private AccPermissionMapper accPermissionMapper;

    @Resource
    private RoleService roleService;

    @Value("${md5.salt}")
    private String salt;

    @Value("${aes.passkey}")
    private String passkey;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long addUser(AddUserAction addUserAction) {
        Long companyId = addUserAction.getCompanyId();
        if (this.verifyByLoginAccount(companyId, addUserAction.getLoginAccount(), LoginClientTypeEnum.PROVINCEPC)) {
            throw new CommonException(CommonErrorCodeEnum.VALIDATE_ERROR, "用户登录账号[" + addUserAction.getLoginAccount() + "]已存在");
        }
        UserEntity userEntity = BeanCopyUtils.copyByJSON(addUserAction, UserEntity.class);
        String password = addUserAction.getPassword();
        if (!RegexUtil.isValid(password)) {
            throw new CommonException(CommonErrorCodeEnum.VALIDATE_ERROR, "密码需要包含数字、大写字母、小写字母、特殊字符且大于8位！");
        }
        userEntity.setUserType(LoginClientTypeEnum.PROVINCEPC.getCode());
        userEntity.setPassword(SaSecureUtil.md5BySalt(password, salt));
        userEntity.setSourcePassword(SaSecureUtil.aesEncrypt(passkey, password));
        if (Objects.nonNull(addUserAction.getPostCode())) {
            userEntity.setPostCode(addUserAction.getPostCode().getCode());
        }
        userEntity.setIsDelete(DeleteStatusEnum.否.getCode().byteValue());
        userEntity.setAccountStatus(AccountStatusEnum.启用.getCode().byteValue());
        userEntity.setLeaderId(addUserAction.getLeaderId());
        this.userRepository.saveUserEntity(userEntity);
        List<Long> roleIds = addUserAction.getRoleIds();
        if (CollectionUtils.isNotEmpty(roleIds)) {
            List<Long> userIds = Lists.newArrayList();
            userIds.add(userEntity.getUserId());
            batchAssignRoleByUserId(companyId,userIds,roleIds,addUserAction.getCreator());
        }
        return userEntity.getUserId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void editUserByUserId(EditUserByUserIdAction editUserByUserIdAction) {
        Long companyId = editUserByUserIdAction.getCompanyId();
        Long userId = editUserByUserIdAction.getUserId();
        String password = editUserByUserIdAction.getPassword();
        AccUserDO userDO = this.accUserMapper.selectByUserId(companyId, userId);
        if (Objects.isNull(userDO)) {
            throw new CommonException(CommonErrorCodeEnum.VALIDATE_ERROR, "用户[" + editUserByUserIdAction.getUserName() + "]不存在");
        }
        if (!RegexUtil.isValid(password)) {
            throw new CommonException(CommonErrorCodeEnum.VALIDATE_ERROR, "密码需要包含数字、大写字母、小写字母、特殊字符且大于8位！");
        }
        UserEntity userEntity = BeanCopyUtils.copyByJSON(userDO, UserEntity.class);
        userEntity.setUserId(editUserByUserIdAction.getUserId());
        userEntity.setUserName(editUserByUserIdAction.getUserName());
        userEntity.setPhone(editUserByUserIdAction.getPhone());
        userEntity.setDepartmentId(editUserByUserIdAction.getDepartmentId());
        userEntity.setPositionId(editUserByUserIdAction.getPositionId());
        if (Objects.nonNull(editUserByUserIdAction.getPostCode())) {
            userEntity.setPostCode(editUserByUserIdAction.getPostCode().getCode());
        }
        userEntity.setModifier(editUserByUserIdAction.getModifier());
        userEntity.setPassword(SaSecureUtil.md5BySalt(password, salt));
        userEntity.setSourcePassword(SaSecureUtil.aesEncrypt(passkey, password));
        userEntity.setLeaderId(editUserByUserIdAction.getLeaderId());
        this.userRepository.editUserEntity(userEntity);
        List<Long> roleIds = editUserByUserIdAction.getRoleIds();
        if (CollectionUtils.isNotEmpty(roleIds)) {
            List<Long> userIds = Lists.newArrayList();
            userIds.add(userEntity.getUserId());
            batchAssignRoleByUserId(companyId,userIds,roleIds,editUserByUserIdAction.getModifier());
        }
    }

    @Override
    public void updatePassword(Long companyId, Long userId, String oldPassword, String newPassword, String confirmNewPassword){
        AccUserDO userDO = this.accUserMapper.selectByUserId(companyId, userId);
        if (Objects.isNull(userDO)) {
            throw new CommonException(CommonErrorCodeEnum.VALIDATE_ERROR, "用户不存在");
        }
        // 验证旧密码是否正确
        String md5OldPassword = SaSecureUtil.md5BySalt(oldPassword, salt);
        if(!md5OldPassword.equals(userDO.getPassword())){
            throw new CommonException(CommonErrorCodeEnum.VALIDATE_ERROR, "旧密码错误！");
        }

        if (!RegexUtil.isValid(newPassword)) {
            throw new CommonException(CommonErrorCodeEnum.VALIDATE_ERROR, "新密码需要包含数字、大写字母、小写字母、特殊字符且大于8位！");
        }

        if(!confirmNewPassword.equals(newPassword)){
            throw new CommonException(CommonErrorCodeEnum.VALIDATE_ERROR, "确认新密码错误！");
        }
        AccUserDO accUserDO = new AccUserDO();
        accUserDO.setCompanyId(companyId);
        accUserDO.setId(userId);
        accUserDO.setPassword(SaSecureUtil.md5BySalt(newPassword, salt));
        accUserDO.setSourcePassword(SaSecureUtil.aesEncrypt(passkey, newPassword));
        accUserDO.setCreator(userId);
        this.accUserMapper.updateByIdAndCompanyId(accUserDO);
    }

    @Override
    public void updateAvatar(Long companyId, Long userId, Long documentId){
        AccUserDO accUserDO = new AccUserDO();
        accUserDO.setCompanyId(companyId);
        accUserDO.setId(userId);
        accUserDO.setAvatar(documentId);
        this.accUserMapper.updateByIdAndCompanyId(accUserDO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delByUserId(DelByUserIdAction delByUserIdAction) {
        Long companyId = delByUserIdAction.getCompanyId();
        Long userId = delByUserIdAction.getUserId();
        AccUserDO userDO = this.accUserMapper.selectByUserId(companyId, userId);
        if (Objects.isNull(userDO)) {
            throw new CommonException(CommonErrorCodeEnum.VALIDATE_ERROR, "用户不存在");
        }
        userDO.setIsDelete(DeleteStatusEnum.是.getCode().byteValue());
        userDO.setAccountStatus(AccountStatusEnum.禁用.getCode().byteValue());
        userDO.setModifier(delByUserIdAction.getModifier());
        this.accUserMapper.updateByPrimaryKeySelective(userDO);
        List<Long> userIds = new ArrayList<>();
        userIds.add(userId);
        this.accUserRoleRelMapper.batchDeleteUserRole(companyId, userIds);
        StpUtil.logout(userId, "PC");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchDelByUserIds(BatchDelByUserIdsAction action) {
        Long companyId = action.getCompanyId();
        List<Long> userIds = action.getUserIds();
        List<AccUserDO> userDOList = this.accUserMapper.listByUserId(companyId, userIds);
        if (CollectionUtils.isEmpty(userDOList) || userDOList.size() != userIds.size()) {
            throw new CommonException(CommonErrorCodeEnum.VALIDATE_ERROR, "用户信息不存在");
        }
        AccUserDO userDO = new AccUserDO();
        userDO.setCompanyId(companyId);
        userDO.setAccountStatus(AccountStatusEnum.禁用.getCode().byteValue());
        userDO.setIsDelete(DeleteStatusEnum.是.getCode().byteValue());
        userDO.setModifier(action.getModifier());
        this.accUserMapper.updateByUserIds(userDO, userIds);
        userIds.forEach(userId -> StpUtil.logout(userId, "PC"));
    }

    @Override
    public void enableLoginAccount(Long companyId, Long userId, Long modifier) {
        AccUserDO userDO = this.accUserMapper.selectByUserId(companyId, userId);
        if (Objects.isNull(userDO)) {
            throw new CommonException(CommonErrorCodeEnum.VALIDATE_ERROR, "用户不存在");
        }
        userDO.setAccountStatus(AccountStatusEnum.启用.getCode().byteValue());
        userDO.setModifier(modifier);
        this.accUserMapper.updateByPrimaryKeySelective(userDO);
    }

    @Override
    public void batchEnableLoginAccount(Long companyId, List<Long> userIds, Long modifier) {
        List<AccUserDO> userDOList = this.accUserMapper.listByUserId(companyId, userIds);
        if (CollectionUtils.isEmpty(userDOList) || userDOList.size() != userIds.size()) {
            throw new CommonException(CommonErrorCodeEnum.VALIDATE_ERROR, "用户信息不存在");
        }
        long disabledUserCount = userDOList.stream()
                .filter(accUserDO -> AccountStatusEnum.禁用.getCode().byteValue() == accUserDO.getAccountStatus()).count();
        if (userIds.size() != disabledUserCount) {
            throw new CommonException(CommonErrorCodeEnum.VALIDATE_ERROR, "存在账号状态为[启用]的用户");
        }
        AccUserDO userDO = new AccUserDO();
        userDO.setCompanyId(companyId);
        userDO.setAccountStatus(AccountStatusEnum.启用.getCode().byteValue());
        userDO.setModifier(modifier);
        this.accUserMapper.updateByUserIds(userDO, userIds);
    }

    @Override
    public void disabledLoginAccount(Long companyId, Long userId, Long modifier) {
        AccUserDO userDO = this.accUserMapper.selectByUserId(companyId, userId);
        if (Objects.isNull(userDO)) {
            throw new CommonException(CommonErrorCodeEnum.VALIDATE_ERROR, "用户不存在");
        }
        userDO.setAccountStatus(AccountStatusEnum.禁用.getCode().byteValue());
        userDO.setModifier(modifier);
        this.accUserMapper.updateByPrimaryKeySelective(userDO);
        StpUtil.kickout(userId, "PC");
    }

    @Override
    public void batchDisabledLoginAccount(Long companyId, List<Long> userIds, Long modifier) {
        List<AccUserDO> userDOList = this.accUserMapper.listByUserId(companyId, userIds);
        if (CollectionUtils.isEmpty(userDOList) || userDOList.size() != userIds.size()) {
            throw new CommonException(CommonErrorCodeEnum.VALIDATE_ERROR, "用户信息不存在");
        }
        long disabledUserCount = userDOList.stream()
                .filter(accUserDO -> AccountStatusEnum.启用.getCode().byteValue() == accUserDO.getAccountStatus()).count();
        if (userIds.size() != disabledUserCount) {
            throw new CommonException(CommonErrorCodeEnum.VALIDATE_ERROR, "存在账号状态为[禁用]的用户");
        }
        AccUserDO userDO = new AccUserDO();
        userDO.setCompanyId(companyId);
        userDO.setAccountStatus(AccountStatusEnum.禁用.getCode().byteValue());
        userDO.setModifier(modifier);
        this.accUserMapper.updateByUserIds(userDO, userIds);
        userIds.forEach(userId -> StpUtil.kickout(userId, "PC"));
    }

    @Override
    public SimplePageInfo<UserPageDTO> queryPageByCondition(QueryPageByConditionAction action) {
        Long companyId = action.getCompanyId();
        Long departmentId =  action.getDepartmentId();

        if(Objects.isNull(departmentId)){
            CommClassificationDO commClassificationDO = commClassificationMapper.selectRootByCompanyIdAndType(companyId,ClassificationBizTypeEnum.DPTMT.getCode().byteValue(),null,null);
            if(Objects.isNull(commClassificationDO)){
                return new SimplePageInfo<>();
            }
            departmentId = commClassificationDO.getId();
        }
        List<CommClassificationDO> departmentList = this.commClassificationMapper.selectIncludeChildById(companyId, departmentId);
        if (CollectionUtils.isEmpty(departmentList)) {
            return new SimplePageInfo<>();
        }
        List<Long> ids = departmentList.stream().map(CommClassificationDO::getId).collect(Collectors.toList());

        action.startPage();
        List<AccUserDO> userList = this.accUserMapper.listByCondition(companyId, ids,
                action.getAccountStatus(), action.getQueryContent(), action.getSortBy(), DeleteStatusEnum.否.getCode().byteValue());
        if (CollectionUtils.isEmpty(userList)) {
            return new SimplePageInfo<>();
        }
        SimplePageInfo<UserPageDTO> pageInfo = SimplePageInfoUtils
                .convertSimplePageInfoResult(userList, UserPageDTO.class);
        List<Long> departmentIds = userList.stream().map(AccUserDO::getDepartmentId).collect(Collectors.toList());
        List<CommClassificationDO> departmentDOList = this.commClassificationMapper.listByIds(companyId, departmentIds);
        Map<Long, String> departmentMap = departmentDOList.stream().collect(Collectors.toMap(CommClassificationDO::getId,
                CommClassificationDO::getName, (key1, key2) -> key2));
        List<AccRoleDO> roleDOList = this.accRoleMapper.selectRoleList(companyId);
        Map<Long, String> roleNameMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(roleDOList)) {
            roleNameMap = roleDOList.stream().collect(Collectors.toMap(AccRoleDO::getId,
                    AccRoleDO::getRoleName, (key1, key2) -> key2));
        }
        List<AccUserRoleRelDO> userRoleRel = this.accUserRoleRelMapper.listByCompanyId(companyId);
        Map<Long, List<Long>> userRoleIdMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(userRoleRel)) {
            userRoleRel.forEach(k -> {
                if (userRoleIdMap.containsKey(k.getUserId())) {
                    userRoleIdMap.get(k.getUserId()).add(k.getRoleId());
                } else {
                    List<Long> roleIds = Lists.newArrayList(k.getRoleId());
                    userRoleIdMap.put(k.getUserId(), roleIds);
                }
            });
        }
        Map<Long, String> finalRoleNameMap = roleNameMap;
        List<UserPageDTO> userPageDTOList = userList.stream().map(accUserDO -> {
            UserPageDTO userPageDTO = BeanCopyUtils.copyByJSON(accUserDO, UserPageDTO.class);
            userPageDTO.setUserId(accUserDO.getId());
            if (Objects.nonNull(accUserDO.getPostCode())) {
                userPageDTO.setPostName(PostCodeEnum.fromCode(accUserDO.getPostCode()).getDesc());
            }
            userPageDTO.setDepartmentName(departmentMap.get(accUserDO.getDepartmentId()));
            if (userRoleIdMap.containsKey(accUserDO.getId())) {
                List<Long> roleIds = userRoleIdMap.get(accUserDO.getId());
                userPageDTO.setRoleIds(roleIds);
                if (CollectionUtils.isNotEmpty(roleIds)) {
                    List<String> roleNames = Lists.newArrayList();
                    roleIds.forEach(r -> roleNames.add(finalRoleNameMap.get(r)));
                    userPageDTO.setRoleName(roleNames);
                } else {
                    userPageDTO.setRoleName(Lists.newArrayList());
                }

            } else {
                userPageDTO.setRoleIds(Lists.newArrayList());
                userPageDTO.setRoleName(Lists.newArrayList());
            }
            return userPageDTO;
        }).collect(Collectors.toList());
        pageInfo.setList(userPageDTOList);
        return pageInfo;
    }

    @Override
    public List<UserListDTO> queryUserList(Long companyId,String userName){
        List<AccUserDO> userList = this.accUserMapper.listByConditionNoPage(companyId, userName, DeleteStatusEnum.否.getCode().byteValue());
        if(CollectionUtils.isEmpty(userList)){
            return Lists.newArrayList();
        }
        List<Long> departmentIds = userList.stream().map(AccUserDO::getDepartmentId).collect(Collectors.toList());
        List<CommClassificationDO> departmentDOList = this.commClassificationMapper.listByIds(companyId, departmentIds);
        Map<Long, String> departmentMap = departmentDOList.stream().collect(Collectors.toMap(CommClassificationDO::getId,
                CommClassificationDO::getName, (key1, key2) -> key2));

        return userList.stream().map(accUserDO -> {
            UserListDTO userListDTO = new UserListDTO();
            userListDTO.setUserId(accUserDO.getId());
            userListDTO.setUserName(accUserDO.getUserName());
            if(departmentMap.containsKey(accUserDO.getDepartmentId())){
                userListDTO.setDeptName(departmentMap.get(accUserDO.getDepartmentId()));
                userListDTO.setUserAndDeptName(accUserDO.getUserName()+"-"+userListDTO.getDeptName());
            }
            return userListDTO;
        }).collect(Collectors.toList());

    }

    @Override
    public UserDTO queryByLoginAccount(Long companyId, Long userId) {
        AccUserDO userDO = this.accUserMapper.selectByUserId(companyId, userId);
        if (Objects.isNull(userDO)) {
            throw new CommonException(CommonErrorCodeEnum.VALIDATE_ERROR, "用户不存在");
        }
        UserDTO userDto = BeanCopyUtils.copyByJSON(userDO, UserDTO.class);
        userDto.setUserId(userDO.getId());
        userDto.setPassword(SaSecureUtil.aesDecrypt(passkey, userDO.getSourcePassword()));
        List<AccUserRoleRelDO> roleRel = this.accUserRoleRelMapper.listByUserId(companyId, userId);
        if (CollectionUtils.isNotEmpty(roleRel)) {
            List<Long> roleIds = roleRel.stream().map(AccUserRoleRelDO::getRoleId).collect(Collectors.toList());
            userDto.setRoleIds(roleIds);
            List<AccRoleDO> roleDOList = this.accRoleMapper.listByRoleIds(companyId, roleIds, DeleteStatusEnum.否.getCode().byteValue());
            userDto.setRoleName(roleDOList.stream().map(AccRoleDO::getRoleName).collect(Collectors.toList()));
        } else {
            userDto.setRoleIds(Lists.newArrayList());
            userDto.setRoleName(Lists.newArrayList());
        }
        if (Objects.nonNull(userDO.getLeaderId())) {
            AccUserDO leaderUserDO = this.accUserMapper.selectByUserId(companyId, userDO.getLeaderId());
            if (Objects.nonNull(leaderUserDO)) {
                userDto.setLeaderName(leaderUserDO.getUserName());
            }
        }
        return userDto;
    }

    @Override
    public UserInfoDTO queryLoginUserInfo(Long companyId, Long userId){
        AccUserDO userDO = this.accUserMapper.selectByUserId(companyId, userId);
        if (Objects.isNull(userDO)) {
            throw new CommonException(CommonErrorCodeEnum.VALIDATE_ERROR, "用户不存在");
        }
        UserInfoDTO userInfoDTO = BeanCopyUtils.copyByJSON(userDO, UserInfoDTO.class);
        if(userInfoDTO.getAvatar() != null){
            /*CommDocumentDO commDocumentDO = commDocumentMapper.selectById(companyId,userInfoDTO.getAvatar());
            if(Objects.nonNull(commDocumentDO)) {
                userInfoDTO.setAvatarUrl(commDocumentDO.getUrl());
            }*/
        }
        return userInfoDTO;
    }

    @Override
    public UserDTO queryByLoginAccount(Long companyId, String loginAccount, LoginClientTypeEnum loginClientType) {
        AccUserDO userDO = this.accUserMapper.selectByLoginAccount(companyId, loginAccount, loginClientType.getCode());
        if (Objects.isNull(userDO)) {
           return null;
        }
        UserDTO userDto = BeanCopyUtils.copyByJSON(userDO, UserDTO.class);
        userDto.setPassword(SaSecureUtil.aesDecrypt(passkey, userDO.getSourcePassword()));
        return userDto;
    }

    @Override
    public Boolean verifyByLoginAccount(Long companyId, String loginAccount, LoginClientTypeEnum loginClientType) {
        AccUserDO userDO = this.accUserMapper.selectByLoginAccountAndIsDelete(loginAccount, DeleteStatusEnum.否.getCode().byteValue());
        return Objects.nonNull(userDO);
    }

    @Override
    public UserDTO queryUserByLoginAccount(QueryUserByLoginAccountAction action) {
        String loginAccount = action.getLoginAccount();
        String password = action.getPassword();
        AccUserDO accUserDO = this.accUserMapper.selectByLoginAccountAndPassword(loginAccount, SaSecureUtil.md5BySalt(password, salt),
                DeleteStatusEnum.否.getCode().byteValue(), AccountStatusEnum.启用.getCode().byteValue(), action.getLoginClientType().getCode());
        if (Objects.isNull(accUserDO)) {
            return null;
        }
        UserDTO userDTO = BeanCopyUtils.copyByJSON(accUserDO, UserDTO.class);
        userDTO.setUserId(accUserDO.getId());
        return userDTO;
    }

    @Override
    public List<RoleDTO> queryUserNoHaveRoleList(Long companyId, Long copyUserId, Long userId){
        List<AccUserRoleRelDO> userHaveRoleList =  this.accUserRoleRelMapper.listByUserId(companyId,userId);
        List<Long> userHaveRoleIdList = userHaveRoleList.stream().map(AccUserRoleRelDO::getRoleId).collect(Collectors.toList());
        List<RoleDTO> roleList = Lists.newArrayList();
        if(Objects.isNull(copyUserId)){
            roleList = roleService.listRole(companyId);
        }else{
            List<AccUserRoleRelDO> copyUserHaveRoleList =  this.accUserRoleRelMapper.listByUserId(companyId,copyUserId);
            if(CollectionUtils.isNotEmpty(copyUserHaveRoleList)){
                roleList = roleService.queryByRoleIds(companyId,copyUserHaveRoleList.stream().map(AccUserRoleRelDO::getRoleId).collect(Collectors.toList()));
            }
        }
        List<RoleDTO> resultList = Lists.newArrayList();
        for(RoleDTO roleDTO : roleList){
            if(!userHaveRoleIdList.contains(roleDTO.getRoleId())){
                resultList.add(roleDTO);
            }
        }
        return resultList;
    }

    @Override
    public List<RoleDTO> queryUserHaveRoleList(Long companyId,Long userId){
        List<AccUserRoleRelDO> userHaveRoleList =  this.accUserRoleRelMapper.listByUserId(companyId,userId);
        if(CollectionUtils.isEmpty(userHaveRoleList)){
            return Lists.newArrayList();
        }
        List<Long> roleIds = userHaveRoleList.stream().map(AccUserRoleRelDO::getRoleId).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(roleIds)){
            return Lists.newArrayList();
        }
        //获取角色状态，只保留 未删除状态和启动状态的角色
        List<AccRoleDO> accRoleDOList = accRoleMapper.listByRoleIds(companyId,roleIds,DeleteStatusEnum.否.getCode().byteValue());
        List<Long> usedRoleIds = accRoleDOList.stream().filter(accRoleDO -> accRoleDO.getStatus() == 1).map(AccRoleDO::getId).collect(Collectors.toList());
        return roleService.queryByRoleIds(companyId,usedRoleIds);
    }

    @Override
    public void assignRoleByUserId(Long companyId,Long userId,List<Long> roleIds,Long modifier){
        List<Long> userIds = Lists.newArrayList();
        userIds.add(userId);
        batchAssignRoleByUserId(companyId,userIds,roleIds,modifier);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchAssignRoleByUserId(Long companyId,List<Long> userIds,List<Long> roleIds,Long modifier){
        this.accUserRoleRelMapper.batchDeleteUserRole(companyId,userIds);
        for(Long userId : userIds){
            List<AccUserRoleRelDO> accUserRoleRelDOList = Lists.newArrayList();
            for(Long roleId : roleIds){
                AccUserRoleRelDO accUserRoleRelDO = new AccUserRoleRelDO();
                accUserRoleRelDO.setCompanyId(companyId);
                accUserRoleRelDO.setUserId(userId);
                accUserRoleRelDO.setRoleId(roleId);
                accUserRoleRelDO.setCreator(modifier);
                accUserRoleRelDO.setModifier(modifier);
                accUserRoleRelDOList.add(accUserRoleRelDO);
            }
            if(CollectionUtils.isNotEmpty(accUserRoleRelDOList)){
                this.accUserRoleRelMapper.batchInsertUserRole(accUserRoleRelDOList);
            }
        }
    }

    @Override
    public UserJobCountDTO userJobCount(Long companyId){
        UserJobCountDTO dto = new UserJobCountDTO();
        List<AccUserDO> userList = this.accUserMapper.listByCondition(companyId, null,
                null, null, null, DeleteStatusEnum.否.getCode().byteValue());
        if(CollectionUtils.isNotEmpty(userList)){
            List<AccUserDO> accUserDOList = userList.stream().filter(accUserDO -> DeleteStatusEnum.否.getCode().byteValue() == accUserDO.getIsDelete()).collect(Collectors.toList());
            dto.setTotalJobNum((long) accUserDOList.size());
            dto.setInNum(accUserDOList.stream().filter(accUserDO -> PostCodeEnum.PREN.getCode().equals(accUserDO.getPostCode())).count());
            dto.setEmployNum(accUserDOList.stream().filter(accUserDO -> PostCodeEnum.CONT.getCode().equals(accUserDO.getPostCode())).count());
            dto.setOtherNum(dto.getTotalJobNum()-dto.getInNum()-dto.getEmployNum());
        }
        return dto;
    }

    @Override
    public List<UserStatusCountDTO> userStatusCount(Long companyId){
        List<UserStatusCountDTO> resultList = Lists.newArrayList();
        List<AccUserDO> userList = this.accUserMapper.listByCondition(companyId, null,
                null, null, null, DeleteStatusEnum.否.getCode().byteValue());
        List<AccUserDO> accUserDOList = userList.stream().filter(accUserDO -> DeleteStatusEnum.否.getCode().byteValue() == accUserDO.getIsDelete()).collect(Collectors.toList());
        UserStatusCountDTO enableDto = new UserStatusCountDTO();
        enableDto.setAccountStatus(0);
        enableDto.setNum(accUserDOList.stream().filter(accUserDO -> AccountStatusEnum.禁用.getCode().byteValue() == accUserDO.getAccountStatus()).count());

        UserStatusCountDTO disableDto = new UserStatusCountDTO();
        disableDto.setAccountStatus(1);
        disableDto.setNum(accUserDOList.stream().filter(accUserDO -> AccountStatusEnum.启用.getCode().byteValue() == accUserDO.getAccountStatus()).count());
        resultList.add(enableDto);
        resultList.add(disableDto);
        return resultList;
    }

    @Override
    public List<UserDeptCountDTO> userDeptCount(Long companyId){
        List<UserDeptCountDTO> resultList = Lists.newArrayList();
        List<AccUserDO> userList = this.accUserMapper.listByCondition(companyId, null,
                null, null, null, DeleteStatusEnum.否.getCode().byteValue());
        List<AccUserDO> accUserDOList = userList.stream().filter(accUserDO -> DeleteStatusEnum.否.getCode().byteValue() == accUserDO.getIsDelete()).collect(Collectors.toList());
        Map<Long, Long> deptMap = accUserDOList.stream().collect(Collectors.groupingBy(AccUserDO::getDepartmentId, Collectors.counting()));
        List<CommClassificationDO> deptList = commClassificationMapper.selectByTypeAndLevel(companyId,ClassificationBizTypeEnum.DPTMT.getCode().byteValue(),2);
        for(CommClassificationDO commClassificationDO:deptList){
            UserDeptCountDTO userDeptCountDTO = new UserDeptCountDTO();
            userDeptCountDTO.setDeptName(commClassificationDO.getName());
            List<CommClassificationDO> commClassificationDOList = commClassificationMapper.selectIncludeChildById(companyId,commClassificationDO.getId());
            Long userNum = 0L;
            for(CommClassificationDO classificationDO : commClassificationDOList){
                if(deptMap.containsKey(classificationDO.getId())){
                    userNum += deptMap.get(classificationDO.getId());
                }
            }
            userDeptCountDTO.setNum(userNum);
            resultList.add(userDeptCountDTO);
        }
        return resultList;
    }

    @Override
    public List<UserPermissionDTO> queryUserPermissionList(Long companyId,Long userId){
        List<RoleDTO> roleDTOList = queryUserHaveRoleList(companyId,userId);
        List<Long> roleIds = roleDTOList.stream().map(RoleDTO::getRoleId).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(roleIds)){
            return Lists.newArrayList();
        }
        List<AccRolePermissionRelDO> accRolePermissionRelDOList = accRolePermissionRelMapper.listByRoleIds(companyId,roleIds);
        if(CollectionUtils.isEmpty(accRolePermissionRelDOList)){
            return Lists.newArrayList();
        }
        List<String> permissionCodes = accRolePermissionRelDOList.stream().map(AccRolePermissionRelDO::getPermissionCode).distinct().collect(Collectors.toList());
        List<AccPermissionDO> rolePermissionList = accPermissionMapper.listByPermissionCodes(companyId,permissionCodes,DeleteStatusEnum.否.getCode().byteValue());
        List<UserPermissionDTO> permissionDTOList = this.buildPermissionTree(rolePermissionList);
        if (CollectionUtils.isEmpty(permissionDTOList)) {
            return  Lists.newArrayList();
        }
        return permissionDTOList;
    }
    private List<UserPermissionDTO> buildPermissionTree(List<AccPermissionDO> permissionDOList) {
        List<UserPermissionDTO> permissionTreeDTOList = this.convertPermissionTreeDTO(permissionDOList);
        if (CollectionUtils.isEmpty(permissionTreeDTOList)) {
            return permissionTreeDTOList;
        }
        List<UserPermissionDTO> permissionTreeList = new ArrayList<>();
        for (UserPermissionDTO node : permissionTreeDTOList) {
            boolean rootFlag = false;
            for (UserPermissionDTO parentNode : permissionTreeDTOList) {
                if (Objects.nonNull(node.getParentId()) && node.getParentId().equals(parentNode.getId())) {
                    rootFlag = true;
                    if (parentNode.getChildren() == null) {
                        parentNode.setChildren(Lists.newArrayList());
                    }
                    parentNode.getChildren().add(node);
                    break;
                }
            }
            if (!rootFlag) {
                permissionTreeList.add(node);
            }
        }
        return permissionTreeList;
    }
    private List<UserPermissionDTO> convertPermissionTreeDTO(List<AccPermissionDO> permissionDOList) {
        if (CollectionUtils.isEmpty(permissionDOList)) {
            return Lists.newArrayList();
        }
        return permissionDOList.stream().map(permissionDO -> {
            UserPermissionDTO userPermissionDTO = new UserPermissionDTO();
            userPermissionDTO.setId(permissionDO.getId());
            userPermissionDTO.setIcon(permissionDO.getResourceIcon());
            userPermissionDTO.setName(permissionDO.getPermissionName());
            userPermissionDTO.setPermissionCode(permissionDO.getPermissionCode());
            userPermissionDTO.setPath(permissionDO.getResourceUrl());
            userPermissionDTO.setParentId(permissionDO.getParentId());
            userPermissionDTO.setChildren(Lists.newArrayList());
            return userPermissionDTO;
        }).collect(Collectors.toList());
    }

    @Override
    public List<UserPermissionButtonDTO> queryUserPermissionButtonList(Long companyId,Long userId){
        List<RoleDTO> roleDTOList = queryUserHaveRoleList(companyId,userId);
        List<Long> roleIds = roleDTOList.stream().map(RoleDTO::getRoleId).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(roleIds)){
            return Lists.newArrayList();
        }
        List<AccRolePermissionRelDO> accRolePermissionRelDOList = accRolePermissionRelMapper.listByRoleIds(companyId,roleIds);
        if(CollectionUtils.isEmpty(accRolePermissionRelDOList)){
            return Lists.newArrayList();
        }
        List<String> permissionCodes = accRolePermissionRelDOList.stream().map(AccRolePermissionRelDO::getPermissionCode).distinct().collect(Collectors.toList());
        List<AccPermissionDO> rolePermissionList = accPermissionMapper.listByPermissionCodes(companyId,permissionCodes,DeleteStatusEnum.否.getCode().byteValue());
        return rolePermissionList.stream().filter((accPermissionDO -> PermissionTypeEnum.FCN.getCode().equals(accPermissionDO.getPermissionType()))).map(accPermissionDO -> {
            UserPermissionButtonDTO userPermissionButtonDTO = new UserPermissionButtonDTO();
            userPermissionButtonDTO.setId(accPermissionDO.getId());
            userPermissionButtonDTO.setPermissionCode(accPermissionDO.getPermissionCode());
            userPermissionButtonDTO.setPermissionName(accPermissionDO.getPermissionName());
            return userPermissionButtonDTO;
        }).collect(Collectors.toList());
    }

    @Override
    public Boolean checkUsersRole(Long companyId,List<Long> userIds){
        Long firstUserId = userIds.get(0);
        Map<Long,List<Long>> rolesMap = new HashMap<>();
        for(Long userId:userIds){
            rolesMap.put(userId,Lists.newArrayList());
        }
        List<AccUserRoleRelDO> accUserRoleRelDOList = accUserRoleRelMapper.listByUserIds(companyId,userIds);
        for(AccUserRoleRelDO accUserRoleRelDO:accUserRoleRelDOList){
            if(rolesMap.containsKey(accUserRoleRelDO.getUserId())){
                rolesMap.get(accUserRoleRelDO.getUserId()).add(accUserRoleRelDO.getRoleId());
            }
        }
        List<Long> firstUserRoles = rolesMap.get(firstUserId);
        for (List<Long> roles : rolesMap.values()) {
            boolean checkResult = isEqualCollection(firstUserRoles,roles);
            if(!checkResult){
                return false;
            }
        }
        return true;
    }

    @Override
    public Map<Long, String> queryUserNameMap(Long companyId, List<Long> userIds) {
        if (CollectionUtils.isEmpty(userIds)) {
            return Maps.newHashMap();
        }
        List<AccUserDO> accUserInfoDOList = this.accUserMapper.listByIds(companyId, userIds);
        if (CollectionUtils.isEmpty(accUserInfoDOList)) {
            return Maps.newHashMap();
        }
        return accUserInfoDOList.stream()
                .collect(Collectors.toMap(AccUserDO::getId, AccUserDO::getUserName, (key1, key2) -> key2));
    }

    @Override
    public UserClassificationDTO queryUserTree(String userName) {
        // LoginUserInfoBO userBO = LoginUserInfoUtils.getLoginUserInfo();
        List<CommClassificationDO> classificationDOList = this.commClassificationMapper
                .selectAllChildById(1L, (byte)3, null, null);
        if (CollectionUtils.isEmpty(classificationDOList)) {
            return null;
        }
        List<UserClassificationDTO> userClassificationDTOList = classificationDOList.stream().map(c -> {
            UserClassificationDTO userClassificationDTO = new UserClassificationDTO();
            userClassificationDTO.setBizType((byte)3);
            userClassificationDTO.setClassificationId(c.getId());
            userClassificationDTO.setParentId(c.getParentId());
            userClassificationDTO.setName(c.getName());
            return userClassificationDTO;
        }).collect(Collectors.toList());
        List<AccUserDO> accUserDOList = this.accUserMapper.listByIsDelete(userName, DeleteStatusEnum.否.getCode().byteValue());
        if (CollectionUtils.isNotEmpty(accUserDOList)) {
            accUserDOList.forEach(c -> {
                UserClassificationDTO userClassificationDTO = new UserClassificationDTO();
                userClassificationDTO.setBizType((byte)3);
                userClassificationDTO.setUserId(c.getId());
                userClassificationDTO.setParentId(c.getDepartmentId());
                userClassificationDTO.setName(c.getUserName());
                userClassificationDTOList.add(userClassificationDTO);
            });
        }
        List<UserClassificationDTO> classificationDTOList = this.buildClassificationTree(userClassificationDTOList);
        return classificationDTOList.get(0);
    }

    public static boolean isEqualCollection(List<Long> firstList, List<Long> secondList) {
        if (firstList.size() != secondList.size()) {
            return false;
        }
        for (Long data:firstList) {
            if (!secondList.contains(data)) {
                return false;
            }
        }
        for (Long data:secondList) {
            if (!firstList.contains(data)) {
                return false;
            }
        }
        return true;
    }

    private List<UserClassificationDTO> buildClassificationTree(List<UserClassificationDTO> userClassificationDTOList) {
        if (CollectionUtils.isEmpty(userClassificationDTOList)) {
            return userClassificationDTOList;
        }
        List<UserClassificationDTO> classificationDtoTreeList = new ArrayList<>();
        for (UserClassificationDTO node : userClassificationDTOList) {
            boolean rootFlag = false;
            for (UserClassificationDTO parentNode : userClassificationDTOList) {
                if (Objects.nonNull(node.getParentId()) && node.getParentId().equals(parentNode.getClassificationId())) {
                    rootFlag = true;
                    if (parentNode.getChildClassificationList() == null) {
                        parentNode.setChildClassificationList(Lists.newArrayList());
                    }
                    parentNode.getChildClassificationList().add(node);
                    break;
                }
            }
            if (!rootFlag) {
                classificationDtoTreeList.add(node);
            }
        }
        return classificationDtoTreeList;
    }

}
