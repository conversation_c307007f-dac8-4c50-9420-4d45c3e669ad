package com.das.province.service.biz.user.dto;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

@Data
public class UserPageDTO implements Serializable {
    private static final long serialVersionUID = 931436772484939219L;
    private Long userId;
    private Long companyId;
    private String userName;
    private String loginAccount;
    private String password;
    private String phone;
    private Long departmentId;
    private String departmentName;
    private Long positionId;
    private String positionName;
    private String postCode;
    private String postName;
    private Byte accountStatus;
    private Byte isDelete;

    /**
     * 是否是领导: 1:是,0:否
     */
    private Byte isLeader;

    /**
     * 领导级别
     */
    private Byte leaderLevel;

    /**
     * 角色ids
     */
    private List<Long> roleIds;

    /**
     * 角色名称
     */
    private List<String> roleName;
}