package com.das.province.service.biz.authentication;

import cn.hutool.core.util.RandomUtil;
import cn.hutool.crypto.symmetric.SymmetricAlgorithm;
import cn.hutool.crypto.symmetric.SymmetricCrypto;
import com.alibaba.fastjson.JSON;
import com.das.province.common.utils.Md5DigesterUtil;
import com.das.province.infr.dataobject.SyncMuseumAuthDO;
import com.das.province.infr.mapper.SyncMuseumAuthMapper;
import com.das.province.service.biz.authentication.action.GenerateSessionKeyAction;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023/3/17
 */
@Slf4j
@Service
public class ProvinceAuthServiceImpl implements ProvinceAuthService {

    @Value("${aes.session.key}")
    private String sessionKey;

    @Resource
    private SyncMuseumAuthMapper syncMuseumAuthMapper;

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    private static final String PROVINCE_AUTH_KEY_PREFIX = "PROVINCE_AUTH_KEY_";

    @Override
    // @Transactional(rollbackFor = Exception.class)
    public Long generateSessionKey(GenerateSessionKeyAction action) {
        SyncMuseumAuthDO syncMuseumAuth = this.syncMuseumAuthMapper.selectByMuseumBaseId(action.getMuseumBaseId());
        if (Objects.isNull(syncMuseumAuth)) {
            return -1L;
        }
        Long serverPreMaster = RandomUtil.randomLong(10000, 100000);
        String content = action.getPreMaster() + String.valueOf(serverPreMaster) + syncMuseumAuth.getUniqueCode();
        // SymmetricCrypto aes = new SymmetricCrypto(SymmetricAlgorithm.AES, sessionKey.getBytes());
        // String encryptHexKey = aes.encryptHex(content);
        syncMuseumAuth.setKey(Md5DigesterUtil.digestHex(content, sessionKey));
        this.syncMuseumAuthMapper.updateByPrimaryKeySelective(syncMuseumAuth);
        try {
            this.stringRedisTemplate.opsForValue().set(PROVINCE_AUTH_KEY_PREFIX + action.getMuseumBaseId(), JSON.toJSONString(syncMuseumAuth));
        } catch (Exception e) {
            log.error("ProvinceAuthServiceImpl getSessionKey error: ", e);
        }
        return serverPreMaster;
    }

    @Override
    public SyncMuseumAuthDO getMuseumAuth(Long museumBaseId) {
        try {
            String authValue = this.stringRedisTemplate.opsForValue().get(PROVINCE_AUTH_KEY_PREFIX + museumBaseId);
            if (StringUtils.isNotBlank(authValue)) {
                return JSON.parseObject(authValue, SyncMuseumAuthDO.class);
            }
        } catch (Exception e) {
            log.error("ProvinceAuthServiceImpl getMuseumAuth redis error: ", e);
        }
        SyncMuseumAuthDO syncMuseumAuth = this.syncMuseumAuthMapper.selectByMuseumBaseId(museumBaseId);
        if (Objects.isNull(syncMuseumAuth) || StringUtils.isBlank(syncMuseumAuth.getKey())) {
            return null;
        }
        try {
            this.stringRedisTemplate.opsForValue().set(PROVINCE_AUTH_KEY_PREFIX + museumBaseId, JSON.toJSONString(syncMuseumAuth));
        } catch (Exception e) {
            log.error("ProvinceAuthServiceImpl getMuseumAuth redis error: ", e);
        }
        return syncMuseumAuth;
    }
}
