package com.das.province.service.biz.common.action;

import com.das.province.service.enums.ClassificationBizTypeEnum;
import lombok.Data;

import java.io.Serializable;

@Data
public class QueryClassificationTreeAction implements Serializable {
    private static final long serialVersionUID = -3058282698973478438L;
    private Long companyId;
    private Long bizId;
    private String bizCode;
    private ClassificationBizTypeEnum bizType;
    private Long creator;
}
