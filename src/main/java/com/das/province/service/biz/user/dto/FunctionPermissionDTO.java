package com.das.province.service.biz.user.dto;

import lombok.Data;

import java.io.Serializable;

@Data
public class FunctionPermissionDTO implements Serializable {
    private static final long serialVersionUID = 8850540453005214318L;
    private Long permissionId;
    private Long companyId;
    private String permissionName;
    private String permissionCode;
    private String permissionType;
    private Long parentId;
    private Byte level;
    private Byte sortIndex;
}