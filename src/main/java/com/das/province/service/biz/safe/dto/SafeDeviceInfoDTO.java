package com.das.province.service.biz.safe.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR> 温湿度设备信息表
 */
@Data
public class SafeDeviceInfoDTO implements Serializable {

    private static final long serialVersionUID = -8620920672359276399L;

    /**
     * 公司名称
     */
    private String museumName;

    /**
     * 公司编码
     */
    private String uniqueCode;

    /**
     * 所属地区编码
     */
    private String regionCode;

    /**
     * 所在区域(1展厅,2库房,3其他)
     */
    private Byte regionName;

    /**
     * 设备编码
     */
    private String deviceCode;

    /**
     * 设备位置
     */
    private String deviceAddress;

    /**
     * 最低温度阈值
     */
    private String minTemperature;

    /**
     * 最高温度阈值
     */
    private String maxTemperature;

    private String temperatureLen;

    /**
     * 最低湿度阈值
     */
    private String minHumidity;

    /**
     * 最高湿度阈值
     */
    private String maxHumidity;

    private String humidityLen;

    /**
     * 温度
     */
    private String temperature;

    /**
     * 温度异常程度: 1:轻度, 2:中度, 3:严重
     */
    private String temperatureExceptionDegree;

    /**
     * 湿度
     */
    private String humidity;

    /**
     * 湿度异常程度: 1:轻度, 2:中度, 3:严重
     */
    private String humidityExceptionDegree;

    /**
     * 上报时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date reportTime;

    /**
     * 上报时间
     */
    private String reportTimeStr;

    /**
     * 设备状态：1:在线, 0:离线
     */
    private Byte deviceStatus;

    /**
     * 设备状态：1:在线, 0:离线
     */
    private String deviceStatusStr;

    private Integer sdx;
}