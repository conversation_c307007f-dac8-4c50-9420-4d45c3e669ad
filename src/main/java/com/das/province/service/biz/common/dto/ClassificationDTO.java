package com.das.province.service.biz.common.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
public class ClassificationDTO implements Serializable {
    private static final long serialVersionUID = 2908779329654896274L;
    private Long classificationId;
    private Long companyId;
    private Long bizId;
    private String bizCode;
    private Long bizNumber;
    private Byte bizType;
    private String name;
    private Long parentId;
    private Byte level;
    private Integer sortIndex;
    private String permission;
    private Byte enabledStatus;
    private Byte leafNode;
    private String remark;
    private Long creator;
    private Long modifier;
    private Date gmtCreate;
    private Date gmtModified;
    private List<ClassificationDTO> childClassificationList;
}