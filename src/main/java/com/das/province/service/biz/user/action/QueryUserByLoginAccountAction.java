package com.das.province.service.biz.user.action;

import com.das.province.service.enums.LoginClientTypeEnum;
import lombok.Data;

import java.io.Serializable;

@Data
public class QueryUserByLoginAccountAction implements Serializable {
    private static final long serialVersionUID = -2691655685497070427L;
    private String loginAccount;
    private String password;
    private LoginClientTypeEnum loginClientType;
}
