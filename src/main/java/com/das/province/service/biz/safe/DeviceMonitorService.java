package com.das.province.service.biz.safe;

import com.das.province.common.bo.SimplePageInfo;
import com.das.province.service.biz.safe.dto.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/7/11
 */
public interface DeviceMonitorService {

    DeviceMonitorInfoDTO getDeviceMonitorInfo(String museumId, String regionCode, String exceptionDegree, String regionName);

    SimplePageInfo<SafeDeviceInfoDTO> getPageByConditionQuery(String museumId, String regionCode, String exceptionDegree,
                                                              String regionName, Integer type, Integer pageNum, Integer pageSize);

    SimplePageInfo<SafeDeviceInfoDTO> pageByDeviceCode(String deviceCode, Integer isException, Integer statisticsWay, String startTime,
                                                       String endTime, Integer pageNum, Integer pageSize);

    SingleDeviceMonitorInfoDTO getByDeviceCode(String deviceCode, Integer isException, Integer statisticsWay, String startTime, String endTime);

    TemperatureTrendDTO getTemperatureByDeviceCode(String deviceCode, Integer statisticsWay);

    HumidityTrendDTO getHumidityByDeviceCode(String deviceCode, Integer statisticsWay);

    List<SafeDeviceInfoDTO> deviceInfoExport(String deviceCode, Integer isException, Integer statisticsWay, String startTime, String endTime);
}
