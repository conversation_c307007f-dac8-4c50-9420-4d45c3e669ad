package com.das.province.service.biz.user.action;

import com.das.province.common.bo.PageInfoBaseReq;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class QueryPageByConditionAction extends PageInfoBaseReq {
    private static final long serialVersionUID = -8925892197416031918L;
    private Long companyId;
    private Long departmentId;
    private String queryContent;
    private Byte accountStatus;
    private String sortBy;
}
