package com.das.province.service.biz.user.action;

import com.das.province.common.bo.PageInfoBaseReq;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class QueryPagePermissionByCondition extends PageInfoBaseReq {
    private static final long serialVersionUID = -4235746757978129190L;
    private Long permissionId;
    private String permissionName;
    private Byte level;
    private Long companyId;
    private String sortBy;
}
