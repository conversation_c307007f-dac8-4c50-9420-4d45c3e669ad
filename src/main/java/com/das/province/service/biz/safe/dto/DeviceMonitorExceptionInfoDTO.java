package com.das.province.service.biz.safe.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023/7/11
 */
@Data
public class DeviceMonitorExceptionInfoDTO implements Serializable {

    private static final long serialVersionUID = -1693267030700322471L;

    /**
     * 异常数
     */
    private Integer exceptionTotalCount;

    /**
     * 异常率
     */
    private String exceptionTotalRatio;

    /**
     * 温度异常数
     */
    private Integer temperatureExceptionTotalCount;

    /**
     * 温度异常数(轻度)
     */
    private Integer temperatureMildExceptionCount;

    /**
     * 温度异常数(中度)
     */
    private Integer temperatureModerateExceptionCount;

    /**
     * 温度异常数(重度)
     */
    private Integer temperatureSevereExceptionCount;

    /**
     * 温度异常率
     */
    private String temperatureExceptionRatio;

    /**
     * 湿度异常数
     */
    private Integer humidityExceptionTotalCount;

    /**
     * 湿度异常数(轻度)
     */
    private Integer humidityMildExceptionCount;

    /**
     * 湿度异常数(中度)
     */
    private Integer humidityModerateExceptionCount;

    /**
     * 湿度异常数(重度)
     */
    private Integer humiditySevereExceptionCount;

    /**
     * 湿度异常率
     */
    private String humidityExceptionRatio;

    /**
     * 设备离线次数
     */
    private Integer deviceOfflineTotalCount;

    /**
     * 离线设备数
     */
    private Integer offlineDeviceCount;

    /**
     * 离线率
     */
    private String offlineRatio;

    /**
     * 在线次数
     */
    private Integer deviceOnlineCount;

    /**
     * 在线设备
     */
    private Integer onlineDeviceCount;

    /**
     * 在线率
     */
    private String onlineRatio;

    /**
     * 离线设备异常率
     */
    private String deviceOfflineExceptionRatio;
}
