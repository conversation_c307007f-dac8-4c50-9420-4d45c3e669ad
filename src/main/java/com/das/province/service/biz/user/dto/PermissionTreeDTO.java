package com.das.province.service.biz.user.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class PermissionTreeDTO implements Serializable {
    private static final long serialVersionUID = -1021577232349673692L;
    private Long permissionId;
    private Long companyId;
    private String permissionName;
    private String permissionCode;
    private String permissionType;
    private Long parentId;
    private Byte level;
    private List<PermissionTreeDTO> childPermissionList;
}