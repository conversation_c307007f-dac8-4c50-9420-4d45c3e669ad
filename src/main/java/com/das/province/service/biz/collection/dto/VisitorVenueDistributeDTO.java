package com.das.province.service.biz.collection.dto;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class VisitorVenueDistributeDTO {

    /**
     * 博物馆编码
     */
    private String museumCode;

    /**
     * 博物馆名称
     */
    private String museumName;

    /**
     * 地区
     */
    private String cityName;

    /**
     * 观众数量
     */
    private Integer visitorNum;

    /**
     * 观众接待全市占比
     */
    private BigDecimal inCityRatio;

    /**
     * 观众接待全省占比
     */
    private BigDecimal inProvinceRatio;

    /**
     * 男占比
     */
    private BigDecimal manNumRatio;

    /**
     * 女占比
     */
    private BigDecimal womanNumRatio;

    /**
     * 未成年人数量占比
     */
    private BigDecimal noAdultNumRatio;

    /**
     * 本地观众占比
     */
    private BigDecimal localNumRatio;

    /**
     * 国内异地观众占比
     */
    private BigDecimal offsiteNumRatio;

    /**
     * 境外观众占比
     */
    private BigDecimal abroadNumRatio;
}
