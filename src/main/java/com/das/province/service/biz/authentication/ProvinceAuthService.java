package com.das.province.service.biz.authentication;

import com.das.province.infr.dataobject.SyncMuseumAuthDO;
import com.das.province.service.biz.authentication.action.GenerateSessionKeyAction;

/**
 * <AUTHOR>
 * @date 2023/3/17
 */
public interface ProvinceAuthService {

    /**
     * 生成密钥
     * @param action action
     * @return 服务端preMaster
     */
    Long generateSessionKey(GenerateSessionKeyAction action);

    SyncMuseumAuthDO getMuseumAuth(Long museumBaseId);
}
