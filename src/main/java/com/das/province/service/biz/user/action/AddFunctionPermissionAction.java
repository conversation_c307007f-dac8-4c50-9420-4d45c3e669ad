package com.das.province.service.biz.user.action;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class AddFunctionPermissionAction implements Serializable {
    private static final long serialVersionUID = -5208857331870416566L;
    private Long parentId;
    private Long companyId;
    private List<SaveFunctionPermissionAction> functionPermissionList;
    private Long modifier;
}
