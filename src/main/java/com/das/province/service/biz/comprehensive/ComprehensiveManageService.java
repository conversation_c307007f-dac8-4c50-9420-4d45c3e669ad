package com.das.province.service.biz.comprehensive;

import com.das.province.common.bo.SimplePageInfo;
import com.das.province.service.biz.comprehensive.action.ChangeRemindPageAction;
import com.das.province.service.biz.comprehensive.action.GeneralStatisticMuseumAction;
import com.das.province.service.biz.comprehensive.action.GeneralStatisticRegionAction;
import com.das.province.service.biz.comprehensive.action.MuseumInfoListAction;
import com.das.province.web.controller.comprehensive.response.*;

import java.util.List;

/**
 * 综合管理
 */

public interface ComprehensiveManageService {

    /**
     * 博物馆概况
     * @return
     */
    MuseumGeneralVO queryMuseumGeneral();

    /**
     * 博物馆城市分布
     */
    List<CityMuseumNumVO> queryCityMuseumNum();

    /**
     * 查询博物馆列表
     * @param action
     * @return
     */
    SimplePageInfo<MuseumInfoListVO> museumInfoList(MuseumInfoListAction action);

    /**
     * 异动信息列表
     * @return
     */
    SimplePageInfo<ChangeRemindPageVO>  queryChangeRemindPageList(ChangeRemindPageAction action);

    /**
     * 异动信息详情列表
     * @return
     */
    List<ChangeDetailInfoVO> queryChangeRemindDetailList(Long changeId);

    /**
     * 全省观众访问统计
     * @return
     */
    ProvinceVisitorStatisticsVO provinceVisitorStatistics();

    /**
     * 全省藏品统计
     * @return
     */
    ProvinceCollectionStatisticsVO provinceCollectionStatistics();

    /**
     * 全省热门top5
     * @return
     */
    List<HotVisitorTopVO> hotVisitorTop();

    /**
     * 查询指定市的观众统计信息
     * @return
     */
    CityVisitorStatisticsVO cityVisitorStatistics(String regionCode);

    /**
     * 市各博物馆藏品、观众统计
     * @param regionCode
     * @return
     */
    List<MuseumDataStatisticsVO> museumDataStatistics(String regionCode,Byte sortBy,Byte orderBy);

    /**
     * 市概况、藏品动态
     * @param regionCode
     * @return
     */
    CityCollectionStatisticsVO cityCollectionStatistics(String regionCode);


    /**
     * 地区情况
     * @return
     */
    SimplePageInfo<GeneralStatisticRegionVO>  generalStatisticRegionList(GeneralStatisticRegionAction action);

    /**
     * 博物馆情况
     * @return
     */
    SimplePageInfo<GeneralStatisticMuseumVO>  generalStatisticMuseumList(GeneralStatisticMuseumAction action);

    /**
     * 按藏品等级统计藏品数量
     * @return
     */
    List<CollectionGroupByLevelVO>  collectionGroupByLevel();


    ChangeThInfoVO getChangeDeviceThInfo(Long changeId);


    List<ChangeOfflineVO> getChangeDeviceOfflineList(Long changeId);

}
