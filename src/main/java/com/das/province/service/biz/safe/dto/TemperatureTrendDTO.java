package com.das.province.service.biz.safe.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/7/14
 */
@Data
public class TemperatureTrendDTO implements Serializable {

    private static final long serialVersionUID = 2952211451870331265L;

    /**
     * 温度趋势
     */
    private List<Trend> temperatureTrend;

    /**
     * 平均值
     */
    private Double averageValue;

    /**
     * 最低温度阈值
     */
    private String minTemperature;

    /**
     * 最高温度阈值
     */
    private String maxTemperature;

    @Data
    public static class Trend {
        /**
         * 日期
         */
        private String date;

        /**
         * 数据
         */
        private List<TrendData> trendData;
    }

    @Data
    public static class TrendData {
        /**
         * 日期
         */
        private String date;

        /**
         * 温度
         */
        private String temperature;
    }
}
