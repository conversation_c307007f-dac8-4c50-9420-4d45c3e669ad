package com.das.province.service.biz.user.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class PermissionPageDTO implements Serializable {
    private static final long serialVersionUID = 3651779890934960812L;
    private Long permissionId;
    private Long companyId;
    private String permissionName;
    private String permissionCode;
    private String permissionType;
    private Long parentId;
    private String parentName;
    private Byte level;
    private String levelName;
    private Byte sortIndex;
    private Byte functionStatus;
    private String functionStatusDesc;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date gmtCreate;
}