package com.das.province.service.biz.user;

import com.das.province.service.biz.user.action.*;
import com.das.province.service.biz.user.dto.PermissionDTO;
import com.das.province.service.biz.user.dto.PermissionPageDTO;
import com.das.province.service.biz.user.dto.PermissionTreeDTO;
import com.das.province.service.enums.PermissionTypeEnum;
import com.das.province.common.bo.SimplePageInfo;

import java.util.List;

public interface PermissionService {
    Long addPermission(AddPermissionAction action);
    void saveFunctionPermission(AddFunctionPermissionAction action);
    void editPermissionById(EditPermissionByIdAction action);
    void delPermissionById(DelPermissionByIdAction action);
    void batchDelPermissionById(BatchDelPermissionByIdAction action);
    SimplePageInfo<PermissionPageDTO> queryPageByCondition(QueryPagePermissionByCondition action);
    PermissionDTO queryByPermissionId(Long companyId, Long permissionId);
    List<PermissionDTO> queryDirectChildByPermissionId(Long companyId, Long permissionId);
    PermissionTreeDTO queryTreeByPermissionType(Long companyId, PermissionTypeEnum permissionType);
    PermissionTreeDTO queryTreeByPermissionId(Long companyId, Long permissionId, String permissionType);
}
