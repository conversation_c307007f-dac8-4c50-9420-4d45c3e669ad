package com.das.province.service.biz.museum.action;

import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
public class AddMuseumAction {

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 博物馆名称
     */
    private String museumName;

    /**
     * 所属地区编码
     */
    private String regionCode;

    /**
     * 所属省份名称
     */
    private String provinceName;

    /**
     * 所属城市名称
     */
    private String cityName;

    /**
     * 所属区县名称
     */
    private String countyName;

    /**
     * 等级id
     */
    private Long levelId;

    /**
     * 博物馆性质id
     */
    private Long natureId;

    /**
     * 博物馆类型id
     */
    private Long typeId;

    /**
     * 官方平台地址
     */
    private String platformAddress;

    /**
     * 藏品年报数据
     */
    private Long reportCollectionNum;

    /**
     * 博物馆经度
     */
    private String lng;

    /**
     * 博物馆纬度
     */
    private String lat;

    /**
     * 馆长姓名
     */
    private String curatorName;

    /**
     * 馆长电话
     */
    private String curatorPhone;

    /**
     * 消防负责人姓名
     */
    private String firemenName;

    /**
     * 消防负责人电话
     */
    private String firemenPhone;

    /**
     * 安防负责人姓名
     */
    private String securityName;

    /**
     * 安防负责人电话
     */
    private String securityPhone;

    /**
     * 藏品负责人姓名
     */
    private String collectionName;

    /**
     * 藏品负责人电话
     */
    private String collectionPhone;

    /**
     * 巡查人员姓名
     */
    private String patrolName;

    /**
     * 巡查人员电话
     */
    private String patrolPhone;

    /**
     * 是否已入驻省平台 0否 1是
     */
    private Byte settleIn;

    private Integer sdx;
}
