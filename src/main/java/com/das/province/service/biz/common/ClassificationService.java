package com.das.province.service.biz.common;

import com.das.province.service.biz.common.action.*;
import com.das.province.service.biz.common.dto.ClassificationDTO;
import com.das.province.service.enums.ClassificationBizTypeEnum;

import java.util.List;

public interface ClassificationService {
    ClassificationDTO queryClassificationTree(QueryClassificationTreeAction action);
    ClassificationDTO queryClassificationTreeById(QueryClassificationTreeByIdAction action);
    ClassificationDTO queryTreeByBizCodeAndBizType(QueryTreeByBizCodeAndBizTypeAction action);
    ClassificationDTO queryByClassificationId(Long companyId, Long classificationId);
    List<ClassificationDTO> queryChildByParentId(Long companyId, Long parentId);
    ClassificationDTO queryByParentIdAndName(Long companyId, Long parentId, String name);
    ClassificationDTO queryByTypeAndParentIdAndName(Long companyId, ClassificationBizTypeEnum bizType, Long parentId, String name);
    ClassificationDTO queryByTypeAndCode(Long companyId, ClassificationBizTypeEnum bizType, String bizCode);
    Long addClassification(AddClassificationAction action);
    void editClassification(EditClassificationAction action);
    void delClassificationIncludeChild(Long companyId, Long classificationId);

    /**
     * 停用 启用
     * @param companyId
     * @param classificationId
     * @param enableStatus
     */
    void updateEnableStatus(Long companyId,Long userId,Long classificationId,Byte enableStatus);

    /**
     * 根据当前列表的层级和指定字符串查询
     * @param name
     * @param classificationId
     * @return
     */
    List<ClassificationDTO> queryByParentAndName(Long companyId, String name, Long classificationId);
}
