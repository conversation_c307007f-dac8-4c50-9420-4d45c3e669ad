package com.das.province.service.biz.safe.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023/7/11
 */
@Data
public class DeviceMonitorInfoDTO implements Serializable {

    private static final long serialVersionUID = -6318836741511560735L;

    /**
     * 监控设备数
     */
    private Integer monitorDeviceCount;

    /**
     * 温度正常数
     */
    private Integer temperatureCount;

    /**
     * 湿度正常数
     */
    private Integer humidityCount;

    /**
     * 监控设备异常数
     */
    private Integer monitorDeviceExceptionCount;

    /**
     * 温度异常数
     */
    private Integer temperatureExceptionCount;

    /**
     * 湿度异常数
     */
    private Integer humidityExceptionCount;

    /**
     * 离线设备数
     */
    private Integer offlineDeviceCount;
}
