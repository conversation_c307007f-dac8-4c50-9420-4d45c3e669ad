package com.das.province.service.biz.common;

import com.das.province.common.exception.CommonErrorCodeEnum;
import com.das.province.common.exception.CommonException;
import com.das.province.common.extension.BizScenario;
import com.das.province.common.extension.ExtensionCoordinate;
import com.das.province.common.extension.ExtensionExecutor;
import com.das.province.common.utils.BeanCopyUtils;
import com.das.province.infr.dataobject.CommClassificationDO;
import com.das.province.infr.entity.ClassificationEntity;
import com.das.province.infr.mapper.CommClassificationMapper;
import com.das.province.infr.repository.ClassificationRepository;
import com.das.province.service.biz.common.action.*;
import com.das.province.service.biz.common.dto.ClassificationDTO;
import com.das.province.service.enums.ClassificationBizTypeEnum;
import com.das.province.service.extension.ClassificationDeleteExtPt;
import com.das.province.service.extension.ClassificationScenario;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@Service
public class ClassificationServiceImpl implements ClassificationService {

    @Resource
    private ClassificationRepository classificationRepository;

    @Resource
    private CommClassificationMapper commClassificationMapper;

    @Resource
    private ExtensionExecutor extensionExecutor;

    private static final String WHS_ROOT_NAME = "库房管理";

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ClassificationDTO queryClassificationTree(QueryClassificationTreeAction action) {
        Long companyId = action.getCompanyId();
        Long bizId = action.getBizId();
        String bizCode = action.getBizCode();
        Byte bizType = action.getBizType().getCode().byteValue();
        CommClassificationDO classificationRootDO = this.commClassificationMapper
                .selectRootByCompanyIdAndType(companyId, bizType, bizId, bizCode);
        if (Objects.isNull(classificationRootDO) && ClassificationBizTypeEnum.WHS.getCode().byteValue() == bizType) {
            CommClassificationDO rootDO = new CommClassificationDO();
            rootDO.setCompanyId(companyId);
            rootDO.setBizType(ClassificationBizTypeEnum.WHS.getCode().byteValue());
            rootDO.setName(WHS_ROOT_NAME);
            rootDO.setLevel((byte)1);
            rootDO.setSortIndex(1);
            rootDO.setCreator(action.getCreator());
            rootDO.setModifier(action.getCreator());
            commClassificationMapper.insert(rootDO);
            ClassificationDTO classificationTree = BeanCopyUtils.copyByJSON(rootDO, ClassificationDTO.class);
            classificationTree.setClassificationId(rootDO.getId());
            classificationTree.setChildClassificationList(Lists.newArrayList());
            return classificationTree;
        }
        if (Objects.isNull(classificationRootDO)) {
            return null;
        }
        List<CommClassificationDO> classificationDOList = this.commClassificationMapper
                .selectAllChildById(companyId, bizType, bizId, bizCode);
        List<ClassificationDTO> classificationDTOList = this.buildClassificationTree(classificationDOList, false);
        if (CollectionUtils.isEmpty(classificationDTOList)) {
            return null;
        }
        return classificationDTOList.get(0);
    }

    @Override
    public ClassificationDTO queryClassificationTreeById(QueryClassificationTreeByIdAction action) {
        Long companyId = action.getCompanyId();
        Long classificationId = action.getClassificationId();
        CommClassificationDO classificationCurrDO = this.commClassificationMapper
                .selectById(companyId, classificationId);
        if (Objects.isNull(classificationCurrDO)) {
            return null;
        }
        List<CommClassificationDO> classificationDOList = this.commClassificationMapper
                .selectChildById(companyId, classificationId);
        Long parentId = classificationCurrDO.getParentId();
        classificationCurrDO.setParentId(null);
        classificationDOList.add(classificationCurrDO);
        List<ClassificationDTO> classificationDTOList = this.buildClassificationTree(classificationDOList, false);
        if (CollectionUtils.isEmpty(classificationDTOList)) {
            return null;
        }
        classificationCurrDO.setParentId(parentId);
        return classificationDTOList.get(0);
    }

    @Override
    public ClassificationDTO queryTreeByBizCodeAndBizType(QueryTreeByBizCodeAndBizTypeAction action) {
        Long companyId = action.getCompanyId();
        String bizCode = action.getBizCode();
        Byte bizType = action.getBizType().getCode().byteValue();
        CommClassificationDO classificationCurrDO = this.commClassificationMapper
                .selectByBizCodeAndBizType(companyId, bizType, bizCode);
        if (Objects.isNull(classificationCurrDO)) {
            return null;
        }
        List<CommClassificationDO> classificationDOList = this.commClassificationMapper
                .selectChildById(companyId, classificationCurrDO.getId());
        if(classificationDOList.isEmpty()){
            return null;
        }
        // 只返回启用状态的
        classificationDOList = classificationDOList.stream().filter(commClassificationDO -> commClassificationDO.getEnabledStatus() == 1).collect(Collectors.toList());
        Long parentId = classificationCurrDO.getParentId();
        classificationCurrDO.setParentId(null);
        classificationDOList.add(classificationCurrDO);
        List<ClassificationDTO> classificationDTOList = this.buildClassificationTree(classificationDOList, false);
        if (CollectionUtils.isEmpty(classificationDTOList)) {
            return null;
        }
        classificationCurrDO.setParentId(parentId);
        return classificationDTOList.get(0);
    }

    @Override
    public ClassificationDTO queryByClassificationId(Long companyId, Long classificationId) {
        CommClassificationDO classificationCurrDO = this.commClassificationMapper
                .selectById(companyId, classificationId);
        if (Objects.isNull(classificationCurrDO)) {
            return null;
        }
        ClassificationDTO classificationDTO = BeanCopyUtils.copyByJSON(classificationCurrDO, ClassificationDTO.class);
        classificationDTO.setClassificationId(classificationCurrDO.getId());
        return classificationDTO;
    }

    @Override
    public List<ClassificationDTO> queryChildByParentId(Long companyId, Long parentId) {
        List<CommClassificationDO> classificationDOList = this.commClassificationMapper.selectChildByParentId(companyId, parentId);
        if (CollectionUtils.isEmpty(classificationDOList)) {
            return Lists.newArrayList();
        }
        return this.convertClassificationDTO(classificationDOList);
    }

    @Override
    public ClassificationDTO queryByParentIdAndName(Long companyId, Long parentId, String name) {
        CommClassificationDO classificationCurrDO = this.commClassificationMapper
                .selectByParentIdAndName(companyId, parentId, name);
        if (Objects.isNull(classificationCurrDO)) {
            return null;
        }
        ClassificationDTO classificationDTO = BeanCopyUtils.copyByJSON(classificationCurrDO, ClassificationDTO.class);
        classificationDTO.setClassificationId(classificationCurrDO.getId());
        return classificationDTO;
    }

    @Override
    public ClassificationDTO queryByTypeAndParentIdAndName(Long companyId, ClassificationBizTypeEnum bizType, Long parentId, String name) {
        CommClassificationDO classificationCurrDO = this.commClassificationMapper
                .selectByTypeAndParentIdAndName(companyId, bizType.getCode().byteValue(), parentId, name);
        if (Objects.isNull(classificationCurrDO)) {
            return null;
        }
        ClassificationDTO classificationDTO = BeanCopyUtils.copyByJSON(classificationCurrDO, ClassificationDTO.class);
        classificationDTO.setClassificationId(classificationCurrDO.getId());
        return classificationDTO;
    }

    @Override
    public ClassificationDTO queryByTypeAndCode(Long companyId, ClassificationBizTypeEnum bizType, String bizCode) {
        CommClassificationDO classificationDO = this.commClassificationMapper.selectByBizCodeAndBizType(companyId, bizType.getCode().byteValue(), bizCode);
        if (Objects.isNull(classificationDO)) {
            return null;
        }
        ClassificationDTO classificationDTO = BeanCopyUtils.copyByJSON(classificationDO, ClassificationDTO.class);
        classificationDTO.setClassificationId(classificationDO.getId());
        return classificationDTO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long addClassification(AddClassificationAction action) {
        Long companyId = action.getCompanyId();
        Long parentId = action.getParentId();
        String name = action.getName();
        Byte bizType = action.getBizType();
        this.addAndEditVailVerify(companyId, parentId, name, bizType, action.getBizCode(), false, null);
        ClassificationEntity entity = BeanCopyUtils.copyByJSON(action, ClassificationEntity.class);
        entity.setModifier(action.getCreator());
        this.classificationRepository.saveClassification(entity);
        return entity.getClassificationId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void editClassification(EditClassificationAction action) {
        Long companyId = action.getCompanyId();
        Long parentId = action.getParentId();
        String name = action.getName();
        Byte bizType = action.getBizType();
        String bizCode = action.getBizCode();
        this.addAndEditVailVerify(companyId, parentId, name, bizType, bizCode, true, action.getClassificationId());
        ClassificationEntity entity = BeanCopyUtils.copyByJSON(action, ClassificationEntity.class);
        entity.setModifier(action.getModifier());
        this.classificationRepository.updateClassification(entity);
    }

    private void addAndEditVailVerify(Long companyId, Long parentId, String name, Byte bizType,
                                      String bizCode, Boolean editFlag, Long currClassificationId) {
        ClassificationDTO classificationDTO = this.queryByTypeAndParentIdAndName(companyId,
                ClassificationBizTypeEnum.fromCode(bizType.intValue()), parentId, name);
        if (editFlag && Objects.nonNull(classificationDTO)
                && !currClassificationId.equals(classificationDTO.getClassificationId())) {
            throw new CommonException(CommonErrorCodeEnum.VALIDATE_ERROR, "[" + name + "]分类已经存在");
        } else if (!editFlag && Objects.nonNull(classificationDTO)) {
            throw new CommonException(CommonErrorCodeEnum.VALIDATE_ERROR, "[" + name + "]分类已经存在");
        }
        if (StringUtils.isBlank(bizCode)) {
            return;
        }
        CommClassificationDO classificationCurrDO = this.commClassificationMapper
                .selectByBizCodeAndBizType(companyId, bizType, bizCode);
        if (editFlag && Objects.nonNull(classificationCurrDO)
                && !currClassificationId.equals(classificationCurrDO.getId())) {
            throw new CommonException(CommonErrorCodeEnum.VALIDATE_ERROR, "[" + bizCode + "]分类编码已经存在");
        } else if (!editFlag && Objects.nonNull(classificationCurrDO)) {
            throw new CommonException(CommonErrorCodeEnum.VALIDATE_ERROR, "[" + bizCode + "]分类编码已经存在");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delClassificationIncludeChild(Long companyId, Long classificationId) {
        CommClassificationDO classificationCurrDO = this.commClassificationMapper
                .selectById(companyId, classificationId);
        if (Objects.isNull(classificationCurrDO)) {
            throw new CommonException(CommonErrorCodeEnum.VALIDATE_ERROR, "分类不存在");
        }
        List<CommClassificationDO> classificationDOList = this.commClassificationMapper
                .selectIncludeChildById(companyId, classificationId);
        classificationDOList.add(classificationCurrDO);
        List<Long> classificationIds = classificationDOList.stream()
                .map(CommClassificationDO::getId).collect(Collectors.toList());
        BizScenario bizScenario = BizScenario.valueOf(ClassificationScenario.BIZ_ID, ClassificationScenario.USE_CASE_DELETE,
                ClassificationScenario.getDelClassificationScenario(classificationCurrDO.getBizType().intValue()));
        ExtensionCoordinate extensionCoordinate = new ExtensionCoordinate(ClassificationDeleteExtPt.class, bizScenario);
        boolean deleteResult = extensionExecutor.execute(extensionCoordinate,
                extension -> ((ClassificationDeleteExtPt) extension).deleteHandler(companyId, null, classificationIds));
        if (deleteResult) {
            this.classificationRepository.delClassificationByIds(companyId, classificationIds);
        } else {
            throw new CommonException(CommonErrorCodeEnum.VALIDATE_ERROR, "删除分类信息校验不通过");
        }
    }

    @Override
    public void updateEnableStatus(Long companyId, Long userId, Long classificationId, Byte enabledStatus){
        CommClassificationDO classificationCurrDO = this.commClassificationMapper
                .selectById(companyId, classificationId);
        if (Objects.isNull(classificationCurrDO)) {
            throw new CommonException(CommonErrorCodeEnum.VALIDATE_ERROR, "分类不存在");
        }
        List<CommClassificationDO> classificationDOList = this.commClassificationMapper
                .selectIncludeChildById(companyId, classificationId);
        classificationDOList.add(classificationCurrDO);
        List<Long> classificationIds = classificationDOList.stream()
                .map(CommClassificationDO::getId).distinct().collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(classificationIds)){
            this.commClassificationMapper.updateEnableStatusByIds(companyId,userId,enabledStatus,classificationIds);
        }
    }

    private List<ClassificationDTO> buildClassificationTree(List<CommClassificationDO> classificationDOList, Boolean doCount) {
        List<ClassificationDTO> classificationDTOList = this.convertClassificationDTO(classificationDOList);
        if (CollectionUtils.isEmpty(classificationDOList)) {
            return classificationDTOList;
        }
        List<ClassificationDTO> classificationDtoTreeList = new ArrayList<>();
        for (ClassificationDTO node : classificationDTOList) {
            boolean rootFlag = false;
            if (Boolean.TRUE.equals(doCount)) {
            }
            for (ClassificationDTO parentNode : classificationDTOList) {
                if (Objects.nonNull(node.getParentId()) && node.getParentId().equals(parentNode.getClassificationId())) {
                    rootFlag = true;
                    if (parentNode.getChildClassificationList() == null) {
                        parentNode.setChildClassificationList(Lists.newArrayList());
                    }
                    parentNode.getChildClassificationList().add(node);
                    break;
                }
            }
            if (!rootFlag) {
                classificationDtoTreeList.add(node);
            }
        }
        return classificationDtoTreeList;
    }
    private List<ClassificationDTO> convertClassificationDTO(List<CommClassificationDO> classificationDOList) {
        if (CollectionUtils.isEmpty(classificationDOList)) {
            return Lists.newArrayList();
        }
        return classificationDOList.stream().map(classification -> {
            ClassificationDTO classificationDTO = BeanCopyUtils.copyByJSON(classification, ClassificationDTO.class);
            classificationDTO.setClassificationId(classification.getId());
            return classificationDTO;
        }).collect(Collectors.toList());
    }

    @Override
    public List<ClassificationDTO> queryByParentAndName(Long companyId, String name, Long classificationId) {
        List<CommClassificationDO> list = this.commClassificationMapper.selectByParentAndName(companyId, classificationId, name);
        return this.convertClassificationDTO(list);
    }
}
