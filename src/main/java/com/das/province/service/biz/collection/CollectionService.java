package com.das.province.service.biz.collection;

import com.das.province.common.bo.SimplePageInfo;
import com.das.province.service.biz.collection.action.QueryCulturalPageByConditionAction;
import com.das.province.service.biz.collection.dto.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/3/28
 */
public interface CollectionService {

    /**
     * 查询藏品列表
     * @param action action
     * @return 藏品列表列表
     */
    SimplePageInfo<CollectionInfoListDTO> queryCollectionPageByCondition(QueryCulturalPageByConditionAction action);

    List<HolidayDTO> holidayList(Integer year, String museumCode, String regionCode);

    AudiencePortraitAreaDTO audiencePortraitArea(Integer statisticsWay, String startTime, String endTime, String museumCode, String regionCode);

    AudiencePortraitAgeDTO audiencePortraitAge(Integer statisticsWay, String startTime, String endTime, String museumCode, String regionCode);

    List<StatisticsByProvinceDTO> statisticsByProvince(Integer statisticsWay, String startTime, String endTime, String museumCode, String regionCode);

    VisitorStatisticsDTO visitorList(Integer statisticsWay, String startTime, String endTime, String museumCode, String regionCode);

    List<VisitorCityRankDTO> visitorCityRank(Integer statisticsWay, String startTime, String endTime);

    List<VisitorMuseumRankDTO> visitorMuseumRank(Integer statisticsWay, String startTime, String endTime, String regionCode, String keyword, String museumId, Byte orderBy);

    List<TimePeriodStatisticsDTO> timePeriodStatistics(Integer statisticsWay, String startTime, String endTime, String museumCode, String regionCode);

    List<VisitorTendencyDTO> visitorTendency(Integer statisticsWay, String startTime, String endTime, String regionCode, String museumCode);

    SimplePageInfo<VisitorVenueDistributeDTO> visitorVenueDistribute(Integer statisticsWay, String startTime, String endTime, String regionCode, String museumCode,
                                                                     Integer sortField, Integer orderBy, Integer pageNum, Integer pageSize);
}
