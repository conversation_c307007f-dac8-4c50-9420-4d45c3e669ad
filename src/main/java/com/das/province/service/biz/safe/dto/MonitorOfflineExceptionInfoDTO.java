package com.das.province.service.biz.safe.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023/7/11
 */
@Data
public class MonitorOfflineExceptionInfoDTO implements Serializable {

    private static final long serialVersionUID = -6497024590087180017L;

    /**
     * 离线设备数
     */
    private Integer offlineDeviceCount;

    /**
     * 设备离线率
     */
    private String deviceOfflineRatio;

    /**
     * 离线设备数次数
     */
    private Integer deviceOfflineCount;

    /**
     * 设备离线异常率
     */
    private String deviceOfflineExceptionRatio;

}
