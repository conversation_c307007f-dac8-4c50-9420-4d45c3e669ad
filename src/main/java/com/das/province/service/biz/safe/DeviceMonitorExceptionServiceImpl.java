package com.das.province.service.biz.safe;

import com.das.province.common.bo.PageInfoBaseReq;
import com.das.province.common.bo.SimplePageInfo;
import com.das.province.common.exception.CommonErrorCodeEnum;
import com.das.province.common.exception.CommonException;
import com.das.province.common.utils.BeanCopyUtils;
import com.das.province.common.utils.DateUtils;
import com.das.province.common.utils.PaginationUtils;
import com.das.province.infr.dataobject.MuseumBaseInfoDO;
import com.das.province.infr.dataobject.SyncSafeDeviceInfoDO;
import com.das.province.infr.dataobject.SyncSafeTemperatureHumidityDeviceDO;
import com.das.province.infr.mapper.MuseumBaseInfoMapper;
import com.das.province.infr.mapper.SyncSafeDeviceInfoMapper;
import com.das.province.infr.mapper.SyncSafeTemperatureHumidityDeviceMapper;
import com.das.province.service.biz.safe.dto.*;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.javatuples.Pair;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/7/12
 */
@Slf4j
@Service
public class DeviceMonitorExceptionServiceImpl implements DeviceMonitorExceptionService {

    @Resource
    private SyncSafeDeviceInfoMapper syncSafeDeviceInfoMapper;

    @Resource
    private MuseumBaseInfoMapper museumBaseInfoMapper;

    @Resource
    private SyncSafeTemperatureHumidityDeviceMapper syncSafeTemperatureHumidityDeviceMapper;

    @Override
    public DeviceMonitorExceptionInfoDTO getDeviceMonitorExceptionInfo(String museumId, String regionCode,
                                                                       String startTime, String endTime, Integer statisticsWay) {
        Pair<Date, Date> pair = getDate(statisticsWay, startTime, endTime);
        Date timeStart = pair.getValue0();
        Date timeEnd = pair.getValue1();
        List<SyncSafeDeviceInfoDO> safeDeviceInfoDOList = this.syncSafeDeviceInfoMapper.listMonitorException(museumId, regionCode, timeStart, timeEnd);
        if (CollectionUtils.isEmpty(safeDeviceInfoDOList)) {
            return null;
        }
        AtomicInteger exceptionTotalCount= new AtomicInteger();// 异常数
        String exceptionTotalRatio;// 异常率
        AtomicInteger temperatureExceptionTotalCount= new AtomicInteger();// 温度异常数
        AtomicInteger temperatureMildExceptionCount= new AtomicInteger();// 温度异常数(轻度)
        AtomicInteger temperatureModerateExceptionCount= new AtomicInteger();// 温度异常数(中度)
        AtomicInteger temperatureSevereExceptionCount= new AtomicInteger();// 温度异常数(重度)
        String temperatureExceptionRatio;// 温度异常率
        AtomicInteger humidityExceptionTotalCount= new AtomicInteger();// 湿度异常数
        AtomicInteger humidityMildExceptionCount= new AtomicInteger();// 湿度异常数(轻度)
        AtomicInteger humidityModerateExceptionCount= new AtomicInteger();// 湿度异常数(中度)
        AtomicInteger humiditySevereExceptionCount= new AtomicInteger();// 湿度异常数(重度)
        String humidityExceptionRatio;// 湿度异常率
        AtomicInteger deviceOfflineTotalCount= new AtomicInteger();// 设备离线次数
        AtomicInteger offlineDeviceCount= new AtomicInteger();// 离线设备数
        String offlineRatio;// 离线率
        AtomicInteger deviceOnlineCount= new AtomicInteger();// 在线次数
        AtomicInteger onlineDeviceCount= new AtomicInteger();// 在线设备
        String onlineRatio;// 在线率
        String deviceOfflineExceptionRatio;// 离线设备异常率
        Set<String> offlineDeviceSet = new HashSet<>();
        Set<String> onlineDeviceSet = new HashSet<>();
        safeDeviceInfoDOList.forEach(k -> {
            if ("1".equalsIgnoreCase(k.getTemperatureExceptionDegree())) {
                exceptionTotalCount.getAndIncrement();
                temperatureExceptionTotalCount.getAndIncrement();
                temperatureMildExceptionCount.getAndIncrement();
            } else if ("2".equalsIgnoreCase(k.getTemperatureExceptionDegree())) {
                exceptionTotalCount.getAndIncrement();
                temperatureExceptionTotalCount.getAndIncrement();
                temperatureModerateExceptionCount.getAndIncrement();
            } else if ("3".equalsIgnoreCase(k.getTemperatureExceptionDegree())) {
                exceptionTotalCount.getAndIncrement();
                temperatureExceptionTotalCount.getAndIncrement();
                temperatureSevereExceptionCount.getAndIncrement();
            }

            if ("1".equalsIgnoreCase(k.getHumidityExceptionDegree())) {
                exceptionTotalCount.getAndIncrement();
                humidityExceptionTotalCount.getAndIncrement();
                humidityMildExceptionCount.getAndIncrement();
            } else if ("2".equalsIgnoreCase(k.getHumidityExceptionDegree())) {
                exceptionTotalCount.getAndIncrement();
                humidityExceptionTotalCount.getAndIncrement();
                humidityModerateExceptionCount.getAndIncrement();
            } else if ("3".equalsIgnoreCase(k.getHumidityExceptionDegree())) {
                exceptionTotalCount.getAndIncrement();
                humidityExceptionTotalCount.getAndIncrement();
                humiditySevereExceptionCount.getAndIncrement();
            }

            if (0 == k.getDeviceStatus()) {
                exceptionTotalCount.getAndIncrement();
                deviceOfflineTotalCount.getAndIncrement();
                if (!offlineDeviceSet.contains(k.getDeviceCode())) {
                    offlineDeviceCount.getAndIncrement();
                }
                offlineDeviceSet.add(k.getDeviceCode());
            } else {
                deviceOnlineCount.getAndIncrement();
                if (!onlineDeviceSet.contains(k.getDeviceCode())) {
                    onlineDeviceCount.getAndIncrement();
                }
                onlineDeviceSet.add(k.getDeviceCode());
            }
        });
        int total = (2 * safeDeviceInfoDOList.size()) - deviceOfflineTotalCount.get();
        BigDecimal bigDecimal1 = BigDecimal.valueOf(exceptionTotalCount.get()* 100L).divide(BigDecimal.valueOf(total),2,RoundingMode.HALF_UP);
        exceptionTotalRatio =  bigDecimal1 + "%";
        BigDecimal bigDecimal2 = BigDecimal.valueOf(temperatureExceptionTotalCount.get()* 100L).divide(BigDecimal.valueOf(safeDeviceInfoDOList.size() - deviceOfflineTotalCount.get()),2,RoundingMode.HALF_UP);
        temperatureExceptionRatio = bigDecimal2 + "%";
        BigDecimal bigDecimal3 = BigDecimal.valueOf(humidityExceptionTotalCount.get()*100L).divide(BigDecimal.valueOf(safeDeviceInfoDOList.size() - deviceOfflineTotalCount.get()),2,RoundingMode.HALF_UP);
        humidityExceptionRatio = bigDecimal3 + "%";
        BigDecimal bigDecimal4 = BigDecimal.valueOf(offlineDeviceCount.get()*100L).divide(BigDecimal.valueOf(offlineDeviceCount.get() + onlineDeviceCount.get()),2,RoundingMode.HALF_UP);
        offlineRatio = bigDecimal4 + "%";
        BigDecimal bigDecimal5 = BigDecimal.valueOf(onlineDeviceCount.get()*100L).divide(BigDecimal.valueOf(offlineDeviceCount.get() + onlineDeviceCount.get()),2,RoundingMode.HALF_UP);
        onlineRatio = bigDecimal5 + "%";
        BigDecimal bigDecimal6 = BigDecimal.valueOf(deviceOfflineTotalCount.get()*100L).divide(BigDecimal.valueOf(deviceOfflineTotalCount.get() + deviceOnlineCount.get()),2,RoundingMode.HALF_UP);
        deviceOfflineExceptionRatio = bigDecimal6 + "%";
        DeviceMonitorExceptionInfoDTO deviceMonitorExceptionInfoDTO = new DeviceMonitorExceptionInfoDTO();
        deviceMonitorExceptionInfoDTO.setDeviceOfflineExceptionRatio(deviceOfflineExceptionRatio);
        deviceMonitorExceptionInfoDTO.setDeviceOfflineTotalCount(deviceOfflineTotalCount.get());
        deviceMonitorExceptionInfoDTO.setDeviceOnlineCount(deviceOnlineCount.get());
        deviceMonitorExceptionInfoDTO.setExceptionTotalCount(exceptionTotalCount.get());
        deviceMonitorExceptionInfoDTO.setExceptionTotalRatio(exceptionTotalRatio);
        deviceMonitorExceptionInfoDTO.setHumidityExceptionRatio(humidityExceptionRatio);
        deviceMonitorExceptionInfoDTO.setHumidityExceptionTotalCount(humidityExceptionTotalCount.get());
        deviceMonitorExceptionInfoDTO.setHumidityMildExceptionCount(humidityMildExceptionCount.get());
        deviceMonitorExceptionInfoDTO.setHumidityModerateExceptionCount(humidityModerateExceptionCount.get());
        deviceMonitorExceptionInfoDTO.setHumiditySevereExceptionCount(humiditySevereExceptionCount.get());
        deviceMonitorExceptionInfoDTO.setOfflineDeviceCount(offlineDeviceCount.get());
        deviceMonitorExceptionInfoDTO.setOfflineRatio(offlineRatio);
        deviceMonitorExceptionInfoDTO.setOnlineDeviceCount(onlineDeviceCount.get());
        deviceMonitorExceptionInfoDTO.setOnlineRatio(onlineRatio);
        deviceMonitorExceptionInfoDTO.setTemperatureExceptionRatio(temperatureExceptionRatio);
        deviceMonitorExceptionInfoDTO.setTemperatureExceptionTotalCount(temperatureExceptionTotalCount.get());
        deviceMonitorExceptionInfoDTO.setTemperatureMildExceptionCount(temperatureMildExceptionCount.get());
        deviceMonitorExceptionInfoDTO.setTemperatureModerateExceptionCount(temperatureModerateExceptionCount.get());
        deviceMonitorExceptionInfoDTO.setTemperatureSevereExceptionCount(temperatureSevereExceptionCount.get());
        return deviceMonitorExceptionInfoDTO;
    }

    @Override
    public List<DeviceMonitorExceptionTrend> getDeviceMonitorExceptionTrend(String museumId, String regionCode, String startTime, String endTime, Integer statisticsWay) {
        Pair<Date, Date> pair = getDate(statisticsWay, startTime, endTime);
        Date timeStart = pair.getValue0();
        Date timeEnd = pair.getValue1();
        Map<String, DeviceMonitorExceptionTrend> trendMap = Maps.newHashMap();
        if (StringUtils.isBlank(museumId)) {
            List<MuseumBaseInfoDO> museumBaseList = this.museumBaseInfoMapper.selectAll();
            if (CollectionUtils.isEmpty(museumBaseList)) {
                return Lists.newArrayList();
            }
            museumBaseList.forEach(m -> {
                DeviceMonitorExceptionTrend trend = new DeviceMonitorExceptionTrend();
                trend.setMuseumName(m.getMuseumName());
                trend.setMonitorDeviceException(new DeviceMonitorExceptionTrend.DegreeData());
                trend.setTemperatureException(new DeviceMonitorExceptionTrend.DegreeData());
                trend.setHumidityException(new DeviceMonitorExceptionTrend.DegreeData());
                trendMap.put(m.getId(), trend);
            });

        } else {
            List<SyncSafeTemperatureHumidityDeviceDO> deviceDOList = this.syncSafeTemperatureHumidityDeviceMapper.selectByUniqueCode(museumId);
            if (CollectionUtils.isEmpty(deviceDOList)) {
                return Lists.newArrayList();
            }
            deviceDOList.forEach(m -> {
                DeviceMonitorExceptionTrend trend = new DeviceMonitorExceptionTrend();
                trend.setMuseumName(m.getDeviceAddress());
                trend.setDeviceAddress(m.getDeviceAddress());
                trend.setMonitorDeviceException(new DeviceMonitorExceptionTrend.DegreeData());
                trend.setTemperatureException(new DeviceMonitorExceptionTrend.DegreeData());
                trend.setHumidityException(new DeviceMonitorExceptionTrend.DegreeData());
                trendMap.put(m.getDeviceCode(), trend);
            });
        }
        List<SyncSafeDeviceInfoDO> safeDeviceInfoDOList = this.syncSafeDeviceInfoMapper.listMonitorException(museumId, regionCode, timeStart, timeEnd);
        if (CollectionUtils.isEmpty(safeDeviceInfoDOList)) {
            List<DeviceMonitorExceptionTrend> result = new ArrayList<>();
            trendMap.forEach((k, v) -> result.add(v));
            return result;
        }
        safeDeviceInfoDOList.forEach(k -> {
            DeviceMonitorExceptionTrend trend;
            if (StringUtils.isBlank(museumId)) {
                if (!trendMap.containsKey(k.getUniqueCode())) {
                    return;
                }
                trend = trendMap.get(k.getUniqueCode());
            } else {
                if (!trendMap.containsKey(k.getDeviceCode())) {
                    return;
                }
                trend = trendMap.get(k.getDeviceCode());
            }
            DeviceMonitorExceptionTrend.DegreeData temperatureException = trend.getTemperatureException();
            if ("1".equalsIgnoreCase(k.getTemperatureExceptionDegree())) {
                trend.setTemperatureExceptionCount(trend.getTemperatureExceptionCount() + 1);
                trend.setMildExceptionCount(trend.getMildExceptionCount() + 1);
                trend.setMonitorDeviceExceptionCount(trend.getMonitorDeviceExceptionCount() + 1);
                temperatureException.setMildExceptionCount(temperatureException.getMildExceptionCount() + 1);
            } else if ("2".equalsIgnoreCase(k.getTemperatureExceptionDegree())) {
                trend.setTemperatureExceptionCount(trend.getTemperatureExceptionCount() + 1);
                trend.setMonitorDeviceExceptionCount(trend.getMonitorDeviceExceptionCount() + 1);
                trend.setModerateExceptionCount(trend.getModerateExceptionCount() + 1);
                temperatureException.setModerateExceptionCount(temperatureException.getModerateExceptionCount() + 1);
            } else if ("3".equalsIgnoreCase(k.getTemperatureExceptionDegree())) {
                trend.setTemperatureExceptionCount(trend.getTemperatureExceptionCount() + 1);
                trend.setMonitorDeviceExceptionCount(trend.getMonitorDeviceExceptionCount() + 1);
                trend.setSevereExceptionCount(trend.getSevereExceptionCount() + 1);
                temperatureException.setSevereExceptionCount(temperatureException.getSevereExceptionCount() + 1);
            }

            DeviceMonitorExceptionTrend.DegreeData humidityException = trend.getHumidityException();
            if ("1".equalsIgnoreCase(k.getHumidityExceptionDegree())) {
                trend.setHumidityExceptionCount(trend.getHumidityExceptionCount() + 1);
                trend.setMildExceptionCount(trend.getMildExceptionCount() + 1);
                trend.setMonitorDeviceExceptionCount(trend.getMonitorDeviceExceptionCount() + 1);
                humidityException.setMildExceptionCount(humidityException.getMildExceptionCount() + 1);
            } else if ("2".equalsIgnoreCase(k.getHumidityExceptionDegree())) {
                trend.setHumidityExceptionCount(trend.getHumidityExceptionCount() + 1);
                trend.setMonitorDeviceExceptionCount(trend.getMonitorDeviceExceptionCount() + 1);
                trend.setModerateExceptionCount(trend.getModerateExceptionCount() + 1);
                humidityException.setModerateExceptionCount(humidityException.getModerateExceptionCount() + 1);
            } else if ("3".equalsIgnoreCase(k.getHumidityExceptionDegree())) {
                trend.setHumidityExceptionCount(trend.getHumidityExceptionCount() + 1);
                trend.setMonitorDeviceExceptionCount(trend.getMonitorDeviceExceptionCount() + 1);
                trend.setSevereExceptionCount(trend.getSevereExceptionCount() + 1);
                humidityException.setSevereExceptionCount(humidityException.getSevereExceptionCount() + 1);
            }
            if (0 == k.getDeviceStatus()) {
                trend.setDeviceOfflineCount(trend.getDeviceOfflineCount() + 1);
            }
        });
        List<DeviceMonitorExceptionTrend> result = new ArrayList<>();
        trendMap.forEach((k, v) -> {
            DeviceMonitorExceptionTrend.DegreeData temperatureException = v.getTemperatureException();
            Integer temperatureExceptionCount;
            String temperatureExceptionDegree;
            if (temperatureException.getMildExceptionCount() == 0 && temperatureException.getModerateExceptionCount() == 0 && temperatureException.getSevereExceptionCount() == 0) {
                temperatureExceptionCount = 0;
                temperatureExceptionDegree = "0";
            } else {
                if (temperatureException.getMildExceptionCount() >= temperatureException.getModerateExceptionCount()) {
                    if (temperatureException.getMildExceptionCount() >= temperatureException.getSevereExceptionCount()) {
                        temperatureExceptionCount = temperatureException.getMildExceptionCount();
                        temperatureExceptionDegree = "1";
                    } else {
                        temperatureExceptionCount = temperatureException.getSevereExceptionCount();
                        temperatureExceptionDegree = "3";
                    }
                } else {
                    if (temperatureException.getModerateExceptionCount() >= temperatureException.getSevereExceptionCount()) {
                        temperatureExceptionCount = temperatureException.getModerateExceptionCount();
                        temperatureExceptionDegree = "2";
                    } else {
                        temperatureExceptionCount = temperatureException.getSevereExceptionCount();
                        temperatureExceptionDegree = "3";
                    }
                }
            }
            temperatureException.setExceptionDegree(temperatureExceptionDegree);
            temperatureException.setExceptionCount(temperatureExceptionCount);

            DeviceMonitorExceptionTrend.DegreeData humidityException = v.getHumidityException();
            Integer humidityExceptionCount;
            String humidityExceptionDegree;
            if (humidityException.getMildExceptionCount() == 0 && humidityException.getModerateExceptionCount() == 0 && humidityException.getSevereExceptionCount() == 0) {
                humidityExceptionCount = 0;
                humidityExceptionDegree = "0";
            } else {
                if (humidityException.getMildExceptionCount() >= humidityException.getModerateExceptionCount()) {
                    if (humidityException.getMildExceptionCount() >= humidityException.getSevereExceptionCount()) {
                        humidityExceptionCount = humidityException.getMildExceptionCount();
                        humidityExceptionDegree = "1";
                    } else {
                        humidityExceptionCount = humidityException.getSevereExceptionCount();
                        humidityExceptionDegree = "3";
                    }
                } else {
                    if (humidityException.getModerateExceptionCount() >= humidityException.getSevereExceptionCount()) {
                        humidityExceptionCount = humidityException.getModerateExceptionCount();
                        humidityExceptionDegree = "2";
                    } else {
                        humidityExceptionCount = humidityException.getSevereExceptionCount();
                        humidityExceptionDegree = "3";
                    }
                }
            }
            humidityException.setExceptionDegree(humidityExceptionDegree);
            humidityException.setExceptionCount(humidityExceptionCount);

            DeviceMonitorExceptionTrend.DegreeData monitorDeviceException = v.getMonitorDeviceException();
            Integer monitorDeviceCount;
            String monitorDeviceDegree;
            if (v.getMildExceptionCount() == 0 && v.getModerateExceptionCount() == 0 && v.getSevereExceptionCount() == 0) {
                monitorDeviceCount = 0;
                monitorDeviceDegree = "0";
            } else {
                if (v.getMildExceptionCount() >= v.getModerateExceptionCount()) {
                    if (v.getMildExceptionCount() >= v.getSevereExceptionCount()) {
                        monitorDeviceCount = v.getMildExceptionCount();
                        monitorDeviceDegree = "1";
                    } else {
                        monitorDeviceCount = v.getSevereExceptionCount();
                        monitorDeviceDegree = "3";
                    }
                } else {
                    if (v.getModerateExceptionCount() >= v.getSevereExceptionCount()) {
                        monitorDeviceCount = v.getModerateExceptionCount();
                        monitorDeviceDegree = "2";
                    } else {
                        monitorDeviceCount = v.getSevereExceptionCount();
                        monitorDeviceDegree = "3";
                    }
                }
            }
            monitorDeviceException.setExceptionCount(monitorDeviceCount);
            monitorDeviceException.setExceptionDegree(monitorDeviceDegree);
            monitorDeviceException.setMildExceptionCount(v.getMildExceptionCount());
            monitorDeviceException.setModerateExceptionCount(v.getModerateExceptionCount());
            monitorDeviceException.setSevereExceptionCount(v.getSevereExceptionCount());
            result.add(v);
        });
        return result;
    }

    @Override
    public SimplePageInfo<DeviceMonitorThExceptionDTO> pageDeviceMonitorThException(String museumId, String regionCode, String regionName, String deviceAddress,
                                                                                    String exceptionType, String exceptionDegree, String startTime, String endTime,
                                                                                    Integer statisticsWay, Integer pageNum, Integer pageSize) {
        Pair<Date, Date> pair = getDate(statisticsWay, startTime, endTime);
        Date timeStart = pair.getValue0();
        Date timeEnd = pair.getValue1();
        List<SyncSafeDeviceInfoDO> syncSafeDeviceInfoDOList = this.syncSafeDeviceInfoMapper
                .listDeviceMonitorThException(museumId, regionCode, regionName, deviceAddress,
                        "1", exceptionDegree, timeStart, timeEnd, Objects.isNull(exceptionType) ? "1" : exceptionType);
        if (CollectionUtils.isEmpty(syncSafeDeviceInfoDOList)) {
            return new SimplePageInfo<>();
        }
        List<String> uniqueCodes = syncSafeDeviceInfoDOList.stream().map(SyncSafeDeviceInfoDO::getUniqueCode).distinct().collect(Collectors.toList());
        List<MuseumBaseInfoDO> museumBaseInfoDOList = this.museumBaseInfoMapper.selectListByIds(uniqueCodes);
        Map<String, String> museumMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(museumBaseInfoDOList)) {
            museumMap = museumBaseInfoDOList.stream().collect(Collectors.toMap(MuseumBaseInfoDO::getId,
                    MuseumBaseInfoDO::getMuseumName, (key1, key2) -> key2));
        }
        List<DeviceMonitorThExceptionDTO> result = Lists.newArrayList();
        Map<String, String> finalMuseumMap = museumMap;
        syncSafeDeviceInfoDOList.forEach(k -> {
            if (!"0".equalsIgnoreCase(k.getHumidityExceptionDegree())) {
                DeviceMonitorThExceptionDTO deviceMonitorThExceptionDTO = BeanCopyUtils.copyByJSON(k, DeviceMonitorThExceptionDTO.class);
                deviceMonitorThExceptionDTO.setExceptionType("2");
                deviceMonitorThExceptionDTO.setThValue(k.getHumidity());
                deviceMonitorThExceptionDTO.setMinTh(k.getMinHumidity());
                deviceMonitorThExceptionDTO.setMaxTh(k.getMaxHumidity());
                deviceMonitorThExceptionDTO.setExceptionDegree(k.getHumidityExceptionDegree());
                if (finalMuseumMap.containsKey(k.getUniqueCode())) {
                    deviceMonitorThExceptionDTO.setMuseumName(finalMuseumMap.get(k.getUniqueCode()));
                }
                if (StringUtils.isNotBlank(exceptionType)) {
                    if ("2".equalsIgnoreCase(exceptionType)) {
                        if (StringUtils.isBlank(exceptionDegree)) {
                            result.add(deviceMonitorThExceptionDTO);
                        } else {
                            if (exceptionDegree.equalsIgnoreCase(k.getHumidityExceptionDegree())) {
                                result.add(deviceMonitorThExceptionDTO);
                            }
                        }
                    }
                } else {
                    if (StringUtils.isBlank(exceptionDegree)) {
                        result.add(deviceMonitorThExceptionDTO);
                    } else {
                        if (exceptionDegree.equalsIgnoreCase(k.getHumidityExceptionDegree())) {
                            result.add(deviceMonitorThExceptionDTO);
                        }
                    }
                }
            }
            if (!"0".equalsIgnoreCase(k.getTemperatureExceptionDegree())) {
                DeviceMonitorThExceptionDTO deviceMonitorThExceptionDTO = BeanCopyUtils.copyByJSON(k, DeviceMonitorThExceptionDTO.class);
                deviceMonitorThExceptionDTO.setExceptionType("1");
                deviceMonitorThExceptionDTO.setThValue(k.getTemperature());
                deviceMonitorThExceptionDTO.setMinTh(k.getMinTemperature());
                deviceMonitorThExceptionDTO.setMaxTh(k.getMaxTemperature());
                deviceMonitorThExceptionDTO.setExceptionDegree(k.getTemperatureExceptionDegree());
                if (finalMuseumMap.containsKey(k.getUniqueCode())) {
                    deviceMonitorThExceptionDTO.setMuseumName(finalMuseumMap.get(k.getUniqueCode()));
                }
                if (StringUtils.isNotBlank(exceptionType)) {
                    if ("1".equalsIgnoreCase(exceptionType)) {
                        if (StringUtils.isBlank(exceptionDegree)) {
                            result.add(deviceMonitorThExceptionDTO);
                        } else {
                            if (exceptionDegree.equalsIgnoreCase(k.getTemperatureExceptionDegree())) {
                                result.add(deviceMonitorThExceptionDTO);
                            }
                        }
                    }
                } else {
                    if (StringUtils.isBlank(exceptionDegree)) {
                        result.add(deviceMonitorThExceptionDTO);
                    } else {
                        if (exceptionDegree.equalsIgnoreCase(k.getTemperatureExceptionDegree())) {
                            result.add(deviceMonitorThExceptionDTO);
                        }
                    }
                }
            }
        });
        return PaginationUtils.pagination(result, pageNum, pageSize);
    }

    @Override
    public MonitorThExceptionInfoDTO getMonitorThExceptionInfo(String museumId, String regionCode, String regionName, String deviceAddress,
                                                               String exceptionType, String exceptionDegree, String startTime, String endTime, Integer statisticsWay) {
        Pair<Date, Date> pair = getDate(statisticsWay, startTime, endTime);
        Date timeStart = pair.getValue0();
        Date timeEnd = pair.getValue1();
        List<SyncSafeDeviceInfoDO> syncSafeDeviceInfoDOList = this.syncSafeDeviceInfoMapper
                .listDeviceMonitorThException(museumId, regionCode, regionName, deviceAddress,
                        "1", exceptionDegree, timeStart, timeEnd, Objects.isNull(exceptionType) ? "1" : exceptionType);
        if (CollectionUtils.isEmpty(syncSafeDeviceInfoDOList)) {
            return null;
        }
        AtomicInteger thExceptionCount = new AtomicInteger();
        AtomicInteger temperatureExceptionCount = new AtomicInteger();
        AtomicInteger humidityExceptionCount = new AtomicInteger();
        syncSafeDeviceInfoDOList.forEach(k -> {
            if (!"0".equalsIgnoreCase(k.getHumidityExceptionDegree())) {
                if (StringUtils.isNotBlank(exceptionType)) {
                    if ("2".equalsIgnoreCase(exceptionType)) {
                        if (StringUtils.isBlank(exceptionDegree)) {
                            thExceptionCount.getAndIncrement();
                            humidityExceptionCount.getAndIncrement();
                        } else {
                            if (exceptionDegree.equalsIgnoreCase(k.getHumidityExceptionDegree())) {
                                thExceptionCount.getAndIncrement();
                                humidityExceptionCount.getAndIncrement();
                            }
                        }
                    }
                } else {
                    if (StringUtils.isBlank(exceptionDegree)) {
                        thExceptionCount.getAndIncrement();
                        humidityExceptionCount.getAndIncrement();
                    } else {
                        if (exceptionDegree.equalsIgnoreCase(k.getHumidityExceptionDegree())) {
                            thExceptionCount.getAndIncrement();
                            humidityExceptionCount.getAndIncrement();
                        }
                    }
                }
            }
            if (!"0".equalsIgnoreCase(k.getTemperatureExceptionDegree())) {
                if (StringUtils.isNotBlank(exceptionType)) {
                    if ("1".equalsIgnoreCase(exceptionType)) {
                        if (StringUtils.isBlank(exceptionDegree)) {
                            thExceptionCount.getAndIncrement();
                            temperatureExceptionCount.getAndIncrement();
                        } else {
                            if (exceptionDegree.equalsIgnoreCase(k.getTemperatureExceptionDegree())) {
                                thExceptionCount.getAndIncrement();
                                temperatureExceptionCount.getAndIncrement();
                            }
                        }
                    }
                } else {
                    if (StringUtils.isBlank(exceptionDegree)) {
                        thExceptionCount.getAndIncrement();
                        temperatureExceptionCount.getAndIncrement();
                    } else {
                        if (exceptionDegree.equalsIgnoreCase(k.getTemperatureExceptionDegree())) {
                            thExceptionCount.getAndIncrement();
                            temperatureExceptionCount.getAndIncrement();
                        }
                    }
                }
            }
        });
        MonitorThExceptionInfoDTO monitorThExceptionInfoDTO = new MonitorThExceptionInfoDTO();
        monitorThExceptionInfoDTO.setThExceptionCount(thExceptionCount.get());
        monitorThExceptionInfoDTO.setHumidityExceptionCount(humidityExceptionCount.get());
        monitorThExceptionInfoDTO.setTemperatureExceptionCount(temperatureExceptionCount.get());
        return monitorThExceptionInfoDTO;
    }

    @Override
    public SimplePageInfo<MonitorOfflineExceptionDTO> pageMonitorOfflineExceptionList(String museumId, String regionCode, String regionName, String deviceAddress,
                                                                                      String exceptionDegree, String startTime, String endTime, Integer statisticsWay,
                                                                                      Integer pageNum, Integer pageSize) {
        Pair<Date, Date> pair = getDate(statisticsWay, startTime, endTime);
        Date timeStart = pair.getValue0();
        Date timeEnd = pair.getValue1();
        List<SyncSafeDeviceInfoDO> syncSafeDeviceInfoDOList = this.syncSafeDeviceInfoMapper
                .listMonitorOfflineException(museumId, regionCode, regionName, deviceAddress, exceptionDegree, timeStart, timeEnd);
        if (CollectionUtils.isEmpty(syncSafeDeviceInfoDOList)) {
            return new SimplePageInfo<>();
        }
        List<MonitorOfflineExceptionDTO> result = Lists.newArrayList();
        Set<String> museumSet = new HashSet<>();
        Map<String, List<Date>> reportMap = Maps.newHashMap();
        List<String> uniqueCodes = Lists.newArrayList();
        syncSafeDeviceInfoDOList.forEach(k -> {
            if (museumSet.contains(k.getDeviceCode())) {
                if (reportMap.containsKey(k.getDeviceCode())) {
                    reportMap.get(k.getDeviceCode()).add(k.getReportTime());
                } else {
                    reportMap.put(k.getDeviceCode(), Lists.newArrayList(k.getReportTime()));
                }
            } else {
                uniqueCodes.add(k.getUniqueCode());
                museumSet.add(k.getUniqueCode());
                MonitorOfflineExceptionDTO monitorOfflineExceptionDTO = BeanCopyUtils.copyByJSON(k, MonitorOfflineExceptionDTO.class);
                monitorOfflineExceptionDTO.setCurrReportTime(k.getReportTime());
                monitorOfflineExceptionDTO.setDeviceStatus((byte) 0);
                monitorOfflineExceptionDTO.setDeviceOfflineCount(1);
                result.add(monitorOfflineExceptionDTO);
            }
        });
        List<MuseumBaseInfoDO> museumBaseInfoDOList = this.museumBaseInfoMapper.selectListByIds(uniqueCodes);
        Map<String, String> museumMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(museumBaseInfoDOList)) {
            museumMap = museumBaseInfoDOList.stream().collect(Collectors.toMap(MuseumBaseInfoDO::getId,
                    MuseumBaseInfoDO::getMuseumName, (key1, key2) -> key2));
        }
        Map<String, String> finalMuseumMap = museumMap;
        result.forEach(k -> {
            if (finalMuseumMap.containsKey(k.getUniqueCode())) {
                k.setMuseumName(finalMuseumMap.get(k.getUniqueCode()));
            }
            if (reportMap.containsKey(k.getDeviceCode())) {
                k.setReportTimeList(reportMap.get(k.getDeviceCode()));
                k.setDeviceOfflineCount(1 + reportMap.get(k.getDeviceCode()).size());
            } else {
                k.setReportTimeList(Lists.newArrayList());
            }
        });
        return PaginationUtils.pagination(result, pageNum, pageSize);
    }

    @Override
    public MonitorOfflineExceptionInfoDTO getMonitorOfflineExceptionInfo(String museumId, String regionCode, String regionName, String deviceAddress, String exceptionDegree,
                                                                         String startTime, String endTime, Integer statisticsWay) {
        Pair<Date, Date> pair = getDate(statisticsWay, startTime, endTime);
        Date timeStart = pair.getValue0();
        Date timeEnd = pair.getValue1();
        List<SyncSafeDeviceInfoDO> syncSafeDeviceInfoDOList = this.syncSafeDeviceInfoMapper
                .listMonitorOfflineException(museumId, regionCode, regionName, deviceAddress, exceptionDegree, timeStart, timeEnd);
        if (CollectionUtils.isEmpty(syncSafeDeviceInfoDOList)) {
            return null;
        }
        AtomicInteger offlineDeviceCount = new AtomicInteger();
        int deviceOfflineCount = syncSafeDeviceInfoDOList.size();
        Set<String> museumSet = new HashSet<>();
        syncSafeDeviceInfoDOList.forEach(k -> {
            if (!museumSet.contains(k.getDeviceCode())) {
                offlineDeviceCount.getAndIncrement();
                museumSet.add(k.getDeviceCode());
            }
        });
        List<SyncSafeDeviceInfoDO> syncOnlineSafeDeviceInfoDOList = this.syncSafeDeviceInfoMapper
                .listMonitorOnlineException(museumId, regionCode, regionName, deviceAddress, exceptionDegree, timeStart, timeEnd);
        MonitorOfflineExceptionInfoDTO monitorOfflineExceptionInfoDTO = new MonitorOfflineExceptionInfoDTO();
        if (CollectionUtils.isNotEmpty(syncOnlineSafeDeviceInfoDOList)) {
            syncOnlineSafeDeviceInfoDOList.forEach(k -> {
                museumSet.add(k.getDeviceCode());
            });
        }
        monitorOfflineExceptionInfoDTO.setOfflineDeviceCount(offlineDeviceCount.get());
        monitorOfflineExceptionInfoDTO.setDeviceOfflineCount(deviceOfflineCount);
        double bigDecimal1 = Math.round(offlineDeviceCount.get()*100/museumSet.size());
        monitorOfflineExceptionInfoDTO.setDeviceOfflineRatio(bigDecimal1 + "%");
        double bigDecimal2 = Math.round(deviceOfflineCount*100/(deviceOfflineCount + syncOnlineSafeDeviceInfoDOList.size()));
        monitorOfflineExceptionInfoDTO.setDeviceOfflineExceptionRatio(bigDecimal2 + "%");
        return monitorOfflineExceptionInfoDTO;
    }

    @Override
    public List<DeviceMonitorThExceptionDTO> monitorThExceptionExport(String museumId, String regionCode, String regionName, String deviceAddress, String exceptionType, String exceptionDegree, String startTime, String endTime, Integer statisticsWay) {
        Pair<Date, Date> pair = getDate(statisticsWay, startTime, endTime);
        Date timeStart = pair.getValue0();
        Date timeEnd = pair.getValue1();
        List<SyncSafeDeviceInfoDO> syncSafeDeviceInfoDOList = this.syncSafeDeviceInfoMapper
                .listDeviceMonitorThException(museumId, regionCode, regionName, deviceAddress,
                        "1", exceptionDegree, timeStart, timeEnd, Objects.isNull(exceptionType) ? "1" : exceptionType);
        if (CollectionUtils.isEmpty(syncSafeDeviceInfoDOList)) {
            return Lists.newArrayList();
        }
        List<DeviceMonitorThExceptionDTO> result = Lists.newArrayList();
        List<String> uniqueCodes = Lists.newArrayList();
        syncSafeDeviceInfoDOList.forEach(k -> {
            uniqueCodes.add(k.getUniqueCode());
            if (!"0".equalsIgnoreCase(k.getHumidityExceptionDegree())) {
                DeviceMonitorThExceptionDTO deviceMonitorThExceptionDTO = BeanCopyUtils.copyByJSON(k, DeviceMonitorThExceptionDTO.class);
                deviceMonitorThExceptionDTO.setExceptionType("2");
                deviceMonitorThExceptionDTO.setExceptionTypeStr("湿度异常");
                deviceMonitorThExceptionDTO.setThLen(" " + k.getMinHumidity() + "%RH~" + k.getMaxHumidity() + "%RH");
                deviceMonitorThExceptionDTO.setThValue(k.getHumidity());
                if ("1".equalsIgnoreCase(k.getHumidityExceptionDegree())) {
                    deviceMonitorThExceptionDTO.setExceptionDegreeStr("轻度");
                } else if ("2".equalsIgnoreCase(k.getHumidityExceptionDegree())) {
                    deviceMonitorThExceptionDTO.setExceptionDegreeStr("中度");
                } else if ("3".equalsIgnoreCase(k.getHumidityExceptionDegree())) {
                    deviceMonitorThExceptionDTO.setExceptionDegreeStr("重度");
                }
                deviceMonitorThExceptionDTO.setReportTimeStr(DateUtils.formatDateYYYMMDDHHmmss(k.getReportTime()));
                if (StringUtils.isNotBlank(exceptionType)) {
                    if ("2".equalsIgnoreCase(exceptionType)) {
                        if (StringUtils.isBlank(exceptionDegree)) {
                            result.add(deviceMonitorThExceptionDTO);
                        } else {
                            if (exceptionDegree.equalsIgnoreCase(k.getHumidityExceptionDegree())) {
                                result.add(deviceMonitorThExceptionDTO);
                            }
                        }
                    }
                } else {
                    if (StringUtils.isBlank(exceptionDegree)) {
                        result.add(deviceMonitorThExceptionDTO);
                    } else {
                        if (exceptionDegree.equalsIgnoreCase(k.getHumidityExceptionDegree())) {
                            result.add(deviceMonitorThExceptionDTO);
                        }
                    }
                }
            }
            if (!"0".equalsIgnoreCase(k.getTemperatureExceptionDegree())) {
                DeviceMonitorThExceptionDTO deviceMonitorThExceptionDTO = BeanCopyUtils.copyByJSON(k, DeviceMonitorThExceptionDTO.class);
                deviceMonitorThExceptionDTO.setExceptionType("1");
                deviceMonitorThExceptionDTO.setExceptionTypeStr("湿度异常");
                deviceMonitorThExceptionDTO.setThLen(" " + k.getMinTemperature() + "~" + k.getMaxTemperature());
                if ("1".equalsIgnoreCase(k.getTemperatureExceptionDegree())) {
                    deviceMonitorThExceptionDTO.setExceptionDegreeStr("轻度");
                } else if ("2".equalsIgnoreCase(k.getTemperatureExceptionDegree())) {
                    deviceMonitorThExceptionDTO.setExceptionDegreeStr("中度");
                } else if ("3".equalsIgnoreCase(k.getTemperatureExceptionDegree())) {
                    deviceMonitorThExceptionDTO.setExceptionDegreeStr("重度");
                }
                deviceMonitorThExceptionDTO.setReportTimeStr(DateUtils.formatDateYYYMMDDHHmmss(k.getReportTime()));
                if (StringUtils.isNotBlank(exceptionType)) {
                    if ("1".equalsIgnoreCase(exceptionType)) {
                        if (StringUtils.isBlank(exceptionDegree)) {
                            result.add(deviceMonitorThExceptionDTO);
                        } else {
                            if (exceptionDegree.equalsIgnoreCase(k.getTemperatureExceptionDegree())) {
                                result.add(deviceMonitorThExceptionDTO);
                            }
                        }
                    }
                } else {
                    if (StringUtils.isBlank(exceptionDegree)) {
                        result.add(deviceMonitorThExceptionDTO);
                    } else {
                        if (exceptionDegree.equalsIgnoreCase(k.getTemperatureExceptionDegree())) {
                            result.add(deviceMonitorThExceptionDTO);
                        }
                    }
                }
            }
        });
        List<MuseumBaseInfoDO> museumBaseInfoDOList = this.museumBaseInfoMapper.selectListByIds(uniqueCodes);
        Map<String, String> museumMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(museumBaseInfoDOList)) {
            museumMap = museumBaseInfoDOList.stream().collect(Collectors.toMap(MuseumBaseInfoDO::getId,
                    MuseumBaseInfoDO::getMuseumName, (key1, key2) -> key2));
        }
        Map<String, String> finalMuseumMap = museumMap;
        result.forEach(k -> {
            k.setRegionNameStr(Objects.nonNull(k.getRegionName()) && 1 == k.getRegionName()
                    ? "展厅" : (Objects.nonNull(k.getRegionName()) && 2 == k.getRegionName() ? "库房" : "其他"));
            if (finalMuseumMap.containsKey(k.getUniqueCode())) {
                k.setMuseumName(finalMuseumMap.get(k.getUniqueCode()));
            }
        });
        return result;
    }

    @Override
    public List<MonitorOfflineExceptionDTO> monitorOfflineExceptionExport(String museumId, String regionCode, String regionName, String deviceAddress, String exceptionDegree, String startTime, String endTime, Integer statisticsWay) {
        Pair<Date, Date> pair = getDate(statisticsWay, startTime, endTime);
        Date timeStart = pair.getValue0();
        Date timeEnd = pair.getValue1();
        List<SyncSafeDeviceInfoDO> syncSafeDeviceInfoDOList = this.syncSafeDeviceInfoMapper
                .listMonitorOfflineException(museumId, regionCode, regionName, deviceAddress, exceptionDegree, timeStart, timeEnd);
        if (CollectionUtils.isEmpty(syncSafeDeviceInfoDOList)) {
            return Lists.newArrayList();
        }
        List<MonitorOfflineExceptionDTO> result = Lists.newArrayList();
        Set<String> museumSet = new HashSet<>();
        Map<String, List<Date>> reportMap = Maps.newHashMap();
        List<String> uniqueCodes = Lists.newArrayList();
        syncSafeDeviceInfoDOList.forEach(k -> {
            if (museumSet.contains(k.getDeviceCode())) {
                if (reportMap.containsKey(k.getDeviceCode())) {
                    reportMap.get(k.getDeviceCode()).add(k.getReportTime());
                } else {
                    reportMap.put(k.getDeviceCode(), Lists.newArrayList(k.getReportTime()));
                }
            } else {
                uniqueCodes.add(k.getUniqueCode());
                museumSet.add(k.getUniqueCode());
                MonitorOfflineExceptionDTO monitorOfflineExceptionDTO = BeanCopyUtils.copyByJSON(k, MonitorOfflineExceptionDTO.class);
                monitorOfflineExceptionDTO.setCurrReportTime(k.getReportTime());
                monitorOfflineExceptionDTO.setDeviceStatus((byte) 0);
                monitorOfflineExceptionDTO.setDeviceOfflineCount(1);
                result.add(monitorOfflineExceptionDTO);
            }
        });
        List<MuseumBaseInfoDO> museumBaseInfoDOList = this.museumBaseInfoMapper.selectListByIds(uniqueCodes);
        Map<String, String> museumMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(museumBaseInfoDOList)) {
            museumMap = museumBaseInfoDOList.stream().collect(Collectors.toMap(MuseumBaseInfoDO::getId,
                    MuseumBaseInfoDO::getMuseumName, (key1, key2) -> key2));
        }
        Map<String, String> finalMuseumMap = museumMap;
        result.forEach(k -> {
            k.setRegionNameStr(Objects.nonNull(k.getRegionName()) && 1 == k.getRegionName()
                    ? "展厅" : (Objects.nonNull(k.getRegionName()) && 2 == k.getRegionName() ? "库房" : "其他"));
            k.setDeviceStatusStr(k.getDeviceStatus() == 1 ? "在线" : "离线");
            k.setCurrReportTimeStr(DateUtils.formatDateYYYMMDDHHmmss(k.getCurrReportTime()));
            if (finalMuseumMap.containsKey(k.getUniqueCode())) {
                k.setMuseumName(finalMuseumMap.get(k.getUniqueCode()));
            }
            if (reportMap.containsKey(k.getDeviceCode())) {
                k.setReportTimeList(reportMap.get(k.getDeviceCode()));
                k.setDeviceOfflineCount(1 + reportMap.get(k.getDeviceCode()).size());
            } else {
                k.setReportTimeList(Lists.newArrayList());
            }
        });
        return result;
    }

    private Pair<Date, Date> getDate(Integer statisticsWay, String startTime, String endTime) {
        Date timeStart;
        Date timeEnd;
        //根据类型获取不同维度的统计数据
        if (statisticsWay == 0) {
            timeStart = DateUtils.getTimesDayMorning();
            timeEnd = new Date();
        } else if (statisticsWay == 1) {
            // 近一周
            Calendar calendar = DateUtils.getDateOffset(-7);
            DateUtils.setHourMinuteSecondMillisecondToZero(calendar);
            timeStart = calendar.getTime();
            timeEnd = new Date();
        } else if (statisticsWay == 2) {
            // 近一月
            Calendar calendar = DateUtils.getDateOffset(-30);
            DateUtils.setHourMinuteSecondMillisecondToZero(calendar);
            timeStart = calendar.getTime();
            timeEnd = new Date();
        } else if (statisticsWay == 3) {
            // 近一年
            Calendar calendar = DateUtils.getDateOffset(-365);
            DateUtils.setHourMinuteSecondMillisecondToZero(calendar);
            timeStart = calendar.getTime();
            timeEnd = new Date();
        } else {
            if (startTime == null) {
                throw new CommonException(CommonErrorCodeEnum.OBJECT_NOT_EXIST, "startTime 不能为空！");
            }
            if (endTime == null) {
                throw new CommonException(CommonErrorCodeEnum.OBJECT_NOT_EXIST, "endTime 不能为空！");
            }
            //自定义时间
            timeStart = DateUtils.parseDateYYYMMDDHHmmss(startTime);
            timeEnd = DateUtils.parseDateYYYMMDDHHmmss(endTime);
        }
        return new Pair<>(timeStart, timeEnd);
    }
}
