package com.das.province.service.biz.user.action;

import com.das.province.service.enums.FunctionStatusEnum;
import com.das.province.service.enums.PermissionTypeEnum;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class EditPermissionByIdAction implements Serializable {
    private static final long serialVersionUID = 7538083299191555923L;
    private Long permissionId;
    private Long companyId;
    private String permissionName;
    private String permissionCode;
    private PermissionTypeEnum permissionType;
    private Byte sortIndex;
    private FunctionStatusEnum functionStatus;
    private String resourceUrl;
    private String resourceIcon;
    private Long modifier;
    private List<SaveFunctionPermissionAction> functionPermissionList;
}
