package com.das.province.service.biz.museum;

import com.das.province.common.bo.SimplePageInfo;
import com.das.province.service.biz.museum.action.AddMuseumAction;
import com.das.province.service.biz.museum.action.EditMuseumAction;
import com.das.province.service.biz.museum.action.QueryMuseumPageAction;
import com.das.province.web.controller.museum.response.MuseumPageVO;

import java.util.List;

/**
 * 博物馆管理
 */

public interface MuseumManageService {

    /**
     * 分页查询
     * @param action
     * @return
     */
    SimplePageInfo<MuseumPageVO>  queryPageByCondition(QueryMuseumPageAction action);

    /**
     * 新增博物馆信息
     * @param addMuseumAction
     * @return
     */
    String addMuseum(AddMuseumAction addMuseumAction);

    /**
     * 编辑博物馆信息
     * @param editMuseumAction
     */
    void editMuseum(EditMuseumAction editMuseumAction);

    /**
     * 删除博物馆信息
     * @param museumId
     */
    void delMuseumById(String museumId);

    List<MuseumPageVO> getByRegionCode(String regionCode);

    List<MuseumPageVO> getByUniqueCodes(List<String> uniqueCodes);
}
