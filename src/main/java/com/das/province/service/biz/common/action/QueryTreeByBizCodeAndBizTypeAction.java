package com.das.province.service.biz.common.action;

import com.das.province.service.enums.ClassificationBizTypeEnum;
import lombok.Data;

import java.io.Serializable;

@Data
public class QueryTreeByBizCodeAndBizTypeAction implements Serializable {
    private static final long serialVersionUID = -7652556543383089064L;
    private Long companyId;
    private String bizCode;
    private ClassificationBizTypeEnum bizType;
}
