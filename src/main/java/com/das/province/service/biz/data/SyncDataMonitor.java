package com.das.province.service.biz.data;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 同步数据监控工具
 * 用于监控数据处理状态和性能
 */
@Slf4j
@Component
public class SyncDataMonitor {

    @Resource
    private ProvinceSyncDataServiceImpl provinceSyncDataService;

    // 统计计数器
    private final AtomicLong totalReceived = new AtomicLong(0);
    private final AtomicLong totalProcessed = new AtomicLong(0);
    private final AtomicLong totalFailed = new AtomicLong(0);

    /**
     * 记录接收到的数据
     */
    public void recordReceived() {
        totalReceived.incrementAndGet();
    }

    /**
     * 记录处理成功的数据
     */
    public void recordProcessed() {
        totalProcessed.incrementAndGet();
    }

    /**
     * 记录处理失败的数据
     */
    public void recordFailed() {
        totalFailed.incrementAndGet();
    }

    /**
     * 获取当前队列大小
     */
    public int getCurrentQueueSize() {
        return provinceSyncDataService.getCurrentTaskSize();
    }

    /**
     * 获取统计信息
     */
    public String getStatistics() {
        return String.format(
                "数据处理统计 - 总接收: %d, 总处理: %d, 总失败: %d, 当前队列: %d",
                totalReceived.get(),
                totalProcessed.get(),
                totalFailed.get(),
                getCurrentQueueSize()
        );
    }

    /**
     * 打印详细统计信息
     */
    public void printDetailedStatistics() {
        long received = totalReceived.get();
        long processed = totalProcessed.get();
        long failed = totalFailed.get();
        int queueSize = getCurrentQueueSize();

        log.info("=== 数据同步监控统计 ===");
        log.info("总接收数据量: {}", received);
        log.info("总处理成功数量: {}", processed);
        log.info("总处理失败数量: {}", failed);
        log.info("当前队列大小: {}", queueSize);

        if (received > 0) {
            double successRate = (double) processed / received * 100;
            double failureRate = (double) failed / received * 100;
            log.info("处理成功率: {:.2f}%", successRate);
            log.info("处理失败率: {:.2f}%", failureRate);
        }

        if (queueSize > 0) {
            log.warn("队列中还有 {} 个任务待处理", queueSize);
        }

        log.info("========================");
    }

    /**
     * 重置统计计数器
     */
    public void resetCounters() {
        totalReceived.set(0);
        totalProcessed.set(0);
        totalFailed.set(0);
        log.info("统计计数器已重置");
    }

    /**
     * 检查系统健康状态
     */
    public boolean isHealthy() {
        int queueSize = getCurrentQueueSize();
        long received = totalReceived.get();
        long processed = totalProcessed.get();

        // 如果队列积压过多，认为不健康
        if (queueSize > 1000) {
            log.warn("队列积压过多，系统可能不健康，当前队列大小: {}", queueSize);
            return false;
        }

        // 如果失败率过高，认为不健康
        if (received > 100) { // 至少有100个样本
            double failureRate = (double) totalFailed.get() / received;
            if (failureRate > 0.1) { // 失败率超过10%
                log.warn("处理失败率过高，系统可能不健康，失败率: {:.2f}%", failureRate * 100);
                return false;
            }
        }

        return true;
    }

    /**
     * 获取性能指标
     */
    public PerformanceMetrics getPerformanceMetrics() {
        return new PerformanceMetrics(
                totalReceived.get(),
                totalProcessed.get(),
                totalFailed.get(),
                getCurrentQueueSize()
        );
    }

    /**
     * 性能指标数据类
     */
    public static class PerformanceMetrics {
        private final long totalReceived;
        private final long totalProcessed;
        private final long totalFailed;
        private final int currentQueueSize;

        public PerformanceMetrics(long totalReceived, long totalProcessed, long totalFailed, int currentQueueSize) {
            this.totalReceived = totalReceived;
            this.totalProcessed = totalProcessed;
            this.totalFailed = totalFailed;
            this.currentQueueSize = currentQueueSize;
        }

        public long getTotalReceived() {
            return totalReceived;
        }

        public long getTotalProcessed() {
            return totalProcessed;
        }

        public long getTotalFailed() {
            return totalFailed;
        }

        public int getCurrentQueueSize() {
            return currentQueueSize;
        }

        public double getSuccessRate() {
            return totalReceived > 0 ? (double) totalProcessed / totalReceived * 100 : 0;
        }

        public double getFailureRate() {
            return totalReceived > 0 ? (double) totalFailed / totalReceived * 100 : 0;
        }

        @Override
        public String toString() {
            return String.format(
                    "PerformanceMetrics{received=%d, processed=%d, failed=%d, queueSize=%d, successRate=%.2f%%, failureRate=%.2f%%}",
                    totalReceived, totalProcessed, totalFailed, currentQueueSize, getSuccessRate(), getFailureRate()
            );
        }
    }
}
