package com.das.province.service.biz.safe;

import com.das.province.common.bo.PageInfoBaseReq;
import com.das.province.common.bo.SimplePageInfo;
import com.das.province.common.exception.CommonErrorCodeEnum;
import com.das.province.common.exception.CommonException;
import com.das.province.common.utils.BeanCopyUtils;
import com.das.province.common.utils.DateUtils;
import com.das.province.common.utils.PaginationUtils;
import com.das.province.common.utils.SimplePageInfoUtils;
import com.das.province.infr.dataobject.MuseumBaseInfoDO;
import com.das.province.infr.dataobject.SyncSafeDeviceInfoDO;
import com.das.province.infr.dataobject.SyncSafeDeviceInfoRealtimeDO;
import com.das.province.infr.dataobject.SyncSafeTemperatureHumidityDeviceDO;
import com.das.province.infr.mapper.MuseumBaseInfoMapper;
import com.das.province.infr.mapper.SyncSafeDeviceInfoMapper;
import com.das.province.infr.mapper.SyncSafeDeviceInfoRealtimeMapper;
import com.das.province.infr.mapper.SyncSafeTemperatureHumidityDeviceMapper;
import com.das.province.service.biz.museum.MuseumManageService;
import com.das.province.service.biz.safe.dto.*;
import com.das.province.web.controller.museum.response.MuseumPageVO;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.LoadingCache;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.javatuples.Pair;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/7/12
 */
@Slf4j
@Service
public class DeviceMonitorServiceImpl implements DeviceMonitorService {

    @Resource
    private MuseumManageService museumManageService;

    @Resource
    private SyncSafeDeviceInfoMapper syncSafeDeviceInfoMapper;

    @Resource
    private SyncSafeDeviceInfoRealtimeMapper syncSafeDeviceInfoRealtimeMapper;

    @Resource
    private MuseumBaseInfoMapper museumBaseInfoMapper;

    @Resource
    private SyncSafeTemperatureHumidityDeviceMapper syncSafeTemperatureHumidityDeviceMapper;

    /**
     * 最大本地缓存key数量
     */
    private static final Integer MAXI_MUM_SIZE = 50000;

    /**
     * 本地缓存时间
     */
    private static final long EXPIRE_TIME = 5*60*1000;

    /**
     * 本地缓存
     */
//    private LoadingCache<String, List<SafeDeviceInfoDTO>> DEVICE_INFO_SYNC_CACHE = Caffeine.newBuilder()
//            // 缓存最大值
//            .maximumSize(MAXI_MUM_SIZE)
//            // 本地缓存设置5m
//            .expireAfterWrite(EXPIRE_TIME, TimeUnit.MILLISECONDS)
//            // 写后5m - 10s获取自动异步刷新
//            .refreshAfterWrite( EXPIRE_TIME - 10000, TimeUnit.MILLISECONDS)
//            // 本地缓存不存在，异步加载缓存值
//            .build(this::asyncSafeDeviceInfo);

    @Override
    public DeviceMonitorInfoDTO getDeviceMonitorInfo(String museumId, String regionCode, String exceptionDegree, String regionName) {
        List<SafeDeviceInfoDTO> deviceInfoDTOList = this.getDeviceInfoByCache(museumId, regionCode, exceptionDegree, regionName);
        if (CollectionUtils.isEmpty(deviceInfoDTOList)) {
            return null;
        }
        //监控设备数
        AtomicInteger monitorDeviceCount = new AtomicInteger();
        //温度正常数
        AtomicInteger temperatureCount = new AtomicInteger();
        //湿度正常数
        AtomicInteger humidityCount = new AtomicInteger();
        //监控设备异常数
        AtomicInteger monitorDeviceExceptionCount = new AtomicInteger();
        //温度异常数
        AtomicInteger temperatureExceptionCount = new AtomicInteger();
        //湿度异常数
        AtomicInteger humidityExceptionCount = new AtomicInteger();
        //离线设备数
        AtomicInteger offlineDeviceCount = new AtomicInteger();
        Set<String> deviceSet = new HashSet<>();
        deviceInfoDTOList.forEach(k -> {
            if (deviceSet.contains(k.getDeviceCode())) {
                return;
            }
            deviceSet.add(k.getDeviceCode());
            monitorDeviceCount.getAndIncrement();
            if(k.getDeviceStatus() != null && k.getDeviceStatus() == 1){
                if ("0".equalsIgnoreCase(k.getTemperatureExceptionDegree())) {
                    temperatureCount.getAndIncrement();
                } else {
                    temperatureExceptionCount.getAndIncrement();
                    monitorDeviceExceptionCount.getAndIncrement();
                }
                if ("0".equalsIgnoreCase(k.getHumidityExceptionDegree())) {
                    humidityCount.getAndIncrement();
                } else {
                    humidityExceptionCount.getAndIncrement();
                    monitorDeviceExceptionCount.getAndIncrement();
                }
            }else{
                offlineDeviceCount.getAndIncrement();
                monitorDeviceExceptionCount.getAndIncrement();
            }
        });
        DeviceMonitorInfoDTO deviceMonitorInfoDTO = new DeviceMonitorInfoDTO();
        deviceMonitorInfoDTO.setMonitorDeviceCount(monitorDeviceCount.get());
        deviceMonitorInfoDTO.setMonitorDeviceExceptionCount(monitorDeviceExceptionCount.get());
        deviceMonitorInfoDTO.setTemperatureCount(temperatureCount.get());
        deviceMonitorInfoDTO.setTemperatureExceptionCount(temperatureExceptionCount.get());
        deviceMonitorInfoDTO.setHumidityCount(humidityCount.get());
        deviceMonitorInfoDTO.setHumidityExceptionCount(humidityExceptionCount.get());
        deviceMonitorInfoDTO.setOfflineDeviceCount(offlineDeviceCount.get());
        return deviceMonitorInfoDTO;
    }

    @Override
    public SimplePageInfo<SafeDeviceInfoDTO> getPageByConditionQuery(String museumId, String regionCode, String exceptionDegree,
                                                                     String regionName, Integer type, Integer pageNum, Integer pageSize) {
        /*List<SyncSafeDeviceInfoDO> deviceInfoDOList = this.syncSafeDeviceInfoMapper.listByConditionNew(museumId, regionCode, exceptionDegree, regionName);
        if (CollectionUtils.isEmpty(deviceInfoDOList)) {
            return new SimplePageInfo<>();
        }
        List<String> uniqueCodes = deviceInfoDOList.stream().map(SyncSafeDeviceInfoDO::getUniqueCode).distinct().collect(Collectors.toList());
        List<MuseumBaseInfoDO> museumBaseInfoDOList = this.museumBaseInfoMapper.selectListByIds(uniqueCodes);
        Map<String, MuseumBaseInfoDO> museumMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(museumBaseInfoDOList)) {
            museumBaseInfoDOList.forEach(k -> museumMap.put(k.getId(), k));
        }
        List<SafeDeviceInfoDTO> result = deviceInfoDOList.stream().map(k -> {
            SafeDeviceInfoDTO s = BeanCopyUtils.copyByJSON(k, SafeDeviceInfoDTO.class);
            if (museumMap.containsKey(k.getUniqueCode())) {
                MuseumBaseInfoDO museumBaseInfoDO = museumMap.get(k.getUniqueCode());
                s.setMuseumName(museumBaseInfoDO.getMuseumName());
                s.setSdx(museumBaseInfoDO.getSdx());
            }
            return s;
        }).sorted(Comparator.comparing(SafeDeviceInfoDTO::getSdx)).collect(Collectors.toList());*/
        /*String key = String.join(",", Lists.newArrayList(StringUtils.isBlank(museumId) ? " " : museumId,
                StringUtils.isBlank(regionCode) ? " " : regionCode, StringUtils.isBlank(exceptionDegree) ? " " : exceptionDegree,
                StringUtils.isBlank(regionCode) ? " " : regionCode));*/

        List<SafeDeviceInfoDTO> result = this.getDeviceInfoByCache(museumId, regionCode, exceptionDegree, regionName);
        if (CollectionUtils.isEmpty(result)) {
            return new SimplePageInfo<>();
        }

        if(type == 2){
            result = result.stream().filter(s -> s.getDeviceStatus() == 1 && s.getTemperatureExceptionDegree().equals("0")).collect(Collectors.toList());
        }else if(type == 3){
            result = result.stream().filter(s -> s.getDeviceStatus() == 1 && s.getHumidityExceptionDegree().equals("0")).collect(Collectors.toList());
        }else if(type == 4){
            result = result.stream().filter(s -> (s.getDeviceStatus() == 1 && !s.getHumidityExceptionDegree().equals("0")  )
                    || (s.getDeviceStatus() == 1 &&  !s.getTemperatureExceptionDegree().equals("0") )
                    || s.getDeviceStatus() == 0).collect(Collectors.toList());
        }else if(type == 5){
            result = result.stream().filter(s -> s.getDeviceStatus() == 1 && !s.getTemperatureExceptionDegree().equals("0")).collect(Collectors.toList());
        }else if(type == 6){
            result = result.stream().filter(s -> s.getDeviceStatus() == 1 && !s.getHumidityExceptionDegree().equals("0")).collect(Collectors.toList());
        }else if(type == 7){
            result = result.stream().filter(s -> s.getDeviceStatus() == 0).collect(Collectors.toList());
        }

        return PaginationUtils.pagination(result, pageNum, pageSize);
    }

    private List<SafeDeviceInfoDTO> getDeviceInfoByCache(String museumId, String regionCode, String exceptionDegree,
                                                         String regionName){
//        List<SafeDeviceInfoDTO> safeDeviceInfoDTOList = DEVICE_INFO_SYNC_CACHE.get("DEVICE_INFO_SYNC_CACHE");
//        log.info("key: {} result: {}", DEVICE_INFO_SYNC_CACHE, safeDeviceInfoDTOList);

        // 获取所有设备配置
        List<SyncSafeTemperatureHumidityDeviceDO> deviceDOList = this.syncSafeTemperatureHumidityDeviceMapper.selectList();

        List<String> uniqueCodes = deviceDOList.stream().map(SyncSafeTemperatureHumidityDeviceDO::getUniqueCode).distinct().collect(Collectors.toList());
        List<MuseumBaseInfoDO> museumBaseInfoDOList = this.museumBaseInfoMapper.selectListByIds(uniqueCodes);
        Map<String, MuseumBaseInfoDO> museumMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(museumBaseInfoDOList)) {
            museumBaseInfoDOList.forEach(k -> museumMap.put(k.getId(), k));
        }

        // 获取所有设备的最新上报信息
        List<SyncSafeDeviceInfoRealtimeDO> realtimeDOList = syncSafeDeviceInfoRealtimeMapper.selectList();
        Map<String,SyncSafeDeviceInfoRealtimeDO> realtimeDOMap = new HashMap<>();
        realtimeDOList.forEach(s -> realtimeDOMap.put(s.getDeviceCode(),s));

        List<SafeDeviceInfoDTO> safeDeviceInfoDTOList = deviceDOList.stream().map(deviceDO -> {
            SafeDeviceInfoDTO dto = new SafeDeviceInfoDTO();
            if (museumMap.containsKey(deviceDO.getUniqueCode())) {
                MuseumBaseInfoDO museumBaseInfoDO = museumMap.get(deviceDO.getUniqueCode());
                dto.setMuseumName(museumBaseInfoDO.getMuseumName());
                dto.setSdx(museumBaseInfoDO.getSdx());
                dto.setUniqueCode(deviceDO.getUniqueCode());
                dto.setRegionCode(museumBaseInfoDO.getRegionCode());
            }else{
                // 博物馆不存在时，直接给个默认序号
                dto.setSdx(1000);
            }

            dto.setRegionName(deviceDO.getRegionName());
            dto.setDeviceCode(deviceDO.getDeviceCode());
            dto.setDeviceAddress(deviceDO.getDeviceAddress());
            dto.setMinTemperature(deviceDO.getMinTemperature());
            dto.setMaxTemperature(deviceDO.getMaxTemperature());
            dto.setTemperature(deviceDO.getCurrentTemperature() == null ? "-":deviceDO.getCurrentTemperature());
            dto.setMinHumidity(deviceDO.getMinHumidity());
            dto.setMaxHumidity(deviceDO.getMaxHumidity());
            dto.setHumidity(deviceDO.getCurrentHumidity() == null ? "-":deviceDO.getCurrentHumidity());
            dto.setReportTime(deviceDO.getReportTime());
            dto.setDeviceStatus((byte)0);
            if(realtimeDOMap.containsKey(deviceDO.getDeviceCode())){
                SyncSafeDeviceInfoRealtimeDO realtimeDO = realtimeDOMap.get(deviceDO.getDeviceCode());
                dto.setRegionName(realtimeDO.getRegionName());
                dto.setDeviceCode(realtimeDO.getDeviceCode());
                dto.setDeviceAddress(realtimeDO.getDeviceAddress());
                dto.setMinTemperature(realtimeDO.getMinTemperature());
                dto.setMaxTemperature(realtimeDO.getMaxTemperature());
                dto.setTemperature(realtimeDO.getTemperature());
                dto.setMinHumidity(realtimeDO.getMinHumidity());
                dto.setMaxHumidity(realtimeDO.getMaxHumidity());
                dto.setHumidity(realtimeDO.getHumidity());
                dto.setReportTime(realtimeDO.getReportTime());
                dto.setTemperatureExceptionDegree(realtimeDO.getTemperatureExceptionDegree());
                dto.setHumidityExceptionDegree(realtimeDO.getHumidityExceptionDegree());
                // 上报时间小于3小时，设备状态为离线
                Date timeLimit = DateUtils.getTimeBeforeHours();
                if(realtimeDO.getReportTime().after(timeLimit)){
                    dto.setDeviceStatus((byte)1);
                }
            }
            return dto;
        }).sorted(Comparator.comparing(SafeDeviceInfoDTO::getSdx)).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(safeDeviceInfoDTOList)) {
            return Lists.newArrayList();
        }

        return safeDeviceInfoDTOList.stream().map(d -> {
            if (StringUtils.isNotBlank(museumId) && !museumId.equalsIgnoreCase(d.getUniqueCode())) {
                return null;
            }
            if (StringUtils.isNotBlank(regionCode) && StringUtils.isNotBlank(d.getRegionCode()) && !d.getRegionCode().startsWith(regionCode)) {
                return null;
            }
            if (StringUtils.isNotBlank(regionName) && !regionName.equalsIgnoreCase(String.valueOf(d.getRegionName()))) {
                return null;
            }
            if (StringUtils.isNotBlank(exceptionDegree) &&
                    !(exceptionDegree.equalsIgnoreCase(d.getHumidityExceptionDegree()) || exceptionDegree.equalsIgnoreCase(d.getTemperatureExceptionDegree()))) {
                return null;
            }
            return d;
        }).filter(Objects::nonNull).collect(Collectors.toList());
    }

    private List<SafeDeviceInfoDTO> asyncSafeDeviceInfo(String key) {
        List<SyncSafeDeviceInfoDO> deviceInfoDOList = this.syncSafeDeviceInfoMapper.listByConditionNew(null, null, null, null);
        if (CollectionUtils.isEmpty(deviceInfoDOList)) {
            return Lists.newArrayList();
        }
        List<String> uniqueCodes = deviceInfoDOList.stream().map(SyncSafeDeviceInfoDO::getUniqueCode).distinct().collect(Collectors.toList());
        List<MuseumBaseInfoDO> museumBaseInfoDOList = this.museumBaseInfoMapper.selectListByIds(uniqueCodes);
        Map<String, MuseumBaseInfoDO> museumMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(museumBaseInfoDOList)) {
            museumBaseInfoDOList.forEach(k -> museumMap.put(k.getId(), k));
        }
        return deviceInfoDOList.stream().map(k -> {
            SafeDeviceInfoDTO s = BeanCopyUtils.copyByJSON(k, SafeDeviceInfoDTO.class);
            if (museumMap.containsKey(k.getUniqueCode())) {
                MuseumBaseInfoDO museumBaseInfoDO = museumMap.get(k.getUniqueCode());
                s.setMuseumName(museumBaseInfoDO.getMuseumName());
                s.setSdx(museumBaseInfoDO.getSdx());
            }
            return s;
        }).sorted(Comparator.comparing(SafeDeviceInfoDTO::getSdx)).collect(Collectors.toList());
    }

    @Override
    public SimplePageInfo<SafeDeviceInfoDTO> pageByDeviceCode(String deviceCode, Integer isException, Integer statisticsWay,
                                                              String startTime, String endTime, Integer pageNum, Integer pageSize) {
        Pair<Date, Date> pair = getDate(statisticsWay, startTime, endTime);
        Date timeStart = pair.getValue0();
        Date timeEnd = pair.getValue1();
        PageInfoBaseReq pageReq = new PageInfoBaseReq();
        pageReq.startPage(pageNum, pageSize, null);
        List<String> exceptionTypeList = null;
        if (isException == 1) {
            exceptionTypeList = Lists.newArrayList("1", "2", "3");
        }
        List<SyncSafeDeviceInfoDO> deviceInfoDOList = this.syncSafeDeviceInfoMapper.listByDeviceCode(deviceCode, exceptionTypeList, timeStart, timeEnd);
        if (CollectionUtils.isEmpty(deviceInfoDOList)) {
            return new SimplePageInfo<>();
        }
        return SimplePageInfoUtils
                .convertSimplePageInfoResult(deviceInfoDOList, SafeDeviceInfoDTO.class);
    }

    @Override
    public SingleDeviceMonitorInfoDTO getByDeviceCode(String deviceCode, Integer isException, Integer statisticsWay, String startTime, String endTime) {
        Pair<Date, Date> pair = getDate(statisticsWay, startTime, endTime);
        Date timeStart = pair.getValue0();
        Date timeEnd = pair.getValue1();
        List<String> exceptionTypeList = null;
        if (isException == 1) {
            exceptionTypeList = Lists.newArrayList("1", "2", "3");
        }
        List<SyncSafeDeviceInfoDO> deviceInfoDOList = this.syncSafeDeviceInfoMapper.listByDeviceCode(deviceCode, exceptionTypeList, timeStart, timeEnd);
        if (CollectionUtils.isEmpty(deviceInfoDOList)) {
            return null;
        }
        AtomicInteger deviceTotalExceptionCount = new AtomicInteger();
        AtomicInteger temperatureExceptionCount = new AtomicInteger();
        AtomicInteger humidityExceptionCount = new AtomicInteger();
        AtomicInteger deviceOfflineCount = new AtomicInteger();
        deviceInfoDOList.forEach(k -> {
            if (!"0".equalsIgnoreCase(k.getTemperatureExceptionDegree())) {
                temperatureExceptionCount.getAndIncrement();
                deviceTotalExceptionCount.getAndIncrement();
            }
            if (!"0".equalsIgnoreCase(k.getHumidityExceptionDegree())) {
                humidityExceptionCount.getAndIncrement();
                deviceTotalExceptionCount.getAndIncrement();
            }
            if (k.getDeviceStatus() == 0) {
                deviceOfflineCount.getAndIncrement();
                deviceTotalExceptionCount.getAndIncrement();
            }
        });
        SingleDeviceMonitorInfoDTO singleDeviceMonitorInfoDTO = new SingleDeviceMonitorInfoDTO();
        singleDeviceMonitorInfoDTO.setDeviceTotalExceptionCount(deviceTotalExceptionCount.get());
        singleDeviceMonitorInfoDTO.setTemperatureExceptionCount(temperatureExceptionCount.get());
        singleDeviceMonitorInfoDTO.setHumidityExceptionCount(humidityExceptionCount.get());
        singleDeviceMonitorInfoDTO.setDeviceOfflineCount(deviceOfflineCount.get());
        return singleDeviceMonitorInfoDTO;
    }

    @Override
    public TemperatureTrendDTO getTemperatureByDeviceCode(String deviceCode, Integer statisticsWay) {
        SyncSafeTemperatureHumidityDeviceDO deviceDO = this.syncSafeTemperatureHumidityDeviceMapper.selectByDeviceCode(deviceCode);
        if (Objects.isNull(deviceDO)) {
            return null;
        }
        Pair<Date, Date> pair = getDate(statisticsWay, null, null);
        Date timeStart = pair.getValue0();
        Date timeEnd = pair.getValue1();
        List<String> dateStrList;
        if (statisticsWay == 0) {
            dateStrList = Lists.newArrayList("00:00", "01:00", "02:00", "03:00", "04:00", "05:00", "06:00", "07:00", "08:00", "09:00", "10:00", "11:00", "12:00"
                    , "13:00", "14:00", "15:00", "16:00", "17:00", "18:00", "19:00", "20:00", "21:00", "22:00", "23:00", "24:00");
        } else if (statisticsWay == 1) {
            // 近一周
            dateStrList = DateUtils.getDateList(DateUtils.formatDateYYYMMDDHHmmss(timeStart), DateUtils.formatDateYYYMMDDHHmmss(timeEnd),"yyyy-MM-dd",2);
        } else if (statisticsWay == 2) {
            // 近一月
            dateStrList = DateUtils.getDateList(DateUtils.formatDateYYYMMDDHHmmss(timeStart), DateUtils.formatDateYYYMMDDHHmmss(timeEnd),"yyyy-MM-dd",2);
        } else if (statisticsWay == 3) {
            // 近一年
            dateStrList = DateUtils.getDateList(DateUtils.formatDateYYYMMDDHHmmss(timeStart), DateUtils.formatDateYYYMMDDHHmmss(timeEnd),"yyyy-MM",1);
        } else {
            return null;
        }
        if (CollectionUtils.isEmpty(dateStrList)) {
            return null;
        }
        TemperatureTrendDTO temperatureTrendDTO = new TemperatureTrendDTO();
        List<TemperatureTrendDTO.Trend> temperatureTrendList = Lists.newArrayList();
        List<TemperatureTrendDTO.Trend> finalTemperatureTrendList = temperatureTrendList;
        dateStrList.forEach(d -> {
            TemperatureTrendDTO.Trend trend = new TemperatureTrendDTO.Trend();
            trend.setDate(d);
            trend.setTrendData(Lists.newArrayList());
            finalTemperatureTrendList.add(trend);
        });
        temperatureTrendDTO.setMaxTemperature(deviceDO.getMaxTemperature());
        temperatureTrendDTO.setMinTemperature(deviceDO.getMinTemperature());
        temperatureTrendDTO.setTemperatureTrend(temperatureTrendList);
        List<SyncSafeDeviceInfoDO> deviceInfoDOList = this.syncSafeDeviceInfoMapper.listMonitorTemperatureDeviceCode(deviceCode, timeStart, timeEnd);
        if (CollectionUtils.isEmpty(deviceInfoDOList)) {
            return temperatureTrendDTO;
        }
        AtomicReference<Double> averageTotal = new AtomicReference<>(0.0);
        Map<String, List<TemperatureTrendDTO.TrendData>> trendDataMap = Maps.newHashMap();
        deviceInfoDOList.forEach(d -> {
            if (StringUtils.isNotBlank(d.getTemperature())) {
                averageTotal.updateAndGet(v -> v + Double.parseDouble(d.getTemperature()));
            }
            String key;
            if (statisticsWay == 0) {
                key = DateUtils.formatDate(d.getReportTime(), "HH") + ":00";
            } else if (statisticsWay == 1) {
                // 近一周
                key = DateUtils.formatDate(d.getReportTime(), "yyyy-MM-dd");
            } else if (statisticsWay == 2) {
                // 近一月
                key = DateUtils.formatDate(d.getReportTime(), "yyyy-MM-dd");
            } else {
                // 近一年
                key = DateUtils.formatDate(d.getReportTime(), "yyyy-MM");
            }
            TemperatureTrendDTO.TrendData trendData = new TemperatureTrendDTO.TrendData();
            trendData.setDate(DateUtils.formatDateYYYMMDDHHmmss(d.getReportTime()));
            trendData.setTemperature(d.getTemperature());
            if (trendDataMap.containsKey(key)) {
                trendDataMap.get(key).add(trendData);
            } else {
                trendDataMap.put(key, Lists.newArrayList(trendData));
            }
        });
        temperatureTrendDTO.setAverageValue(averageTotal.get()/deviceInfoDOList.size());
//        trendDataMap.forEach((k, v) -> {
//            TemperatureTrendDTO.Trend trend = new TemperatureTrendDTO.Trend();
//            trend.setDate(k);
//            trend.setTrendData(v);
//            temperatureTrendList.add(trend);
//        });
        temperatureTrendList.forEach(t -> {
            if (trendDataMap.containsKey(t.getDate())) {
                t.setTrendData(trendDataMap.get(t.getDate()));
            }
        });
        temperatureTrendList = temperatureTrendList.stream().filter(trend -> trend.getTrendData().size() != 0).collect(Collectors.toList());
        temperatureTrendDTO.setTemperatureTrend(temperatureTrendList);
        return temperatureTrendDTO;
    }

    @Override
    public HumidityTrendDTO getHumidityByDeviceCode(String deviceCode, Integer statisticsWay) {
        SyncSafeTemperatureHumidityDeviceDO deviceDO = this.syncSafeTemperatureHumidityDeviceMapper.selectByDeviceCode(deviceCode);
        if (Objects.isNull(deviceDO)) {
            return null;
        }
        Pair<Date, Date> pair = getDate(statisticsWay, null, null);
        Date timeStart = pair.getValue0();
        Date timeEnd = pair.getValue1();
        List<String> dateStrList;
        if (statisticsWay == 0) {
            dateStrList = Lists.newArrayList("00:00", "01:00", "02:00", "03:00", "04:00", "05:00", "06:00", "07:00", "08:00", "09:00", "10:00", "11:00", "12:00"
                    , "13:00", "14:00", "15:00", "16:00", "17:00", "18:00", "19:00", "20:00", "21:00", "22:00", "23:00", "24:00");
        } else if (statisticsWay == 1) {
            // 近一周
            dateStrList = DateUtils.getDateList(DateUtils.formatDateYYYMMDDHHmmss(timeStart), DateUtils.formatDateYYYMMDDHHmmss(timeEnd),"yyyy-MM-dd",2);
        } else if (statisticsWay == 2) {
            // 近一月
            dateStrList = DateUtils.getDateList(DateUtils.formatDateYYYMMDDHHmmss(timeStart), DateUtils.formatDateYYYMMDDHHmmss(timeEnd),"yyyy-MM-dd",2);
        } else if (statisticsWay == 3) {
            // 近一年
            dateStrList = DateUtils.getDateList(DateUtils.formatDateYYYMMDDHHmmss(timeStart), DateUtils.formatDateYYYMMDDHHmmss(timeEnd),"yyyy-MM",1);
        } else {
            return null;
        }
        if (CollectionUtils.isEmpty(dateStrList)) {
            return null;
        }
        HumidityTrendDTO humidityTrendDTO = new HumidityTrendDTO();
        List<HumidityTrendDTO.Trend> humidityTrendList = Lists.newArrayList();
        List<HumidityTrendDTO.Trend> finalHumidityTrendList = humidityTrendList;
        dateStrList.forEach(d -> {
            HumidityTrendDTO.Trend trend = new HumidityTrendDTO.Trend();
            trend.setDate(d);
            trend.setTrendData(Lists.newArrayList());
            finalHumidityTrendList.add(trend);
        });
        humidityTrendDTO.setMaxHumidity(deviceDO.getMaxHumidity());
        humidityTrendDTO.setMinHumidity(deviceDO.getMinHumidity());
        humidityTrendDTO.setHumidityTrend(humidityTrendList);
        List<SyncSafeDeviceInfoDO> deviceInfoDOList = this.syncSafeDeviceInfoMapper.listMonitorTemperatureDeviceCode(deviceCode, timeStart, timeEnd);
        if (CollectionUtils.isEmpty(deviceInfoDOList)) {
            return humidityTrendDTO;
        }
        AtomicReference<Double> averageTotal = new AtomicReference<>(0.0);
        Map<String, List<HumidityTrendDTO.TrendData>> trendDataMap = Maps.newHashMap();
        deviceInfoDOList.forEach(d -> {
            if (StringUtils.isNotBlank(d.getHumidity())) {
                averageTotal.updateAndGet(v -> v + Double.parseDouble(d.getHumidity()));
            }
            String key;
            if (statisticsWay == 0) {
                key = DateUtils.formatDate(d.getReportTime(), "HH") + ":00";
            } else if (statisticsWay == 1) {
                // 近一周
                key = DateUtils.formatDate(d.getReportTime(), "yyyy-MM-dd");
            } else if (statisticsWay == 2) {
                // 近一月
                key = DateUtils.formatDate(d.getReportTime(), "yyyy-MM-dd");
            } else {
                // 近一年
                key = DateUtils.formatDate(d.getReportTime(), "yyyy-MM");
            }
            HumidityTrendDTO.TrendData trendData = new HumidityTrendDTO.TrendData();
            trendData.setDate(DateUtils.formatDateYYYMMDDHHmmss(d.getReportTime()));
            trendData.setHumidity(d.getHumidity());
            if (trendDataMap.containsKey(key)) {
                trendDataMap.get(key).add(trendData);
            } else {
                trendDataMap.put(key, Lists.newArrayList(trendData));
            }
        });
        humidityTrendDTO.setAverageValue(averageTotal.get()/deviceInfoDOList.size());
        humidityTrendList.forEach(h -> {
            if (trendDataMap.containsKey(h.getDate())) {
                h.setTrendData(trendDataMap.get(h.getDate()));
            }
        });

        humidityTrendList = humidityTrendList.stream().filter(trend -> trend.getTrendData().size() != 0).collect(Collectors.toList());
        humidityTrendDTO.setHumidityTrend(humidityTrendList);

        return humidityTrendDTO;
    }

    @Override
    public List<SafeDeviceInfoDTO> deviceInfoExport(String deviceCode, Integer isException, Integer statisticsWay, String startTime, String endTime) {
        Pair<Date, Date> pair = getDate(statisticsWay, startTime, endTime);
        Date timeStart = pair.getValue0();
        Date timeEnd = pair.getValue1();
        List<String> exceptionTypeList = null;
        if (isException == 1) {
            exceptionTypeList = Lists.newArrayList("1", "2", "3");
        }
        List<SyncSafeDeviceInfoDO> deviceInfoDOList = this.syncSafeDeviceInfoMapper.listByDeviceCode(deviceCode, exceptionTypeList, timeStart, timeEnd);
        if (CollectionUtils.isEmpty(deviceInfoDOList)) {
            return Lists.newArrayList();
        }

        return  deviceInfoDOList.stream().map(k -> {
            SafeDeviceInfoDTO result = BeanCopyUtils.copyByJSON(k, SafeDeviceInfoDTO.class);
            result.setDeviceStatusStr(k.getDeviceStatus() == 1 ? "在线" : "离线");
            result.setReportTimeStr(DateUtils.formatDateYYYMMDDHHmmss(k.getReportTime()));
            result.setTemperatureLen(k.getMinTemperature() + "~" + k.getMaxTemperature());
            result.setHumidityLen(k.getMaxHumidity() + "%RH~" + k.getMinHumidity() + "%RH");
            return result;
        }).collect(Collectors.toList());
    }

    private Pair<Date, Date> getDate(Integer statisticsWay, String startTime, String endTime) {
        Date timeStart;
        Date timeEnd;
        //根据类型获取不同维度的统计数据
        if (statisticsWay == 0) {
            timeStart = DateUtils.getTimesDayMorning();
            timeEnd = new Date();
        } else if (statisticsWay == 1) {
            // 近一周
            Calendar calendar = DateUtils.getDateOffset(-7);
            DateUtils.setHourMinuteSecondMillisecondToZero(calendar);
            timeStart = calendar.getTime();
            timeEnd = new Date();
        } else if (statisticsWay == 2) {
            // 近一月
            Calendar calendar = DateUtils.getDateOffset(-30);
            DateUtils.setHourMinuteSecondMillisecondToZero(calendar);
            timeStart = calendar.getTime();
            timeEnd = new Date();
        } else if (statisticsWay == 3) {
            // 近一年
            Calendar calendar = DateUtils.getDateOffset(-365);
            DateUtils.setHourMinuteSecondMillisecondToZero(calendar);
            timeStart = calendar.getTime();
            timeEnd = new Date();
        } else {
            if (startTime == null) {
                throw new CommonException(CommonErrorCodeEnum.OBJECT_NOT_EXIST, "startTime 不能为空！");
            }
            if (endTime == null) {
                throw new CommonException(CommonErrorCodeEnum.OBJECT_NOT_EXIST, "endTime 不能为空！");
            }
            //自定义时间
            timeStart = DateUtils.parseDateYYYMMDDHHmmss(startTime);
            timeEnd = DateUtils.parseDateYYYMMDDHHmmss(endTime);
        }
        return new Pair<>(timeStart, timeEnd);
    }
}
