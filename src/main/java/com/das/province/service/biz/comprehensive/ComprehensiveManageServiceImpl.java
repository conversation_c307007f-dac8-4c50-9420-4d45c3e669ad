package com.das.province.service.biz.comprehensive;

import com.das.province.common.bo.SimplePageInfo;
import com.das.province.common.utils.DateUtils;
import com.das.province.common.utils.PaginationUtils;
import com.das.province.common.utils.SimplePageInfoUtils;
import com.das.province.infr.dataobject.*;
import com.das.province.infr.dataobjectexpand.*;
import com.das.province.infr.mapper.*;
import com.das.province.service.biz.comprehensive.action.ChangeRemindPageAction;
import com.das.province.service.biz.comprehensive.action.GeneralStatisticMuseumAction;
import com.das.province.service.biz.comprehensive.action.GeneralStatisticRegionAction;
import com.das.province.service.biz.comprehensive.action.MuseumInfoListAction;
import com.das.province.web.controller.comprehensive.response.*;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class ComprehensiveManageServiceImpl implements ComprehensiveManageService{

    @Resource
    private StatisticMuseumClassifyMapper statisticMuseumClassifyMapper;

    @Resource
    private StatisticMuseumGeneralMapper statisticMuseumGeneralMapper;

    @Resource
    private MuseumBaseInfoMapper museumBaseInfoMapper;

    @Resource
    private DataChangeInfoMapper dataChangeInfoMapper;

    @Resource
    private DataChangeDetailInfoMapper dataChangeDetailInfoMapper;

    @Resource
    private StatisticVisitorDayMapper statisticVisitorDayMapper;

    @Resource
    private StatisticVisitorMonthMapper statisticVisitorMonthMapper;

    @Resource
    private StatisticVisitorYearMapper statisticVisitorYearMapper;

    @Resource
    private StatisticCollectionMapper statisticCollectionMapper;

    @Resource
    private SyncVenueInfoMapper syncVenueInfoMapper;

    @Resource
    private CommClassificationMapper commClassificationMapper;

    @Resource
    private SyncSafeDeviceInfoMapper syncSafeDeviceInfoMapper;

    @Resource
    private SyncCommCollectionRegisterInfoMapper syncCommCollectionRegisterInfoMapper;

    @Resource
    private SyncSafeTemperatureHumidityDeviceMapper safeTemperatureHumidityDeviceMapper;

    @Override
    public MuseumGeneralVO queryMuseumGeneral(){
        MuseumGeneralVO museumGeneralVO = new MuseumGeneralVO();
        List<MuseumBaseInfoDO> museumBaseInfoDOList = museumBaseInfoMapper.selectList((byte)0);
        int totalNum = museumBaseInfoDOList.size();
        int settleInNum = (int)museumBaseInfoDOList.stream().filter(s -> s.getSettleIn() == 1).count();
        int reportCollectionNum = (int)museumBaseInfoDOList.stream().mapToLong(MuseumBaseInfoDO::getReportCollectionNum).sum();
        museumGeneralVO.setTotalNum(totalNum);
        museumGeneralVO.setSettleInNum(settleInNum);
        museumGeneralVO.setReportCollectionNum(reportCollectionNum);

        List<StatisticCollectionDO> statisticCollectionDOList = this.statisticCollectionMapper.selectList();
        int collectionNum = (int)statisticCollectionDOList.stream().mapToLong(StatisticCollectionDO::getCollectionNum).sum();
        museumGeneralVO.setCollectionNum(collectionNum);

        // 今日开放
        int openMuseumNum = 0;
        List<String> museumCodeList = museumBaseInfoDOList.stream().filter(s -> s.getSettleIn() == 1).map(MuseumBaseInfoDO::getId).collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(museumCodeList)){
            List<SyncVenueInfoDO> syncVenueInfoDOList = syncVenueInfoMapper.selectListByUniqueCodes(museumCodeList);
            openMuseumNum = (int)syncVenueInfoDOList.stream().filter(s -> s.getVenueClose() == (byte)1).count();
        }
        museumGeneralVO.setOpenMuseumNum(openMuseumNum);

        // 获取今日观众流量
        String statisticDay = DateUtils.formatDateYYYMMDD(new Date());
        List<StatisticVisitorDayDO> visitorDayDOList = this.statisticVisitorDayMapper.listByStatisticDay(statisticDay,null);
        int todayOffline = 0;

        if(CollectionUtils.isNotEmpty(visitorDayDOList)){
            todayOffline = (int)visitorDayDOList.stream().mapToLong(StatisticVisitorDayDO::getOfflineNum).sum();
        }
        museumGeneralVO.setTodayTotal(todayOffline);


        //获取本年观众流量
        List<StatisticVisitorYearDO> visitorYearDOList = statisticVisitorYearMapper.selectList();
        int yearOffline = 0;
        if(CollectionUtils.isNotEmpty(visitorYearDOList)){
            yearOffline = (int)visitorYearDOList.stream().mapToLong(StatisticVisitorYearDO::getOfflineNum).sum();
        }
        museumGeneralVO.setYearTotal(yearOffline);

        // 视频个数和环境监测个数
        museumGeneralVO.setCameraNum(0);

        List<SyncSafeTemperatureHumidityDeviceDO> deviceDOList = this.safeTemperatureHumidityDeviceMapper.selectList();
        museumGeneralVO.setMonitorNum(deviceDOList.size());

//        StatisticMuseumGeneralDO statisticMuseumGeneralDO = this.statisticMuseumGeneralMapper.selectInfo();
//        if(Objects.isNull(statisticMuseumGeneralDO)){
//            return museumGeneralVO;
//        }
//        museumGeneralVO.setTotalNum(statisticMuseumGeneralDO.getTotalNum());
//        museumGeneralVO.setSettleInNum(statisticMuseumGeneralDO.getSettleInNum());
//        museumGeneralVO.setUnderPreparationNum(statisticMuseumGeneralDO.getUnderPreparationNum());
//        museumGeneralVO.setNotPreparationNum(statisticMuseumGeneralDO.getNotPreparationNum());
//
//        List<MuseumClassifyVO> museumClassifyVOList = Lists.newArrayList();
//        List<StatisticMuseumClassifyDO> classifyDOList = statisticMuseumClassifyMapper.selectList();
//        if(!CollectionUtils.isEmpty(classifyDOList)){
//            museumClassifyVOList = classifyDOList.stream().map(classifyDO -> {
//                MuseumClassifyVO museumClassifyVO = new MuseumClassifyVO();
//                BeanUtils.copyProperties(classifyDO,museumClassifyVO);
//                return museumClassifyVO;
//            }).collect(Collectors.toList());
//        }
//        museumGeneralVO.setMuseumClassifyList(museumClassifyVOList);
        return museumGeneralVO;
    }

    @Override
    public List<CityMuseumNumVO> queryCityMuseumNum(){
        List<CityMuseumNumVO> voList = Lists.newArrayList();
        //统计所有博物馆
        List<CityMuseumNumDO> cityMuseumNumDOList = this.museumBaseInfoMapper.groupCityMuseumNum(null);
        if(CollectionUtils.isEmpty(cityMuseumNumDOList)){
            return voList;
        }

        // 只统计入驻博物馆
        List<CityMuseumNumDO> cityMuseumSettleInList = this.museumBaseInfoMapper.groupCityMuseumNum((byte)1);
        Map<String,CityMuseumNumDO> settleInMuseumMap = Maps.newHashMap();
        cityMuseumSettleInList.forEach(s -> settleInMuseumMap.put(s.getRegionCode(),s));

        //本年观众按城市统计
        List<GroupVisitorYearByCityDO> groupVisitorYearByCityDOList = this.statisticVisitorYearMapper.groupVisitorYearByCity();
        Map<String,GroupVisitorYearByCityDO> visitorYearMap = Maps.newHashMap();
        groupVisitorYearByCityDOList.forEach(s -> visitorYearMap.put(s.getRegionCode(),s));

        return cityMuseumNumDOList.stream().map(cityMuseumNumDO -> {
            CityMuseumNumVO cityMuseumNumVO = new CityMuseumNumVO();
            cityMuseumNumVO.setRegionCode(cityMuseumNumDO.getRegionCode());
            cityMuseumNumVO.setCityName(cityMuseumNumDO.getCityName());
            cityMuseumNumVO.setMuseumNum(cityMuseumNumDO.getMuseumNum());
            cityMuseumNumVO.setReportCollectionNum(cityMuseumNumDO.getReportCollectionNum());
            if(settleInMuseumMap.containsKey(cityMuseumNumDO.getRegionCode())){
                cityMuseumNumVO.setSettleInNum(settleInMuseumMap.get(cityMuseumNumDO.getRegionCode()).getMuseumNum());
            }

            if(visitorYearMap.containsKey(cityMuseumNumDO.getRegionCode())){
                cityMuseumNumVO.setVisitorNum(visitorYearMap.get(cityMuseumNumDO.getRegionCode()).getOfflineNum().intValue());
            }

            return cityMuseumNumVO;
        }).collect(Collectors.toList());
    }

    @Override
    public SimplePageInfo<MuseumInfoListVO> museumInfoList(MuseumInfoListAction action){
        // 查询博物馆列表
        List<MuseumBaseInfoDO> museumList = this.museumBaseInfoMapper.listByCondition(action.getMuseumCode(),action.getSettleIn(),action.getMuseumName(),action.getRegionCode(),
                action.getLevelId(),action.getNatureId(),null,"gmt_create");
        if(CollectionUtils.isEmpty(museumList)){
            return new SimplePageInfo<>();
        }
        List<String> museumIdList = museumList.stream().map(MuseumBaseInfoDO::getId).collect(Collectors.toList());

        List<Long> levelIds = museumList.stream().map(MuseumBaseInfoDO::getLevelId).collect(Collectors.toList());
        List<Long> natureIds = museumList.stream().map(MuseumBaseInfoDO::getNatureId).collect(Collectors.toList());

        List<Long> classificationIds = Lists.newArrayList();
        classificationIds.addAll(levelIds);
        classificationIds.addAll(natureIds);
        List<CommClassificationDO> commClassificationList = this.commClassificationMapper.listByIds(action.getCompanyId(), classificationIds);
        Map<Long, String> commClassificationMap = commClassificationList.stream().collect(Collectors.toMap(CommClassificationDO::getId,
                CommClassificationDO::getName, (key1, key2) -> key2));

        // 今日是否开放
        List<SyncVenueInfoDO> syncVenueInfoDOList = syncVenueInfoMapper.selectListByUniqueCodes(museumIdList);
        Map<String,SyncVenueInfoDO> venueInfoDOMap = Maps.newHashMap();
        syncVenueInfoDOList.forEach(s -> venueInfoDOMap.put(s.getUniqueCode(),s));

        // 平台藏品
        List<StatisticCollectionDO> museumCollectionList = statisticCollectionMapper.selectListByMuseumIds(museumIdList);
        Map<String,Integer> museumCollectionMap = Maps.newHashMap();
        museumCollectionList.forEach(s -> museumCollectionMap.put(s.getMuseumId(),s.getCollectionNum()));

        // 今日观众
        String thisDay = DateUtils.formatDateYYYMMDD(new Date());
        List<StatisticVisitorDayDO> visitorDayList = statisticVisitorDayMapper.listByMuseumIds(thisDay,museumIdList);
        Map<String,Integer> visitorDayMap = Maps.newHashMap();
        visitorDayList.forEach(s -> visitorDayMap.put(s.getMuseumId(),s.getOfflineNum()));

        // 本年观众
        List<StatisticVisitorYearDO> visitorYearList = statisticVisitorYearMapper.selectListMuseumIds(museumIdList);
        Map<String,Integer> visitorYearMap = Maps.newHashMap();
        visitorYearList.forEach(s -> visitorYearMap.put(s.getMuseumId(),s.getOfflineNum()));

        List<MuseumInfoListVO> resultList = museumList.stream().map(museumBaseInfoDO -> {
            MuseumInfoListVO vo = new MuseumInfoListVO();
            vo.setMuseumId(museumBaseInfoDO.getId());
            vo.setMuseumName(museumBaseInfoDO.getMuseumName());
            vo.setProvinceName(museumBaseInfoDO.getProvinceName());
            vo.setCityName(museumBaseInfoDO.getCityName());
            vo.setCountyName(museumBaseInfoDO.getCountyName());
            vo.setLevelId(museumBaseInfoDO.getLevelId());
            if(commClassificationMap.containsKey(museumBaseInfoDO.getLevelId())){
                vo.setLevelName(commClassificationMap.get(museumBaseInfoDO.getLevelId()));
            }
            vo.setNatureId(museumBaseInfoDO.getNatureId());
            if(commClassificationMap.containsKey(museumBaseInfoDO.getNatureId())){
                vo.setNatureName(commClassificationMap.get(museumBaseInfoDO.getNatureId()));
            }
            vo.setSettleIn(museumBaseInfoDO.getSettleIn());
            vo.setPlatformAddress(museumBaseInfoDO.getPlatformAddress());
            vo.setReportCollectionNum(museumBaseInfoDO.getReportCollectionNum());
            vo.setLng(museumBaseInfoDO.getLng());
            vo.setLat(museumBaseInfoDO.getLat());

            if(venueInfoDOMap.containsKey(museumBaseInfoDO.getId())){
                vo.setIsClose(venueInfoDOMap.get(museumBaseInfoDO.getId()).getVenueClose());
            }else{
                vo.setIsClose((byte)0);
            }

            if(museumCollectionMap.containsKey(museumBaseInfoDO.getId())){
                vo.setCollectionNum(museumCollectionMap.get(museumBaseInfoDO.getId()));
            }

            if(visitorDayMap.containsKey(museumBaseInfoDO.getId())){
                vo.setTodayVisitorNum(visitorDayMap.get(museumBaseInfoDO.getId()));
            }

            if(visitorYearMap.containsKey(museumBaseInfoDO.getId())){
                vo.setThisYearNum(visitorYearMap.get(museumBaseInfoDO.getId()));
            }
            return vo;
        }).collect(Collectors.toList());

        if(action.getIsClose() != null){
            resultList = resultList.stream().filter(s -> s.getIsClose() != null && s.getIsClose().equals(action.getIsClose())).collect(Collectors.toList());
        }
        // 排序
        if(action.getSortBy() != null){
            if(action.getSortBy() == 1){
                resultList = resultList.stream().filter(s->s.getReportCollectionNum() != null).sorted(Comparator.comparing(MuseumInfoListVO::getReportCollectionNum)).collect(Collectors.toList());
            }else if(action.getSortBy() == 2){
                resultList = resultList.stream().filter(s->s.getReportCollectionNum() != null).sorted(Comparator.comparing(MuseumInfoListVO::getReportCollectionNum).reversed()).collect(Collectors.toList());
            }else if(action.getSortBy() == 3){
                resultList = resultList.stream().filter(s->s.getCollectionNum() != null).sorted(Comparator.comparing(MuseumInfoListVO::getCollectionNum)).collect(Collectors.toList());
            }else if(action.getSortBy() == 4){
                resultList = resultList.stream().filter(s->s.getCollectionNum() != null).sorted(Comparator.comparing(MuseumInfoListVO::getCollectionNum).reversed()).collect(Collectors.toList());
            }else if(action.getSortBy() == 5){
                resultList = resultList.stream().filter(s->s.getThisYearNum() != null).sorted(Comparator.comparing(MuseumInfoListVO::getThisYearNum)).collect(Collectors.toList());
            }else if(action.getSortBy() == 6){
                resultList = resultList.stream().filter(s->s.getThisYearNum() != null).sorted(Comparator.comparing(MuseumInfoListVO::getThisYearNum).reversed()).collect(Collectors.toList());
            }
        }

        return PaginationUtils.pagination(resultList, action.getPageNum(), action.getPageSize());
    }

    @Override
    public SimplePageInfo<ChangeRemindPageVO>  queryChangeRemindPageList(ChangeRemindPageAction action){
        action.startPage();
        List<DataChangeInfoDO> dataChangeInfoDOList = this.dataChangeInfoMapper.listByCondition(action.getChangeType(),action.getStartTime(),
                action.getEndTime(),action.getSortBy());
        if (CollectionUtils.isEmpty(dataChangeInfoDOList)) {
            return new SimplePageInfo<>();
        }
        SimplePageInfo<ChangeRemindPageVO> pageInfo = SimplePageInfoUtils
                .convertSimplePageInfoResult(dataChangeInfoDOList, ChangeRemindPageVO.class);

        List<ChangeRemindPageVO> museumPageVOList = dataChangeInfoDOList.stream().map(dataChangeInfoDO -> {
            ChangeRemindPageVO changeRemindPageVO = new ChangeRemindPageVO();
            changeRemindPageVO.setChangeId(dataChangeInfoDO.getId());
            changeRemindPageVO.setChangeType(dataChangeInfoDO.getChangeType());
            changeRemindPageVO.setChangeDescription(dataChangeInfoDO.getChangeDescription());
            if(dataChangeInfoDO.getHappenTime() != null){
                changeRemindPageVO.setHappenTime(DateUtils.formatDateYYYMMDD(dataChangeInfoDO.getHappenTime()));
            }
            return changeRemindPageVO;
        }).collect(Collectors.toList());
        pageInfo.setList(museumPageVOList);
        return pageInfo;
    }

    @Override
    public List<ChangeDetailInfoVO> queryChangeRemindDetailList(Long changeId){
        List<DataChangeDetailInfoDO> detailInfoDOList = this.dataChangeDetailInfoMapper.selectListByChangeId(changeId);
        if(CollectionUtils.isEmpty(detailInfoDOList)){
            return Lists.newArrayList();
        }

        List<String> museumIds = detailInfoDOList.stream().map(DataChangeDetailInfoDO::getMuseumId).collect(Collectors.toList());
        Map<String,MuseumBaseInfoDO> museumBaseInfoMap = new HashMap<>();
        if(CollectionUtils.isNotEmpty(museumIds)){
            List<MuseumBaseInfoDO> museumBaseInfoDOList = museumBaseInfoMapper.selectListByIds(museumIds);
            for(MuseumBaseInfoDO museumBaseInfoDO : museumBaseInfoDOList){
                museumBaseInfoMap.put(museumBaseInfoDO.getId(),museumBaseInfoDO);
            }
        }

        return detailInfoDOList.stream().map(dataChangeDetailInfoDO -> {
            ChangeDetailInfoVO changeDetailInfoVO = new ChangeDetailInfoVO();
            changeDetailInfoVO.setMuseumId(dataChangeDetailInfoDO.getMuseumId());
            if(museumBaseInfoMap.containsKey(dataChangeDetailInfoDO.getMuseumId())){
                changeDetailInfoVO.setMuseumName(museumBaseInfoMap.get(dataChangeDetailInfoDO.getMuseumId()).getMuseumName());
            }
            changeDetailInfoVO.setUpDown(dataChangeDetailInfoDO.getUpDown());
            changeDetailInfoVO.setChangeData(dataChangeDetailInfoDO.getChangeData());
            return changeDetailInfoVO;
        }).collect(Collectors.toList());
    }

    @Override
    public ProvinceVisitorStatisticsVO provinceVisitorStatistics(){
        ProvinceVisitorStatisticsVO vo = new ProvinceVisitorStatisticsVO();
        // 获取今日观众流量
        String statisticDay = DateUtils.formatDateYYYMMDD(new Date());
        List<StatisticVisitorDayDO> visitorDayDOList = this.statisticVisitorDayMapper.listByStatisticDay(statisticDay,null);
        long todayTotal = 0;
        long todayOffline = 0;
        long todayOnline = 0;
        if(CollectionUtils.isNotEmpty(visitorDayDOList)){
            todayTotal = visitorDayDOList.stream().mapToLong(StatisticVisitorDayDO::getTotalNum).sum();
            todayOffline = visitorDayDOList.stream().mapToLong(StatisticVisitorDayDO::getOfflineNum).sum();
            todayOnline = visitorDayDOList.stream().mapToLong(StatisticVisitorDayDO::getOnlineNum).sum();
        }
        vo.setTodayTotal(todayTotal);
        vo.setTodayOffline(todayOffline);
        vo.setTodayOnline(todayOnline);

        //获取本年观众流量
        List<StatisticVisitorYearDO> visitorYearDOList = statisticVisitorYearMapper.selectList();
        long yearTotal = 0;
        long yearOffline = 0;
        long yearOnline = 0;
        if(CollectionUtils.isNotEmpty(visitorYearDOList)){
            yearTotal = visitorYearDOList.stream().mapToLong(StatisticVisitorYearDO::getTotalNum).sum();
            yearOffline = visitorYearDOList.stream().mapToLong(StatisticVisitorYearDO::getOfflineNum).sum();
            yearOnline = visitorYearDOList.stream().mapToLong(StatisticVisitorYearDO::getOnlineNum).sum();
        }
        vo.setYearTotal(yearTotal);
        vo.setYearOffline(yearOffline);
        vo.setYearOnline(yearOnline);

        // 近12个月观众
        List<String> monthList = DateUtils.getMonthList();
        Collections.sort(monthList);
        List<StatisticVisitorMonthDO> statisticVisitorMonthDOList = this.statisticVisitorMonthMapper.selectListByMonths(monthList,null);
        if(CollectionUtils.isNotEmpty(statisticVisitorMonthDOList)){
            List<ProvinceMonthVisitorVO> monthVisitorVOList = Lists.newArrayList();
            for(String month : monthList){
                ProvinceMonthVisitorVO provinceMonthVisitorVO = new ProvinceMonthVisitorVO();
                long monthTotalNum = 0;
                long monthOfflineNum = 0;
                long monthOnlineNum = 0;
                List<StatisticVisitorMonthDO> monthVisitorList = statisticVisitorMonthDOList.stream().filter(monthDO -> month.equals(monthDO.getStatisticMonth())).collect(Collectors.toList());
                if(CollectionUtils.isNotEmpty(monthVisitorList)){
                    monthTotalNum = monthVisitorList.stream().mapToLong(StatisticVisitorMonthDO::getTotalNum).sum();
                    monthOfflineNum = monthVisitorList.stream().mapToLong(StatisticVisitorMonthDO::getOfflineNum).sum();
                    monthOnlineNum = monthVisitorList.stream().mapToLong(StatisticVisitorMonthDO::getOnlineNum).sum();
                }
                provinceMonthVisitorVO.setYearMonth(month);
                provinceMonthVisitorVO.setTotalNum(monthTotalNum);
                provinceMonthVisitorVO.setOfflineNum(monthOfflineNum);
                provinceMonthVisitorVO.setOnlineNum(monthOnlineNum);
                monthVisitorVOList.add(provinceMonthVisitorVO);
            }
            vo.setProvinceMonthVisitorVOList(monthVisitorVOList);
        }

        //本年观众地区占比
        List<GroupVisitorYearByCityDO> groupVisitorYearByCityDOList = this.statisticVisitorYearMapper.groupVisitorYearByCity();
        List<CityVisitorNumVO> cityVisitorNumVOList = Lists.newArrayList();
        if(CollectionUtils.isNotEmpty(groupVisitorYearByCityDOList)){
            cityVisitorNumVOList = groupVisitorYearByCityDOList.stream().map(groupVisitorDO -> {
                CityVisitorNumVO cityVisitorNumVO = new CityVisitorNumVO();
                cityVisitorNumVO.setRegionCode(groupVisitorDO.getRegionCode());
                cityVisitorNumVO.setCityName(groupVisitorDO.getCityName());
                cityVisitorNumVO.setTotalVisitorNum(groupVisitorDO.getTotalNum());
                if(vo.getYearTotal() > 0){
                    String totalProportion = new BigDecimal(groupVisitorDO.getTotalNum()).divide(new BigDecimal(vo.getYearTotal()),4,BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100)).setScale(2, RoundingMode.HALF_UP) + "%";
                    cityVisitorNumVO.setTotalProportion(totalProportion);
                }

                cityVisitorNumVO.setOfflineNum(groupVisitorDO.getOfflineNum());
                if(vo.getYearOffline() > 0){
                    String offlineProportion = new BigDecimal(groupVisitorDO.getOfflineNum()).divide(new BigDecimal(vo.getYearOffline()),4,BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100)).setScale(2, RoundingMode.HALF_UP) + "%";
                    cityVisitorNumVO.setOfflineProportion(offlineProportion);
                }

                cityVisitorNumVO.setOnlineNum(groupVisitorDO.getOnlineNum());
                if(vo.getYearOnline() > 0){
                    String onlineProportion = new BigDecimal(groupVisitorDO.getOnlineNum()).divide(new BigDecimal(vo.getYearOnline()),4,BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100)).setScale(2, RoundingMode.HALF_UP) + "%";
                    cityVisitorNumVO.setOnlineProportion(onlineProportion);
                }

                return cityVisitorNumVO;
            }).collect(Collectors.toList());
        }
        vo.setCityVisitorNum(cityVisitorNumVOList);

        return vo;
    }

    @Override
    public ProvinceCollectionStatisticsVO provinceCollectionStatistics(){
        ProvinceCollectionStatisticsVO vo = new ProvinceCollectionStatisticsVO();
        long totalCollectionNum = 0;
        long valuableNum = 0;
        long exhibitionNum = 0;
        long holographicNum = 0;
        List<StatisticCollectionDO> statisticCollectionDOList = this.statisticCollectionMapper.selectList();
        if(CollectionUtils.isNotEmpty(statisticCollectionDOList)){
            totalCollectionNum = statisticCollectionDOList.stream().mapToLong(StatisticCollectionDO::getCollectionNum).sum();
            valuableNum = statisticCollectionDOList.stream().mapToLong(StatisticCollectionDO::getValuableNum).sum();
            exhibitionNum = statisticCollectionDOList.stream().mapToLong(StatisticCollectionDO::getExhibitionNum).sum();
            holographicNum = statisticCollectionDOList.stream().mapToLong(StatisticCollectionDO::getHolographicNum).sum();
        }
        vo.setTotalCollectionNum(totalCollectionNum);
        vo.setValuableNum(valuableNum);
        vo.setExhibitionNum(exhibitionNum);
        vo.setHolographicNum(holographicNum);

        // 根据城市统计藏品
        List<CityCollectionNumVO> cityCollectionNumVOList = Lists.newArrayList();
        List<GroupCollectionByCityDO> groupCollectionByCityDOList = this.statisticCollectionMapper.groupCollectionByCity();
        if(CollectionUtils.isNotEmpty(groupCollectionByCityDOList)){
            cityCollectionNumVOList = groupCollectionByCityDOList.stream().map(groupCollectionByCityDO -> {
                CityCollectionNumVO cityCollectionNumVO = new CityCollectionNumVO();
                BeanUtils.copyProperties(groupCollectionByCityDO,cityCollectionNumVO);
                if(vo.getTotalCollectionNum() > 0){
                    String totalProportion = new BigDecimal(groupCollectionByCityDO.getCollectionNum()).divide(new BigDecimal(vo.getTotalCollectionNum()),4,BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100)).setScale(2, RoundingMode.HALF_UP) + "%";
                    cityCollectionNumVO.setCollectionProportion(totalProportion);
                }
                return cityCollectionNumVO;
            }).collect(Collectors.toList());
        }
        vo.setCityCollectionNumVOList(cityCollectionNumVOList);
        return vo;
    }

    @Override
    public List<HotVisitorTopVO> hotVisitorTop(){
        List<HotVisitorTopVO> voList = Lists.newArrayList();
        List<StatisticVisitorYearDO> statisticVisitorYearDOList = this.statisticVisitorYearMapper.selectTopList();
        int index = 1;
        if(CollectionUtils.isNotEmpty(statisticVisitorYearDOList)){
            for(StatisticVisitorYearDO statisticVisitorYearDO:statisticVisitorYearDOList){
                HotVisitorTopVO hotVisitorTopVO = new HotVisitorTopVO();
                hotVisitorTopVO.setIndex(index);
                hotVisitorTopVO.setMuseumName(statisticVisitorYearDO.getMuseumName());
                hotVisitorTopVO.setVisitorNum(statisticVisitorYearDO.getTotalNum());
                voList.add(hotVisitorTopVO);
                index ++;
            }
        }
        return voList;
    }

    @Override
    public CityVisitorStatisticsVO cityVisitorStatistics(String regionCode){
        CityVisitorStatisticsVO vo = new CityVisitorStatisticsVO();
        // 获取今日观众流量
        String statisticDay = DateUtils.formatDateYYYMMDD(new Date());
        List<StatisticVisitorDayDO> visitorDayDOList = this.statisticVisitorDayMapper.listByStatisticDay(statisticDay,regionCode);
        long todayTotal = 0;
        long todayOffline = 0;
        long todayOnline = 0;
        if(CollectionUtils.isNotEmpty(visitorDayDOList)){
            todayTotal = visitorDayDOList.stream().mapToLong(StatisticVisitorDayDO::getTotalNum).sum();
            todayOffline = visitorDayDOList.stream().mapToLong(StatisticVisitorDayDO::getOfflineNum).sum();
            todayOnline = visitorDayDOList.stream().mapToLong(StatisticVisitorDayDO::getOnlineNum).sum();
        }
        vo.setTodayTotal(todayTotal);
        vo.setTodayOffline(todayOffline);
        vo.setTodayOnline(todayOnline);

        //获取近一年观众流量
        List<StatisticVisitorYearDO> visitorYearDOList = statisticVisitorYearMapper.selectListByRegionCode(regionCode);
        long yearTotal = 0;
        long yearOffline = 0;
        long yearOnline = 0;
        if(CollectionUtils.isNotEmpty(visitorYearDOList)){
            yearTotal = visitorYearDOList.stream().mapToLong(StatisticVisitorYearDO::getTotalNum).sum();
            yearOffline = visitorYearDOList.stream().mapToLong(StatisticVisitorYearDO::getOfflineNum).sum();
            yearOnline = visitorYearDOList.stream().mapToLong(StatisticVisitorYearDO::getOnlineNum).sum();
        }
        vo.setYearTotal(yearTotal);
        vo.setYearOffline(yearOffline);
        vo.setYearOnline(yearOnline);

        // 近12个月观众
        List<String> monthList = DateUtils.getMonthList();
        Collections.sort(monthList);
        List<StatisticVisitorMonthDO> statisticVisitorMonthDOList = this.statisticVisitorMonthMapper.selectListByMonths(monthList,regionCode);
        if(CollectionUtils.isNotEmpty(statisticVisitorMonthDOList)){
            List<CityMonthVisitorVO> monthVisitorVOList = Lists.newArrayList();
            for(String month : monthList){
                CityMonthVisitorVO cityMonthVisitorVO = new CityMonthVisitorVO();
                long monthTotalNum = 0;
                long monthOfflineNum = 0;
                long monthOnlineNum = 0;
                List<StatisticVisitorMonthDO> monthVisitorList = statisticVisitorMonthDOList.stream().filter(monthDO -> month.equals(monthDO.getStatisticMonth())).collect(Collectors.toList());
                if(CollectionUtils.isNotEmpty(monthVisitorList)){
                    monthTotalNum = monthVisitorList.stream().mapToLong(StatisticVisitorMonthDO::getTotalNum).sum();
                    monthOfflineNum = monthVisitorList.stream().mapToLong(StatisticVisitorMonthDO::getOfflineNum).sum();
                    monthOnlineNum = monthVisitorList.stream().mapToLong(StatisticVisitorMonthDO::getOnlineNum).sum();
                }
                cityMonthVisitorVO.setYearMonth(month);
                cityMonthVisitorVO.setTotalNum(monthTotalNum);
                cityMonthVisitorVO.setOfflineNum(monthOfflineNum);
                cityMonthVisitorVO.setOnlineNum(monthOnlineNum);
                monthVisitorVOList.add(cityMonthVisitorVO);
            }
            vo.setCityMonthVisitorVOList(monthVisitorVOList);
        }

        return vo;
    }

    @Override
    public List<MuseumDataStatisticsVO> museumDataStatistics(String regionCode,Byte sortBy,Byte orderBy){
        List<MuseumDataStatisticsVO> voList = Lists.newArrayList();
        // 查询全市入驻博物馆列表
        List<MuseumBaseInfoDO> museumBaseInfoDOList = museumBaseInfoMapper.selectSettleInList((byte)1,regionCode);
        if(CollectionUtils.isEmpty(museumBaseInfoDOList)){
            return voList;
        }

        // 博物馆id集合
        List<String> museumIds = museumBaseInfoDOList.stream().map(MuseumBaseInfoDO::getId).collect(Collectors.toList());

        // 查询各博物馆藏品总数
        List<StatisticCollectionDO> statisticCollectionDOList = this.statisticCollectionMapper.selectListByMuseumIds(museumIds);
        Map<String,StatisticCollectionDO> statisticCollectionDOMap = new HashMap<>();
        for(StatisticCollectionDO collectionDO : statisticCollectionDOList){
            statisticCollectionDOMap.put(collectionDO.getMuseumId(),collectionDO);
        }

        // 查询各博物馆今日观众总量
        String statisticDay = DateUtils.formatDateYYYMMDD(new Date());
        List<StatisticVisitorDayDO> statisticVisitorDayDOList = this.statisticVisitorDayMapper.listByMuseumIds(statisticDay,museumIds);
        Map<String,StatisticVisitorDayDO> statisticVisitorDayDOMap = new HashMap<>();
        for(StatisticVisitorDayDO dayDO : statisticVisitorDayDOList){
            statisticVisitorDayDOMap.put(dayDO.getMuseumId(),dayDO);
        }

        // 查询各博物馆近一年观众总量
        List<StatisticVisitorYearDO> statisticVisitorYearDOList = statisticVisitorYearMapper.selectListMuseumIds(museumIds);
        Map<String,StatisticVisitorYearDO> statisticVisitorYearDOMap = new HashMap<>();
        for(StatisticVisitorYearDO yearDO : statisticVisitorYearDOList){
            statisticVisitorYearDOMap.put(yearDO.getMuseumId(),yearDO);
        }

        voList = museumBaseInfoDOList.stream().map(museumBaseInfoDO -> {
            MuseumDataStatisticsVO vo = new MuseumDataStatisticsVO();
            vo.setMuseumId(museumBaseInfoDO.getId());
            vo.setMuseumName(museumBaseInfoDO.getMuseumName());
            int collectionNum = 0;
            int todayVisitorNum = 0;
            int yearVisitorNum = 0;
            if(statisticCollectionDOMap.containsKey(museumBaseInfoDO.getId())){
                collectionNum = statisticCollectionDOMap.get(museumBaseInfoDO.getId()).getCollectionNum();
            }
            vo.setCollectionNum(collectionNum);
            if(statisticVisitorDayDOMap.containsKey(museumBaseInfoDO.getId())){
                todayVisitorNum = statisticVisitorDayDOMap.get(museumBaseInfoDO.getId()).getTotalNum();
            }
            vo.setTodayVisitorNum(todayVisitorNum);
            if(statisticVisitorYearDOMap.containsKey(museumBaseInfoDO.getId())){
                yearVisitorNum = statisticVisitorYearDOMap.get(museumBaseInfoDO.getId()).getTotalNum();
            }
            vo.setYearVisitorNum(yearVisitorNum);
            return vo;
        }).collect(Collectors.toList());

        // 按字段排序
        if(sortBy == 2){
            if(orderBy == 0 ){
                voList = voList.stream().sorted(Comparator.comparing(MuseumDataStatisticsVO::getCollectionNum)).collect(Collectors.toList());
            }else{
                voList = voList.stream().sorted(Comparator.comparing(MuseumDataStatisticsVO::getCollectionNum).reversed()).collect(Collectors.toList());
            }
        }else if(sortBy == 3){
            if(orderBy == 0 ){
                voList = voList.stream().sorted(Comparator.comparing(MuseumDataStatisticsVO::getTodayVisitorNum)).collect(Collectors.toList());
            }else{
                voList = voList.stream().sorted(Comparator.comparing(MuseumDataStatisticsVO::getTodayVisitorNum).reversed()).collect(Collectors.toList());
            }
        }else{
            if(orderBy == 0){
                voList = voList.stream().sorted(Comparator.comparing(MuseumDataStatisticsVO::getYearVisitorNum)).collect(Collectors.toList());
            }else{
                voList = voList.stream().sorted(Comparator.comparing(MuseumDataStatisticsVO::getYearVisitorNum).reversed()).collect(Collectors.toList());
            }
        }

        return voList;
    }

    @Override
    public CityCollectionStatisticsVO cityCollectionStatistics(String regionCode){
        CityCollectionStatisticsVO vo = new CityCollectionStatisticsVO();
        vo.setRegionCode(regionCode);
        // 查询全市入驻博物馆列表
        List<MuseumBaseInfoDO> museumBaseInfoDOList = museumBaseInfoMapper.selectSettleInList((byte)1,regionCode);
        vo.setMuseumNum(museumBaseInfoDOList.size());

        long totalNum = 0;
        long valuableNum = 0;
        long exhibitionNum = 0;
        List<StatisticCollectionDO> statisticCollectionDOList = statisticCollectionMapper.selectListByCity(regionCode);
        if(CollectionUtils.isNotEmpty(statisticCollectionDOList)){
            totalNum = statisticCollectionDOList.stream().mapToLong(StatisticCollectionDO::getCollectionNum).sum();
            valuableNum = statisticCollectionDOList.stream().mapToLong(StatisticCollectionDO::getValuableNum).sum();
            exhibitionNum = statisticCollectionDOList.stream().mapToLong(StatisticCollectionDO::getExhibitionNum).sum();
        }
        vo.setTotalCollectionNum(totalNum);
        vo.setValuableNum(valuableNum);
        vo.setExhibitionNum(exhibitionNum);

        //获取近一年观众流量
        List<StatisticVisitorYearDO> visitorYearDOList = statisticVisitorYearMapper.selectListByRegionCode(regionCode);
        long yearTotal = 0;
        if(CollectionUtils.isNotEmpty(visitorYearDOList)){
            yearTotal = visitorYearDOList.stream().mapToLong(StatisticVisitorYearDO::getTotalNum).sum();
        }
        vo.setVisitorNum(yearTotal);

        vo.setMediaNum(0);
        return vo;
    }


    @Override
    public SimplePageInfo<GeneralStatisticRegionVO>  generalStatisticRegionList(GeneralStatisticRegionAction action){
        action.startPage();
        // 所有入驻博物馆按城市统计
        List<CityMuseumNumDO> cityMuseumNumDOList = museumBaseInfoMapper.groupCityMuseumNum((byte)1);
        if(CollectionUtils.isEmpty(cityMuseumNumDOList)){
            return new SimplePageInfo<>();
        }

        SimplePageInfo<GeneralStatisticRegionVO> pageInfo = SimplePageInfoUtils
                .convertSimplePageInfoResult(cityMuseumNumDOList, GeneralStatisticRegionVO.class);

        // 藏品概况统计
        List<GroupCollectionByCityDO> groupCollectionByCityDOList = statisticCollectionMapper.groupCollectionByCity();
        Map<String,GroupCollectionByCityDO> collectionByCityMap = new HashMap<>();
        if(CollectionUtils.isNotEmpty(groupCollectionByCityDOList)){
            for(GroupCollectionByCityDO collectionByCityDO : groupCollectionByCityDOList){
                collectionByCityMap.put(collectionByCityDO.getRegionCode(),collectionByCityDO);
            }
        }

        // 观众概况统计
        String startTime = "";
        String endTime = "";
        if(action.getDateType() == 1){
            // 近一月
            startTime = DateUtils.getOrderDay(new Date(),-30,0);
        }else if(action.getDateType() == 2){
            // 近半年
            startTime = DateUtils.getOrderDay(new Date(),-183,0);
        }else if(action.getDateType() == 3){
            // 近一年
            startTime = DateUtils.getOrderDay(new Date(),-365,0);
        }else if(action.getDateType() == 4){
            // 自定义
            startTime = action.getStartTime();
            endTime = action.getEndTime();
        }
        List<GroupVisitorByTimeDO> groupVisitorByTimeList = statisticVisitorDayMapper.groupVisitorByTime(startTime,endTime);
        Map<String,GroupVisitorByTimeDO> groupVisitorByTimeMap = new HashMap<>();
        if(CollectionUtils.isNotEmpty(groupVisitorByTimeList)){
            for(GroupVisitorByTimeDO groupVisitorByTimeDO : groupVisitorByTimeList){
                groupVisitorByTimeMap.put(groupVisitorByTimeDO.getRegionCode(),groupVisitorByTimeDO);
            }
        }

        List<GeneralStatisticRegionVO> generalStatisticRegionVOList = cityMuseumNumDOList.stream().map(cityMuseumNumDO -> {
            GeneralStatisticRegionVO vo = new GeneralStatisticRegionVO();
            BeanUtils.copyProperties(cityMuseumNumDO,vo);
            if(collectionByCityMap.containsKey(cityMuseumNumDO.getRegionCode())){
                GroupCollectionByCityDO groupCollectionByCityDO = collectionByCityMap.get(cityMuseumNumDO.getRegionCode());
                vo.setCollectionNum(groupCollectionByCityDO.getCollectionNum());
                vo.setScoreNum(groupCollectionByCityDO.getValuableNum());
                vo.setUnclassifiedNum(groupCollectionByCityDO.getExhibitionNum());
                BigDecimal displayRatio = new BigDecimal("0.0");
                BigDecimal twoModelRatio = new BigDecimal("0.0");
                BigDecimal threeModelRatio = new BigDecimal("0.0");
                if(groupCollectionByCityDO.getHolographicNum() != null && groupCollectionByCityDO.getCollectionNum() != null){
                    displayRatio = new BigDecimal(groupCollectionByCityDO.getHolographicNum()).divide(new BigDecimal(groupCollectionByCityDO.getCollectionNum()),3,BigDecimal.ROUND_HALF_UP);
                }
                vo.setDisplayRatio(displayRatio.multiply(new BigDecimal("100")).setScale(1,BigDecimal.ROUND_HALF_UP)+"%");
                if(groupCollectionByCityDO.getTwoModelNum() != null && groupCollectionByCityDO.getCollectionNum() != null){
                    twoModelRatio = new BigDecimal(groupCollectionByCityDO.getTwoModelNum()).divide(new BigDecimal(groupCollectionByCityDO.getCollectionNum()),3,BigDecimal.ROUND_HALF_UP);
                }
                vo.setTwoModelRatio(twoModelRatio.multiply(new BigDecimal("100")).setScale(1,BigDecimal.ROUND_HALF_UP)+"%");

                if(groupCollectionByCityDO.getThreeModelNum() != null && groupCollectionByCityDO.getCollectionNum() != null){
                    threeModelRatio = new BigDecimal(groupCollectionByCityDO.getThreeModelNum()).divide(new BigDecimal(groupCollectionByCityDO.getCollectionNum()),3,BigDecimal.ROUND_HALF_UP);
                }
                vo.setThreeModelRatio(threeModelRatio.multiply(new BigDecimal("100")).setScale(1,BigDecimal.ROUND_HALF_UP)+"%");
            }

            if(groupVisitorByTimeMap.containsKey(cityMuseumNumDO.getRegionCode())){
                GroupVisitorByTimeDO timeDO = groupVisitorByTimeMap.get(cityMuseumNumDO.getRegionCode());
                vo.setTotalVisitorNum(timeDO.getTotalVisitorNum());
                vo.setOnlineNum(timeDO.getOnlineNum());
                vo.setOfflineNum(timeDO.getOfflineNum());
                vo.setPersonalNum(timeDO.getPersonalNum());
                vo.setTeamNum(timeDO.getTeamNum());
                vo.setTeamPeopleNum(timeDO.getTeamPeopleNum());
                vo.setReserveNum(timeDO.getReserveNum());
                vo.setAdultNum(timeDO.getAdultNum());
                vo.setChildrenNum(timeDO.getChildrenNum());
                vo.setProvinceNum(timeDO.getProvinceNum());
                vo.setOutsideNum(timeDO.getOutsideNum());
                vo.setAbroadNum(timeDO.getAbroadNum());
                vo.setManNum(timeDO.getManNum());
                vo.setWomanNum(timeDO.getWomanNum());
            }
            return vo;
        }).collect(Collectors.toList());

        pageInfo.setList(generalStatisticRegionVOList);
        return pageInfo;
    }


    @Override
    public ChangeThInfoVO getChangeDeviceThInfo(Long changeId) {
        DataChangeInfoDO dataChangeInfoDO = this.dataChangeInfoMapper.selectByPrimaryKey(changeId);
        if (Objects.isNull(dataChangeInfoDO)) {
            return null;
        }
        DataChangeDetailInfoDO dataChangeDetailInfoDO = this.dataChangeDetailInfoMapper.selectByChangeId(changeId);
        if (Objects.isNull(dataChangeDetailInfoDO)) {
            return null;
        }
        String deviceCodeInfoId = dataChangeDetailInfoDO.getChangeData();
        if (StringUtils.isBlank(deviceCodeInfoId)) {
            return null;
        }
        SyncSafeDeviceInfoDO syncSafeDeviceInfoDO = this.syncSafeDeviceInfoMapper.selectByPrimaryKey(Long.parseLong(deviceCodeInfoId));
        if (Objects.isNull(syncSafeDeviceInfoDO)) {
            return null;
        }
        MuseumBaseInfoDO museumBaseInfoDO = this.museumBaseInfoMapper.selectByPrimaryKey(syncSafeDeviceInfoDO.getUniqueCode());
        if (Objects.isNull(museumBaseInfoDO)) {
            return null;
        }
        Byte changeType = dataChangeInfoDO.getChangeType();
        ChangeThInfoVO changeThInfoVO = new ChangeThInfoVO();
        changeThInfoVO.setUniqueCode(syncSafeDeviceInfoDO.getUniqueCode());
        changeThInfoVO.setDeviceCode(syncSafeDeviceInfoDO.getDeviceCode());
        changeThInfoVO.setDeviceAddress(syncSafeDeviceInfoDO.getDeviceAddress());
        changeThInfoVO.setMuseumName(museumBaseInfoDO.getMuseumName());
        if (3 == changeType) {
            changeThInfoVO.setMinTh(syncSafeDeviceInfoDO.getMinTemperature());
            changeThInfoVO.setMaxTh(syncSafeDeviceInfoDO.getMaxTemperature());
            changeThInfoVO.setThValue(syncSafeDeviceInfoDO.getTemperature());
            changeThInfoVO.setThExceptionDegree(syncSafeDeviceInfoDO.getTemperatureExceptionDegree());
        } else {
            changeThInfoVO.setMinTh(syncSafeDeviceInfoDO.getMinHumidity());
            changeThInfoVO.setMaxTh(syncSafeDeviceInfoDO.getMaxHumidity());
            changeThInfoVO.setThValue(syncSafeDeviceInfoDO.getHumidity());
            changeThInfoVO.setThExceptionDegree(syncSafeDeviceInfoDO.getHumidityExceptionDegree());
        }
        return changeThInfoVO;
    }

    @Override
    public List<ChangeOfflineVO> getChangeDeviceOfflineList(Long changeId) {
        DataChangeInfoDO dataChangeInfoDO = this.dataChangeInfoMapper.selectByPrimaryKey(changeId);
        if (Objects.isNull(dataChangeInfoDO)) {
            return Lists.newArrayList();
        }
        DataChangeDetailInfoDO dataChangeDetailInfoDO = this.dataChangeDetailInfoMapper.selectByChangeId(changeId);
        if (Objects.isNull(dataChangeDetailInfoDO)) {
            return Lists.newArrayList();
        }
        String deviceCodeInfoId = dataChangeDetailInfoDO.getChangeData();
        if (StringUtils.isBlank(deviceCodeInfoId)) {
            return Lists.newArrayList();
        }
        String[] deviceCodeInfoIdArr = deviceCodeInfoId.split(",");
        List<Long> deviceCodeInfoIds = Lists.newArrayList();
        for (String id : deviceCodeInfoIdArr) {
            deviceCodeInfoIds.add(Long.parseLong(id));
        }
        List<SyncSafeDeviceInfoDO> syncSafeDeviceInfoDOList = this.syncSafeDeviceInfoMapper.selectByIds(deviceCodeInfoIds);
        if (CollectionUtils.isEmpty(syncSafeDeviceInfoDOList)) {
            return Lists.newArrayList();
        }
        List<String> uniqueCodes = syncSafeDeviceInfoDOList.stream().map(SyncSafeDeviceInfoDO::getUniqueCode).distinct().collect(Collectors.toList());
        List<MuseumBaseInfoDO> museumBaseInfoDOList = this.museumBaseInfoMapper.selectListByIds(uniqueCodes);
        Map<String, String> museumMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(museumBaseInfoDOList)) {
            museumMap = museumBaseInfoDOList.stream().collect(Collectors.toMap(MuseumBaseInfoDO::getId,
                    MuseumBaseInfoDO::getMuseumName, (key1, key2) -> key2));
        }
        Map<String, String> finalMuseumMap = museumMap;
        return syncSafeDeviceInfoDOList.stream().map(d -> {
            ChangeOfflineVO changeOfflineVO = new ChangeOfflineVO();
            changeOfflineVO.setDeviceAddress(d.getDeviceAddress());
            changeOfflineVO.setDeviceCode(d.getDeviceCode());
            changeOfflineVO.setUniqueCode(d.getUniqueCode());
            changeOfflineVO.setRegionName(d.getRegionName());
            changeOfflineVO.setMuseumName(finalMuseumMap.get(d.getUniqueCode()));
            return changeOfflineVO;
        }).collect(Collectors.toList());
    }

    @Override
    public SimplePageInfo<GeneralStatisticMuseumVO>  generalStatisticMuseumList(GeneralStatisticMuseumAction action){
        action.startPage();
        List<MuseumBaseInfoDO> museumBaseInfoDOList = this.museumBaseInfoMapper.selectListByParam((byte)1,action.getRegionCode(),action.getMuseumId());
        if(CollectionUtils.isEmpty(museumBaseInfoDOList)){
            return new SimplePageInfo<>();
        }
        SimplePageInfo<GeneralStatisticMuseumVO> pageInfo = SimplePageInfoUtils
                .convertSimplePageInfoResult(museumBaseInfoDOList, GeneralStatisticMuseumVO.class);

        List<String> museumIds = museumBaseInfoDOList.stream().map(MuseumBaseInfoDO::getId).collect(Collectors.toList());
        // 藏品概况信息
        List<StatisticCollectionDO> statisticCollectionDOList = this.statisticCollectionMapper.selectListByMuseumIds(museumIds);
        Map<String,StatisticCollectionDO> statisticCollectionMap = new HashMap<>();
        if(CollectionUtils.isNotEmpty(statisticCollectionDOList)){
            for(StatisticCollectionDO statisticCollectionDO : statisticCollectionDOList){
                statisticCollectionMap.put(statisticCollectionDO.getMuseumId(),statisticCollectionDO);
            }
        }

        // 观众概况统计
        String startTime = "";
        String endTime = "";
        if(action.getDateType() == 1){
            // 近一月
            startTime = DateUtils.getOrderDay(new Date(),-30,0);
        }else if(action.getDateType() == 2){
            // 近半年
            startTime = DateUtils.getOrderDay(new Date(),-183,0);
        }else if(action.getDateType() == 3){
            // 近一年
            startTime = DateUtils.getOrderDay(new Date(),-365,0);
        }else if(action.getDateType() == 4){
            // 自定义
            startTime = action.getStartTime();
            endTime = action.getEndTime();
        }
        List<GroupVisitorByMuseumDO> groupVisitorByMuseumList = statisticVisitorDayMapper.groupVisitorByMuseum(museumIds,startTime,endTime);
        Map<String,GroupVisitorByMuseumDO> groupVisitorByMuseumMap = new HashMap<>();
        if(CollectionUtils.isNotEmpty(groupVisitorByMuseumList)){
            for(GroupVisitorByMuseumDO groupVisitorByMuseumDO : groupVisitorByMuseumList){
                groupVisitorByMuseumMap.put(groupVisitorByMuseumDO.getMuseumId(),groupVisitorByMuseumDO);
            }
        }

        List<GeneralStatisticMuseumVO> voList = museumBaseInfoDOList.stream().map(museumBaseInfoDO -> {
            GeneralStatisticMuseumVO vo = new GeneralStatisticMuseumVO();
            vo.setMuseumId(museumBaseInfoDO.getId());
            vo.setMuseumName(museumBaseInfoDO.getMuseumName());
            if(statisticCollectionMap.containsKey(museumBaseInfoDO.getId())){
                StatisticCollectionDO collectionDO = statisticCollectionMap.get(museumBaseInfoDO.getId());
                vo.setCollectionNum(collectionDO.getCollectionNum());
                vo.setScoreNum(collectionDO.getValuableNum());
                vo.setUnclassifiedNum(collectionDO.getExhibitionNum());
                BigDecimal displayRatio = new BigDecimal("0.0");
                BigDecimal twoModelRatio = new BigDecimal("0.0");
                BigDecimal threeModelRatio = new BigDecimal("0.0");
                if(collectionDO.getHolographicNum() != null && collectionDO.getCollectionNum() != null){
                    displayRatio = new BigDecimal(collectionDO.getHolographicNum()).divide(new BigDecimal(collectionDO.getCollectionNum()),3,BigDecimal.ROUND_HALF_UP);
                }
                vo.setDisplayRatio(displayRatio.multiply(new BigDecimal("100")).setScale(1,BigDecimal.ROUND_HALF_UP)+"%");
                if(collectionDO.getTwoModelNum() != null && collectionDO.getCollectionNum() != null){
                    twoModelRatio = new BigDecimal(collectionDO.getTwoModelNum()).divide(new BigDecimal(collectionDO.getCollectionNum()),3,BigDecimal.ROUND_HALF_UP);
                }
                vo.setTwoModelRatio(twoModelRatio.multiply(new BigDecimal("100")).setScale(1,BigDecimal.ROUND_HALF_UP)+"%");

                if(collectionDO.getThreeModelNum() != null && collectionDO.getCollectionNum() != null){
                    threeModelRatio = new BigDecimal(collectionDO.getThreeModelNum()).divide(new BigDecimal(collectionDO.getCollectionNum()),3,BigDecimal.ROUND_HALF_UP);
                }
                vo.setThreeModelRatio(threeModelRatio.multiply(new BigDecimal("100")).setScale(1,BigDecimal.ROUND_HALF_UP)+"%");
            }

            if(groupVisitorByMuseumMap.containsKey(museumBaseInfoDO.getId())){
                GroupVisitorByMuseumDO museumDO = groupVisitorByMuseumMap.get(museumBaseInfoDO.getId());
                vo.setTotalVisitorNum(museumDO.getTotalVisitorNum());
                vo.setOnlineNum(museumDO.getOnlineNum());
                vo.setOfflineNum(museumDO.getOfflineNum());
                vo.setPersonalNum(museumDO.getPersonalNum());
                vo.setTeamNum(museumDO.getTeamNum());
                vo.setTeamPeopleNum(museumDO.getTeamPeopleNum());
                vo.setReserveNum(museumDO.getReserveNum());
                vo.setAdultNum(museumDO.getAdultNum());
                vo.setChildrenNum(museumDO.getChildrenNum());
                vo.setProvinceNum(museumDO.getProvinceNum());
                vo.setOutsideNum(museumDO.getOutsideNum());
                vo.setAbroadNum(museumDO.getAbroadNum());
                vo.setManNum(museumDO.getManNum());
                vo.setWomanNum(museumDO.getWomanNum());
            }
            return vo;
        }).collect(Collectors.toList());
        pageInfo.setList(voList);
        return pageInfo;
    }

    @Override
    public List<CollectionGroupByLevelVO>  collectionGroupByLevel(){
        List<CollectionGroupByLevelDO> collectionGroupByLevelDOList = syncCommCollectionRegisterInfoMapper.collectionGroupByLevel();
        if(CollectionUtils.isEmpty(collectionGroupByLevelDOList)){
            return Lists.newArrayList();
        }

        return collectionGroupByLevelDOList.stream().map(collectionGroupByLevelDO -> {
            CollectionGroupByLevelVO vo = new CollectionGroupByLevelVO();
            vo.setIdentifyLevelName(collectionGroupByLevelDO.getIdentifyLevelName());
            vo.setCollectionNum(collectionGroupByLevelDO.getCollectionNum());
            return vo;
        }).collect(Collectors.toList());
    }

}
