package com.das.province.service.biz.collection.action;

import com.das.province.common.bo.PageInfoBaseReq;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2023/3/28
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class QueryCulturalPageByConditionAction extends PageInfoBaseReq {

    private static final long serialVersionUID = -2348769298810357176L;

    private String collectionName;
    private String categoryName;
    private String museumCode;
    private String regionCode;
    private String levelName;
    private String textureName;
    private String sourceName;
    private String age;
    private String completeDegreeName;
    private String sortBy;
}
