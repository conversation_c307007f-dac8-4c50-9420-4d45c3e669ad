package com.das.province.service.biz.user;

import com.das.province.service.biz.user.action.*;
import com.das.province.service.biz.user.dto.PositionDTO;
import com.das.province.service.biz.user.dto.PositionDepartmentDTO;
import com.das.province.service.biz.user.dto.PositionPageDTO;
import com.das.province.common.bo.SimplePageInfo;

import java.util.List;

public interface PositionService {
    Long addPosition(AddPositionAction action);
    void editPositionById(EditPositionByIdAction action);
    void delPositionById(DelPositionByIdAction action);
    void batchDelPositionByIds(DelPositionByIdsAction action);
    SimplePageInfo<PositionPageDTO> queryPageByCondition(QueryPositionPageByConditionAction action);
    PositionDTO queryByPositionId(Long companyId, Long positionId);
    List<PositionDepartmentDTO> queryByPositionIds(Long companyId, List<Long> positionIds);
    List<PositionDTO> queryByDepartmentId(Long companyId, Long departmentId);
}
