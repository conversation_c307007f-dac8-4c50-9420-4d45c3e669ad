package com.das.province.service.biz.collection.dto;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2023/3/30
 */
@Data
public class AgeRangDTO {

    /**
     * 年龄段key
     */
    private String key;

    /**
     * 年段段key数量
     */
    private Long keyNum;

    /**
     * 年段段key占比
     */
    private BigDecimal keyNumRatio;

    /**
     * 年段段key占比
     */
    private String keyNumRatioStr;

    public AgeRangDTO(String key, Long keyNum, BigDecimal keyNumRatio, String keyNumRatioStr) {
        this.key = key;
        this.keyNum = keyNum;
        this.keyNumRatio = keyNumRatio;
        this.keyNumRatioStr = keyNumRatioStr;
    }

}
