package com.das.province.service.biz.museum;

import com.das.province.common.bo.SimplePageInfo;
import com.das.province.common.exception.CommonErrorCodeEnum;
import com.das.province.common.exception.CommonException;
import com.das.province.common.utils.BeanCopyUtils;
import com.das.province.common.utils.SimplePageInfoUtils;
import com.das.province.common.utils.UniqueIDUtil;
import com.das.province.infr.dataobject.CommClassificationDO;
import com.das.province.infr.dataobject.MuseumBaseInfoDO;
import com.das.province.infr.dataobject.SyncUserInfoDO;
import com.das.province.infr.mapper.CommClassificationMapper;
import com.das.province.infr.mapper.MuseumBaseInfoMapper;
import com.das.province.infr.mapper.SyncUserInfoMapper;
import com.das.province.service.biz.museum.action.AddMuseumAction;
import com.das.province.service.biz.museum.action.EditMuseumAction;
import com.das.province.service.biz.museum.action.QueryMuseumPageAction;
import com.das.province.web.controller.museum.response.MuseumPageVO;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@Service
public class MuseumManageServiceImpl implements MuseumManageService{

    @Resource
    private MuseumBaseInfoMapper museumBaseInfoMapper;

    @Resource
    private CommClassificationMapper commClassificationMapper;

    @Resource
    private SyncUserInfoMapper syncUserInfoMapper;

    @Override
    public SimplePageInfo<MuseumPageVO> queryPageByCondition(QueryMuseumPageAction action){
        action.startPage();
        List<MuseumBaseInfoDO> museumList = this.museumBaseInfoMapper.listByCondition(action.getMuseumId(),action.getSettleIn(),action.getMuseumName(),action.getRegionCode(),
                action.getLevelId(),action.getNatureId(),action.getTypeId(),action.getSortBy());
        if (CollectionUtils.isEmpty(museumList)) {
            return new SimplePageInfo<>();
        }
        SimplePageInfo<MuseumPageVO> pageInfo = SimplePageInfoUtils
                .convertSimplePageInfoResult(museumList, MuseumPageVO.class);

        List<Long> levelIds = museumList.stream().map(MuseumBaseInfoDO::getLevelId).collect(Collectors.toList());
        List<Long> natureIds = museumList.stream().map(MuseumBaseInfoDO::getNatureId).collect(Collectors.toList());
        List<Long> typeIds = museumList.stream().map(MuseumBaseInfoDO::getTypeId).collect(Collectors.toList());

        List<Long> classificationIds = Lists.newArrayList();
        classificationIds.addAll(levelIds);
        classificationIds.addAll(natureIds);
        classificationIds.addAll(typeIds);
        List<CommClassificationDO> commClassificationList = this.commClassificationMapper.listByIds(action.getCompanyId(), classificationIds);
        Map<Long, String> commClassificationMap = commClassificationList.stream().collect(Collectors.toMap(CommClassificationDO::getId,
                CommClassificationDO::getName, (key1, key2) -> key2));

        List<SyncUserInfoDO> syncUserInfoDOList =  syncUserInfoMapper.selectList();
        Map<String,SyncUserInfoDO> syncUserInfoMap = new HashMap<>();
        if(CollectionUtils.isNotEmpty(syncUserInfoDOList)){
            for(SyncUserInfoDO syncUserInfoDO : syncUserInfoDOList){
                syncUserInfoMap.put(syncUserInfoDO.getUniqueCode()+"-"+syncUserInfoDO.getPostCode(),syncUserInfoDO);
            }
        }

        List<MuseumPageVO> museumPageVOList = museumList.stream().map(museumBaseInfoDO -> {
            MuseumPageVO museumPageVO = new MuseumPageVO();
            BeanUtils.copyProperties(museumBaseInfoDO,museumPageVO);
            museumPageVO.setMuseumId(museumBaseInfoDO.getId());
            if(commClassificationMap.containsKey(museumBaseInfoDO.getLevelId())){
                museumPageVO.setLevelName(commClassificationMap.get(museumBaseInfoDO.getLevelId()));
            }
            if(commClassificationMap.containsKey(museumBaseInfoDO.getNatureId())){
                museumPageVO.setNatureName(commClassificationMap.get(museumBaseInfoDO.getNatureId()));
            }
            if(commClassificationMap.containsKey(museumBaseInfoDO.getTypeId())){
                museumPageVO.setTypeName(commClassificationMap.get(museumBaseInfoDO.getTypeId()));
            }
            long totalNum = 0;
            // 在编
            if(syncUserInfoMap.containsKey(museumBaseInfoDO.getId() + "-" + "PREN")){
                long underPreparationNum = syncUserInfoMap.get(museumBaseInfoDO.getId() + "-" + "PREN").getCount();
                museumPageVO.setUnderPreparationNum(underPreparationNum);
                totalNum += underPreparationNum;
            }
            // 不在编
            if(syncUserInfoMap.containsKey(museumBaseInfoDO.getId() + "-" + "CONT")){
                long notPreparationNum = syncUserInfoMap.get(museumBaseInfoDO.getId() + "-" + "CONT").getCount();
                museumPageVO.setNotPreparationNum(notPreparationNum);
                totalNum += notPreparationNum;
            }
            museumPageVO.setTotalNum(totalNum);

            return museumPageVO;
        }).collect(Collectors.toList());
        pageInfo.setList(museumPageVOList);
        return pageInfo;
    }

    @Override
    public String addMuseum(AddMuseumAction addMuseumAction){
        MuseumBaseInfoDO museumBaseInfoDO = new MuseumBaseInfoDO();
        BeanUtils.copyProperties(addMuseumAction,museumBaseInfoDO);
        String museumId = UniqueIDUtil.getUniqueID();
        museumBaseInfoDO.setId(museumId);
        museumBaseInfoDO.setCreator(addMuseumAction.getUserId());
        museumBaseInfoDO.setModifier(addMuseumAction.getUserId());
        this.museumBaseInfoMapper.insertSelective(museumBaseInfoDO);
        return museumId;
    }

    @Override
    public void editMuseum(EditMuseumAction editMuseumAction){
        MuseumBaseInfoDO museumBaseInfoDO = new MuseumBaseInfoDO();
        BeanUtils.copyProperties(editMuseumAction,museumBaseInfoDO);
        museumBaseInfoDO.setId(editMuseumAction.getMuseumId());
        museumBaseInfoDO.setModifier(editMuseumAction.getUserId());
        this.museumBaseInfoMapper.updateByPrimaryKeySelective(museumBaseInfoDO);
    }

    @Override
    public void delMuseumById(String museumId){
        MuseumBaseInfoDO MuseumBaseInfoDO = this.museumBaseInfoMapper.selectByPrimaryKey(museumId);
        if(Objects.isNull(MuseumBaseInfoDO)){
            throw new CommonException(CommonErrorCodeEnum.VALIDATE_ERROR, "博物馆信息不存在！");
        }
        this.museumBaseInfoMapper.updateDeleteStatus(museumId,(byte)1);
    }

    @Override
    public List<MuseumPageVO> getByRegionCode(String regionCode) {
        List<MuseumBaseInfoDO> museumBaseInfoList = this.museumBaseInfoMapper.selectSettleInList(null, regionCode);
        if (CollectionUtils.isEmpty(museumBaseInfoList)) {
            return Lists.newArrayList();
        }
        return museumBaseInfoList.stream().map(k -> {
            MuseumPageVO museumPageVO = BeanCopyUtils.copyByJSON(k, MuseumPageVO.class);
            museumPageVO.setMuseumId(k.getId());
            return museumPageVO;
        }).collect(Collectors.toList());
    }

    @Override
    public List<MuseumPageVO> getByUniqueCodes(List<String> uniqueCodes) {
        List<MuseumBaseInfoDO> museumBaseInfoDOList = museumBaseInfoMapper.selectListByIds(uniqueCodes);
        if (CollectionUtils.isEmpty(museumBaseInfoDOList)) {
            return Lists.newArrayList();
        }
        return museumBaseInfoDOList.stream().map(m -> {
            MuseumPageVO museumPageVO = BeanCopyUtils.copyByJSON(m, MuseumPageVO.class);
            museumPageVO.setMuseumId(m.getId());
            return museumPageVO;
        }).collect(Collectors.toList());
    }
}
