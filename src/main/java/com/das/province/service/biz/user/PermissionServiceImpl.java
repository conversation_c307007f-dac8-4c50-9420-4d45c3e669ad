package com.das.province.service.biz.user;

import com.das.province.common.bo.SimplePageInfo;
import com.das.province.common.exception.CommonErrorCodeEnum;
import com.das.province.common.exception.CommonException;
import com.das.province.common.utils.BeanCopyUtils;
import com.das.province.common.utils.SimplePageInfoUtils;
import com.das.province.infr.dataobject.AccPermissionDO;
import com.das.province.infr.dataobject.AccRoleDO;
import com.das.province.infr.dataobject.AccRolePermissionRelDO;
import com.das.province.infr.entity.FunctionPermissionVO;
import com.das.province.infr.entity.PermissionEntity;
import com.das.province.infr.mapper.AccPermissionMapper;
import com.das.province.infr.mapper.AccRoleMapper;
import com.das.province.infr.mapper.AccRolePermissionRelMapper;
import com.das.province.infr.repository.PermissionRepository;
import com.das.province.service.biz.user.action.*;
import com.das.province.service.biz.user.dto.FunctionPermissionDTO;
import com.das.province.service.biz.user.dto.PermissionDTO;
import com.das.province.service.biz.user.dto.PermissionPageDTO;
import com.das.province.service.biz.user.dto.PermissionTreeDTO;
import com.das.province.service.enums.*;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@Service
public class PermissionServiceImpl implements PermissionService {

    @Resource
    private AccPermissionMapper accPermissionMapper;

    @Resource
    private AccRolePermissionRelMapper accRolePermissionRelMapper;

    @Resource
    private PermissionRepository permissionRepository;

    @Resource
    private AccRoleMapper accRoleMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long addPermission(AddPermissionAction action) {
        Long companyId = action.getCompanyId();
        String permissionCode = action.getPermissionCode();
        if(CollectionUtils.isNotEmpty(action.getFunctionPermissionList())){
            List<SaveFunctionPermissionAction> checkFunctionPermissionList = Lists.newArrayList();
            checkFunctionPermissionList.addAll(action.getFunctionPermissionList());
            SaveFunctionPermissionAction saveFunctionPermissionAction = new SaveFunctionPermissionAction();
            saveFunctionPermissionAction.setPermissionCode(action.getPermissionCode());
            checkFunctionPermissionList.add(saveFunctionPermissionAction);
            List<String> permissionCodeList = checkFunctionPermissionList.stream().map(SaveFunctionPermissionAction::getPermissionCode).distinct().collect(Collectors.toList());
            if(permissionCodeList.size() != checkFunctionPermissionList.size()){
                throw new CommonException(CommonErrorCodeEnum.VALIDATE_ERROR, "权限编码重复");
            }
        }
        AccPermissionDO permissionDO = this.accPermissionMapper.selectByPermissionCode(companyId, permissionCode,
                DeleteStatusEnum.否.getCode().byteValue());
        if (Objects.nonNull(permissionDO)) {
            throw new CommonException(CommonErrorCodeEnum.VALIDATE_ERROR, "该菜单[" + permissionCode + "]编码已存在");
        }
        FunctionStatusEnum functionStatus = action.getFunctionStatus();
        List<SaveFunctionPermissionAction> functionPermissionList = action.getFunctionPermissionList();
        if (FunctionStatusEnum.是.equals(functionStatus) && CollectionUtils.isNotEmpty(functionPermissionList)) {
            List<String> functionPermissionCodes = functionPermissionList.stream()
                    .map(SaveFunctionPermissionAction::getPermissionCode).collect(Collectors.toList());
            List<AccPermissionDO> functionPermissionQueryList = this.accPermissionMapper.listByPermissionCodes(companyId, functionPermissionCodes,
                    DeleteStatusEnum.否.getCode().byteValue());
            if (CollectionUtils.isNotEmpty(functionPermissionQueryList)) {
                String functionPermissionCode = functionPermissionQueryList.stream().map(AccPermissionDO::getPermissionCode)
                        .collect(Collectors.joining(","));
                throw new CommonException(CommonErrorCodeEnum.VALIDATE_ERROR, "该菜单功能[" + functionPermissionCode + "]编码已存在");
            }
        }
        if(Objects.isNull(action.getParentId())){
            AccPermissionDO accPermissionDO = accPermissionMapper.selectRootByCompanyId(companyId,DeleteStatusEnum.否.getCode().byteValue());
            if(Objects.nonNull(accPermissionDO)){
                throw new CommonException(CommonErrorCodeEnum.VALIDATE_ERROR, "菜单根节点[" + accPermissionDO.getPermissionName() + "]已存在");
            }
        }
        PermissionEntity menuPermissionEntity = new PermissionEntity();
        menuPermissionEntity.setCompanyId(companyId);
        menuPermissionEntity.setPermissionName(action.getPermissionName());
        menuPermissionEntity.setPermissionCode(action.getPermissionCode());
        menuPermissionEntity.setPermissionType(action.getPermissionType().getCode());
        menuPermissionEntity.setParentId(action.getParentId());
        menuPermissionEntity.setLevel(action.getLevel());
        menuPermissionEntity.setSortIndex(action.getSortIndex());
        menuPermissionEntity.setPermissionStatus(AccountStatusEnum.启用.getCode().byteValue());
        menuPermissionEntity.setFunctionStatus(action.getFunctionStatus().getCode().byteValue());
        menuPermissionEntity.setIsDelete(DeleteStatusEnum.否.getCode().byteValue());
        menuPermissionEntity.setResourceUrl(action.getResourceUrl());
        menuPermissionEntity.setResourceIcon(action.getResourceIcon());
        menuPermissionEntity.setCreator(action.getCreator());
        menuPermissionEntity.setModifier(action.getModifier());
        if (FunctionStatusEnum.是.equals(functionStatus) && CollectionUtils.isNotEmpty(functionPermissionList)) {
            List<FunctionPermissionVO> functionPermissionVOList = functionPermissionList.stream().map(functionPermission -> {
                FunctionPermissionVO functionPermissionVO = new FunctionPermissionVO();
                functionPermissionVO.setCompanyId(companyId);
                functionPermissionVO.setPermissionName(functionPermission.getPermissionName());
                functionPermissionVO.setPermissionCode(functionPermission.getPermissionCode());
                functionPermissionVO.setPermissionType(functionPermission.getPermissionType().getCode());
                functionPermissionVO.setParentId(functionPermission.getParentId());
                functionPermissionVO.setLevel(functionPermission.getLevel());
                functionPermissionVO.setSortIndex(functionPermission.getSortIndex());
                functionPermissionVO.setPermissionStatus(AccountStatusEnum.启用.getCode().byteValue());
                functionPermissionVO.setFunctionStatus(FunctionStatusEnum.否.getCode().byteValue());
                functionPermissionVO.setIsDelete(DeleteStatusEnum.否.getCode().byteValue());
                functionPermissionVO.setCreator(action.getCreator());
                functionPermissionVO.setModifier(action.getModifier());
                return functionPermissionVO;
            }).collect(Collectors.toList());
            menuPermissionEntity.setFunctionPermissionList(functionPermissionVOList);
        }
        this.permissionRepository.savePermissionEntity(menuPermissionEntity);
        return menuPermissionEntity.getPermissionId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveFunctionPermission(AddFunctionPermissionAction action) {
        Long companyId = action.getCompanyId();
        Long parentId = action.getParentId();
        Long modifier = action.getModifier();
        AccPermissionDO parentPermissionDO = this.accPermissionMapper.selectByPermissionId(companyId, parentId, DeleteStatusEnum.否.getCode().byteValue());
        if (Objects.isNull(parentPermissionDO)) {
            throw new CommonException(CommonErrorCodeEnum.VALIDATE_ERROR, "该父权限信息不存在");
        }
        List<SaveFunctionPermissionAction> functionPermissionList = action.getFunctionPermissionList();
        if (CollectionUtils.isEmpty(functionPermissionList)) {
            return;
        }
        this.functionPermissionCheck(companyId, parentId, functionPermissionList);
        AccPermissionDO updatePermissionDO = new AccPermissionDO();
        updatePermissionDO.setId(parentId);
        updatePermissionDO.setFunctionStatus(FunctionStatusEnum.是.getCode().byteValue());
        updatePermissionDO.setModifier(modifier);
        this.accPermissionMapper.updateByPrimaryKeySelective(updatePermissionDO);
        PermissionEntity menuPermissionEntity = new PermissionEntity();
        menuPermissionEntity.setPermissionId(parentId);
        menuPermissionEntity.setCompanyId(companyId);
        List<FunctionPermissionVO> functionPermissionVOList = this.convertFunctionPermission(companyId, parentId, modifier, functionPermissionList);
        menuPermissionEntity.setFunctionPermissionList(functionPermissionVOList);
        this.permissionRepository.saveFunctionPermissionEntity(menuPermissionEntity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void editPermissionById(EditPermissionByIdAction action) {
        Long companyId = action.getCompanyId();
        String permissionCode = action.getPermissionCode();
        Long permissionId = action.getPermissionId();
        AccPermissionDO permissionDO = this.accPermissionMapper.selectByPermissionId(companyId, permissionId, DeleteStatusEnum.否.getCode().byteValue());
        if (Objects.isNull(permissionDO)) {
            throw new CommonException(CommonErrorCodeEnum.VALIDATE_ERROR, "该菜单权限信息不存在");
        }
        AccPermissionDO permissionCodeDO = this.accPermissionMapper.selectByPermissionCode(companyId, permissionCode,
                DeleteStatusEnum.否.getCode().byteValue());
        if (Objects.nonNull(permissionCodeDO) && !permissionId.equals(permissionCodeDO.getId())) {
            throw new CommonException(CommonErrorCodeEnum.VALIDATE_ERROR, "该菜单[" + permissionCode + "]编码已存在");
        }
        FunctionStatusEnum functionStatus = action.getFunctionStatus();
        List<SaveFunctionPermissionAction> functionPermissionList = action.getFunctionPermissionList();
        List<SaveFunctionPermissionAction> checkFunctionPermissionList = Lists.newArrayList();
        checkFunctionPermissionList.addAll(functionPermissionList);
        SaveFunctionPermissionAction saveFunctionPermissionAction = new SaveFunctionPermissionAction();
        saveFunctionPermissionAction.setPermissionId(action.getPermissionId());
        saveFunctionPermissionAction.setPermissionCode(action.getPermissionCode());
        checkFunctionPermissionList.add(saveFunctionPermissionAction);
        if (FunctionStatusEnum.是.equals(functionStatus) && CollectionUtils.isNotEmpty(functionPermissionList)) {
            this.functionPermissionCheck(companyId, permissionId, checkFunctionPermissionList);
        }
        PermissionEntity menuPermissionEntity = new PermissionEntity();
        menuPermissionEntity.setPermissionId(permissionId);
        menuPermissionEntity.setCompanyId(companyId);
        menuPermissionEntity.setPermissionName(action.getPermissionName());
        menuPermissionEntity.setPermissionCode(action.getPermissionCode());
        menuPermissionEntity.setSortIndex(action.getSortIndex());
        menuPermissionEntity.setFunctionStatus(action.getFunctionStatus().getCode().byteValue());
        menuPermissionEntity.setResourceUrl(action.getResourceUrl());
        menuPermissionEntity.setResourceIcon(action.getResourceIcon());
        menuPermissionEntity.setModifier(action.getModifier());
        this.permissionRepository.editPermissionEntity(menuPermissionEntity);
        menuPermissionEntity.setPermissionId(permissionId);
        menuPermissionEntity.setCompanyId(companyId);
        List<FunctionPermissionVO> functionPermissionVOList = this.convertFunctionPermission(companyId, permissionId, action.getModifier(), functionPermissionList);
        menuPermissionEntity.setFunctionPermissionList(functionPermissionVOList);
        this.permissionRepository.saveFunctionPermissionEntity(menuPermissionEntity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delPermissionById(DelPermissionByIdAction action) {
        Long companyId = action.getCompanyId();
        Long permissionId = action.getPermissionId();
        AccPermissionDO permissionDO = this.accPermissionMapper.selectByPermissionId(companyId, permissionId, DeleteStatusEnum.否.getCode().byteValue());
        if (Objects.isNull(permissionDO)) {
            throw new CommonException(CommonErrorCodeEnum.VALIDATE_ERROR, "该菜单权限信息不存在");
        }
        List<AccPermissionDO> allPermission = this.accPermissionMapper.listIncludeChildByPermissionId(companyId, permissionId, DeleteStatusEnum.否.getCode().byteValue());
        this.delPermissionCode(companyId, action.getModifier(), allPermission);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchDelPermissionById(BatchDelPermissionByIdAction action) {
        Long companyId = action.getCompanyId();
        List<Long> permissionIds = action.getPermissionIds();
        List<AccPermissionDO> menuPermissionDOList = this.accPermissionMapper.listByPermissionIds(companyId, permissionIds, DeleteStatusEnum.否.getCode().byteValue());
        if (CollectionUtils.isEmpty(menuPermissionDOList) || permissionIds.size() != menuPermissionDOList.size()) {
            throw new CommonException(CommonErrorCodeEnum.VALIDATE_ERROR, "该菜单权限信息不存在");
        }
        List<AccPermissionDO> allPermission = this.accPermissionMapper.listIncludeChildByPermissionIds(companyId, permissionIds, DeleteStatusEnum.否.getCode().byteValue());
        this.delPermissionCode(companyId, action.getModifier(), allPermission);
    }

    @Override
    public SimplePageInfo<PermissionPageDTO> queryPageByCondition(QueryPagePermissionByCondition action) {
        Long companyId = action.getCompanyId();
        Long permissionId = action.getPermissionId();
        if(Objects.isNull(permissionId)){
            AccPermissionDO accPermissionDO = this.accPermissionMapper.selectRootByCompanyId(companyId,DeleteStatusEnum.否.getCode().byteValue());
            if(Objects.isNull(accPermissionDO)){
                return new SimplePageInfo<>();
            }
            permissionId = accPermissionDO.getId();
        }
        AccPermissionDO permission = this.accPermissionMapper.selectByPermissionId(companyId, permissionId, DeleteStatusEnum.否.getCode().byteValue());
        if (Objects.isNull(permission)) {
            throw new CommonException(CommonErrorCodeEnum.VALIDATE_ERROR, "该菜单权限信息不存在");
        }
        action.startPage();
        List<AccPermissionDO> permissionDOList = this.accPermissionMapper.listByCondition(companyId, permissionId,
                action.getPermissionName(), action.getLevel(), PermissionTypeEnum.MENU.getCode(), action.getSortBy(),
                DeleteStatusEnum.否.getCode().byteValue());
        if (CollectionUtils.isEmpty(permissionDOList)) {
            return new SimplePageInfo<>();
        }
        List<AccPermissionDO> parentPermissionList = this.accPermissionMapper.listIncludeChildByPermissionId(companyId, permissionId, DeleteStatusEnum.否.getCode().byteValue());
        Map<Long, String> parentPermissionMap = parentPermissionList.stream().collect(Collectors.toMap(AccPermissionDO::getId,
                AccPermissionDO::getPermissionName, (key1, key2) -> key2));
        SimplePageInfo<PermissionPageDTO> pageInfo = SimplePageInfoUtils
                .convertSimplePageInfoResult(permissionDOList, PermissionPageDTO.class);
        List<PermissionPageDTO> permissionPageDTOList = permissionDOList.stream().map(permissionDO -> {
            PermissionPageDTO permissionPageDTO = BeanCopyUtils.copyByJSON(permissionDO, PermissionPageDTO.class);
            permissionPageDTO.setPermissionId(permissionDO.getId());
            if (Objects.nonNull(permissionDO.getParentId())) {
                permissionPageDTO.setParentName(parentPermissionMap.get(permissionDO.getParentId()));
            }
            if (Objects.nonNull(permissionDO.getLevel())) {
                permissionPageDTO.setLevelName(PermissionLevelEnum.fromCode(permissionDO.getLevel().intValue()).getDesc());
            }
            if (Objects.nonNull(permissionDO.getFunctionStatus())) {
                permissionPageDTO.setFunctionStatusDesc(FunctionStatusEnum.fromCode(permissionDO.getFunctionStatus().intValue()).getDesc());
            }
            return permissionPageDTO;
        }).collect(Collectors.toList());
        pageInfo.setList(permissionPageDTOList);
        return pageInfo;
    }

    @Override
    public PermissionDTO queryByPermissionId(Long companyId, Long permissionId) {
        AccPermissionDO permission = this.accPermissionMapper.selectByPermissionId(companyId, permissionId, DeleteStatusEnum.否.getCode().byteValue());
        if (Objects.isNull(permission)) {
            return null;
        }
        PermissionDTO permissionDTO = BeanCopyUtils.copyByJSON(permission, PermissionDTO.class);
        permissionDTO.setPermissionId(permission.getId());
        if (Objects.nonNull(permissionDTO.getParentId())) {
            AccPermissionDO parentPermission = this.accPermissionMapper.selectByPermissionId(companyId, permissionDTO.getParentId(), DeleteStatusEnum.否.getCode().byteValue());
            if (Objects.nonNull(parentPermission)) {
                permissionDTO.setParentName(parentPermission.getPermissionName());
            }
        }
        if (Objects.nonNull(permission.getLevel())) {
            permissionDTO.setLevelName(PermissionLevelEnum.fromCode(permission.getLevel().intValue()).getDesc());
        }
        if (Objects.isNull(permission.getFunctionStatus()) || FunctionStatusEnum.否.getCode().byteValue() == permission.getFunctionStatus()) {
            permissionDTO.setFunctionPermissionList(Lists.newArrayList());
            return permissionDTO;
        }
        List<AccPermissionDO> permissionDOList = this.accPermissionMapper.listDirectChildByPermissionId(companyId, permissionId,
                DeleteStatusEnum.否.getCode().byteValue(),PermissionTypeEnum.FCN.getCode());
        if (CollectionUtils.isEmpty(permissionDOList)) {
            permissionDTO.setFunctionPermissionList(Lists.newArrayList());
            return permissionDTO;
        }
        List<FunctionPermissionDTO> functionPermissionList = permissionDOList.stream().map(permissionDO -> {
            FunctionPermissionDTO functionPermissionDTO = BeanCopyUtils.copyByJSON(permissionDO, FunctionPermissionDTO.class);
            functionPermissionDTO.setPermissionId(permissionDO.getId());
            return functionPermissionDTO;
        }).collect(Collectors.toList());
        permissionDTO.setFunctionPermissionList(functionPermissionList);
        return permissionDTO;
    }

    @Override
    public List<PermissionDTO> queryDirectChildByPermissionId(Long companyId, Long permissionId) {
        AccPermissionDO permission = this.accPermissionMapper.selectByPermissionId(companyId, permissionId, DeleteStatusEnum.否.getCode().byteValue());
        if (Objects.isNull(permission)) {
            return Lists.newArrayList();
        }
        List<AccPermissionDO> functionPermissionList = this.accPermissionMapper.listDirectChildByPermissionId(companyId, permissionId,
                DeleteStatusEnum.否.getCode().byteValue(),null);
        if (CollectionUtils.isEmpty(functionPermissionList)) {
            return Lists.newArrayList();
        }
        return functionPermissionList.stream().map(functionPermission -> {
            PermissionDTO permissionDTO = BeanCopyUtils.copyByJSON(functionPermission, PermissionDTO.class);
            permissionDTO.setPermissionId(functionPermission.getId());
            permissionDTO.setParentName(permission.getPermissionName());
            return permissionDTO;
        }).collect(Collectors.toList());
    }

    @Override
    public PermissionTreeDTO queryTreeByPermissionType(Long companyId, PermissionTypeEnum permissionType) {
        AccPermissionDO rootPermissionDO = this.accPermissionMapper.selectRootByCompanyId(companyId, DeleteStatusEnum.否.getCode().byteValue());
        if (Objects.isNull(rootPermissionDO)) {
            return null;
        }
        return this.queryTreeByPermissionId(companyId, rootPermissionDO.getId(), Objects.isNull(permissionType) ? null : permissionType.getCode());
    }

    @Override
    public PermissionTreeDTO queryTreeByPermissionId(Long companyId, Long permissionId, String permissionType) {
        AccPermissionDO permission = this.accPermissionMapper.selectByPermissionId(companyId, permissionId, DeleteStatusEnum.否.getCode().byteValue());
        if (Objects.isNull(permission)) {
            return null;
        }
        List<AccPermissionDO> permissionDOList = this.accPermissionMapper.listByCondition(companyId, permissionId,
                null, null, permissionType, "sort_index asc", DeleteStatusEnum.否.getCode().byteValue());
        if (CollectionUtils.isEmpty(permissionDOList)) {
            PermissionTreeDTO permissionTreeDTO = BeanCopyUtils.copyByJSON(permission, PermissionTreeDTO.class);
            permissionTreeDTO.setPermissionId(permission.getId());
            permissionTreeDTO.setChildPermissionList(Lists.newArrayList());
            return permissionTreeDTO;
        }
        Long parentId = permission.getParentId();
        permission.setParentId(null);
        permissionDOList.add(permission);
        List<PermissionTreeDTO> permissionDTOList = this.buildPermissionTree(permissionDOList, false);
        if (CollectionUtils.isEmpty(permissionDTOList)) {
            return null;
        }
        permission.setParentId(parentId);
        return permissionDTOList.get(0);
    }
    private List<PermissionTreeDTO> buildPermissionTree(List<AccPermissionDO> permissionDOList, Boolean doCount) {
        List<PermissionTreeDTO> permissionTreeDTOList = this.convertPermissionTreeDTO(permissionDOList);
        if (CollectionUtils.isEmpty(permissionTreeDTOList)) {
            return permissionTreeDTOList;
        }
        List<PermissionTreeDTO> permissionTreeList = new ArrayList<>();
        for (PermissionTreeDTO node : permissionTreeDTOList) {
            boolean rootFlag = false;
            if (Boolean.TRUE.equals(doCount)) {
            }
            for (PermissionTreeDTO parentNode : permissionTreeDTOList) {
                if (Objects.nonNull(node.getParentId()) && node.getParentId().equals(parentNode.getPermissionId())) {
                    rootFlag = true;
                    if (parentNode.getChildPermissionList() == null) {
                        parentNode.setChildPermissionList(Lists.newArrayList());
                    }
                    parentNode.getChildPermissionList().add(node);
                    break;
                }
            }
            if (!rootFlag) {
                permissionTreeList.add(node);
            }
        }
        return permissionTreeList;
    }
    private List<PermissionTreeDTO> convertPermissionTreeDTO(List<AccPermissionDO> permissionDOList) {
        if (CollectionUtils.isEmpty(permissionDOList)) {
            return Lists.newArrayList();
        }
        return permissionDOList.stream().map(permissionDO -> {
            PermissionTreeDTO permissionTreeDTO = BeanCopyUtils.copyByJSON(permissionDO, PermissionTreeDTO.class);
            permissionTreeDTO.setPermissionId(permissionDO.getId());
            permissionTreeDTO.setChildPermissionList(Lists.newArrayList());
            return permissionTreeDTO;
        }).collect(Collectors.toList());
    }
    private void functionPermissionCheck(Long companyId, Long permissionId, List<SaveFunctionPermissionAction> functionPermissionList) {
        if (CollectionUtils.isEmpty(functionPermissionList)) {
            return;
        }
        List<String> permissionCodeLists = functionPermissionList.stream().map(SaveFunctionPermissionAction::getPermissionCode).distinct().collect(Collectors.toList());
        if(functionPermissionList.size() != permissionCodeLists.size()){
            throw new CommonException(CommonErrorCodeEnum.VALIDATE_ERROR, "权限编码重复");
        }

        functionPermissionList.forEach(functionPermission -> {
            AccPermissionDO functionPermissionDO = this.accPermissionMapper.selectByPermissionCode(companyId, functionPermission.getPermissionCode(),
                    DeleteStatusEnum.否.getCode().byteValue());
            if (Objects.nonNull(functionPermissionDO) && !functionPermissionDO.getId().equals(functionPermission.getPermissionId())) {
                throw new CommonException(CommonErrorCodeEnum.VALIDATE_ERROR, "该菜单功能[" + functionPermission.getPermissionCode() + "]编码已存在");
            }
        });
        List<String> newFunctionPermissionCodes = functionPermissionList.stream()
                .map(SaveFunctionPermissionAction::getPermissionCode).collect(Collectors.toList());
        List<AccPermissionDO> oldFunctionPermissionList = this.accPermissionMapper.listDirectChildByPermissionId(companyId, permissionId,
                DeleteStatusEnum.否.getCode().byteValue(), PermissionTypeEnum.FCN.getCode());
        if (CollectionUtils.isNotEmpty(oldFunctionPermissionList)) {
            List<String> oldFunctionPermissionCodes = oldFunctionPermissionList.stream().map(AccPermissionDO::getPermissionCode).collect(Collectors.toList());
            oldFunctionPermissionCodes.removeAll(newFunctionPermissionCodes);
            if(CollectionUtils.isNotEmpty(oldFunctionPermissionCodes)){
                List<AccRolePermissionRelDO> rolePermissionRelDOList = this.accRolePermissionRelMapper.listByPermissionCodes(companyId, oldFunctionPermissionCodes);
                if (Objects.nonNull(rolePermissionRelDOList) && rolePermissionRelDOList.size() > 0) {
                    String oldFunctionPermissionCode = String.join(",", oldFunctionPermissionCodes);
                    throw new CommonException(CommonErrorCodeEnum.VALIDATE_ERROR, "该菜单功能[" + oldFunctionPermissionCode + "]编码已被使用, 请解绑后重试");
                }
            }
        }
    }
    private List<FunctionPermissionVO> convertFunctionPermission(Long companyId, Long permissionId, Long modifier, List<SaveFunctionPermissionAction> functionPermissionList) {
        if (CollectionUtils.isEmpty(functionPermissionList)) {
            return Lists.newArrayList();
        }
        return functionPermissionList.stream().map(functionPermission -> {
            FunctionPermissionVO functionPermissionVO = new FunctionPermissionVO();
            functionPermissionVO.setCompanyId(companyId);
            functionPermissionVO.setPermissionName(functionPermission.getPermissionName());
            functionPermissionVO.setPermissionCode(functionPermission.getPermissionCode());
            functionPermissionVO.setPermissionType(functionPermission.getPermissionType().getCode());
            functionPermissionVO.setParentId(permissionId);
            functionPermissionVO.setLevel(functionPermission.getLevel());
            functionPermissionVO.setSortIndex(functionPermission.getSortIndex());
            functionPermissionVO.setPermissionStatus(AccountStatusEnum.启用.getCode().byteValue());
            functionPermissionVO.setFunctionStatus(FunctionStatusEnum.否.getCode().byteValue());
            functionPermissionVO.setIsDelete(DeleteStatusEnum.否.getCode().byteValue());
            functionPermissionVO.setCreator(modifier);
            functionPermissionVO.setModifier(modifier);
            return functionPermissionVO;
        }).collect(Collectors.toList());
    }
    private void delPermissionCode(Long companyId, Long modifier, List<AccPermissionDO> allPermission) {
        if (CollectionUtils.isEmpty(allPermission)) {
            return;
        }
        List<String> allPermissionCodes = allPermission.stream().map(AccPermissionDO::getPermissionCode).collect(Collectors.toList());
        List<AccRolePermissionRelDO> rolePermissionRelDOList = this.accRolePermissionRelMapper.listByPermissionCodesGroupByRoleId(companyId, allPermissionCodes);
        if (CollectionUtils.isNotEmpty(rolePermissionRelDOList)) {
            // 如果角色已经被删除，则排除在外
            List<Long> existRoleCodeList = rolePermissionRelDOList.stream().map(AccRolePermissionRelDO::getRoleId).collect(Collectors.toList());
            List<AccRoleDO> roleDOList = this.accRoleMapper.listByRoleIds(companyId, existRoleCodeList, (byte)1);
            if(CollectionUtils.isNotEmpty(roleDOList)){
                roleDOList.forEach(accRoleDO -> existRoleCodeList.remove(accRoleDO.getId()));

                // 删除已经无效的关联
                List<Long> delRoleIds = roleDOList.stream().map(AccRoleDO::getId).collect(Collectors.toList());
                if(CollectionUtils.isNotEmpty(delRoleIds)){
                    this.accRolePermissionRelMapper.deleteByRoleIds(companyId, delRoleIds);
                }
            }

            if(existRoleCodeList.size() > 0){
                String usePermissionCode = rolePermissionRelDOList.stream()
                        .map(AccRolePermissionRelDO::getPermissionCode).distinct().collect(Collectors.joining(","));
                throw new CommonException(CommonErrorCodeEnum.VALIDATE_ERROR, "编码为[" + usePermissionCode + "]菜单功能已被使用, 请解绑后重试");
            }
        }
        AccPermissionDO accPermissionDO = new AccPermissionDO();
        accPermissionDO.setIsDelete(DeleteStatusEnum.是.getCode().byteValue());
        accPermissionDO.setModifier(modifier);
        List<Long> allPermissionIds = allPermission.stream()
                .map(AccPermissionDO::getId).collect(Collectors.toList());
        this.accPermissionMapper.updateByPermissionIds(accPermissionDO, companyId, allPermissionIds);
    }
}
