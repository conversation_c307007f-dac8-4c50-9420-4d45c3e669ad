package com.das.province.service.biz.user.action;

import com.das.province.service.enums.AccountStatusEnum;
import com.das.province.service.enums.PostCodeEnum;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

@Data
public class AddUserAction implements Serializable {
    private static final long serialVersionUID = 1295071236269383437L;
    private Long companyId;
    private String userName;
    private String realName;
    private String loginAccount;
    private String password;
    private String sourcePassword;
    private Long avatar;
    private String phone;
    private String email;
    private Long departmentId;
    private Long positionId;
    private PostCodeEnum postCode;
    private String remark;
    private AccountStatusEnum accountStatus;
    private AccountStatusEnum isDelete;
    private Long creator;
    private Long modifier;

    /**
     * 角色ids
     */
    @NotNull(message = "角色不能为空")
    private List<Long> roleIds;

    /**
     * 职位名称
     */
    private String positionName;

    /**
     * 领导id
     */
    private Long leaderId;
}
