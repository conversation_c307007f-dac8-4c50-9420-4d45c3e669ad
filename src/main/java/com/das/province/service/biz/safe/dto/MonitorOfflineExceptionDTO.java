package com.das.province.service.biz.safe.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/7/11
 */
@Data
public class MonitorOfflineExceptionDTO implements Serializable {

    private static final long serialVersionUID = 3516945480676109809L;

    /**
     * 公司名称
     */
    private String museumName;

    /**
     * 公司编码
     */
    private String uniqueCode;

    /**
     * 所属地区编码
     */
    private String regionCode;

    /**
     * 所在区域(1展厅,2库房,3其他)
     */
    private Byte regionName;

    private String regionNameStr;

    /**
     * 设备编码
     */
    private String deviceCode;

    /**
     * 设备位置
     */
    private String deviceAddress;

    /**
     * 设备状态：1:在线, 0:离线
     */
    private Byte deviceStatus;

    /**
     * 设备状态：1:在线, 0:离线
     */
    private String deviceStatusStr;

    /**
     * 离线次数
     */
    private Integer deviceOfflineCount;

    /**
     * 上报离线时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date currReportTime;

    /**
     * 上报离线时间
     */
    private String currReportTimeStr;

    /**
     * 上报离线时间列表
     */
    private List<Date> reportTimeList;
}
