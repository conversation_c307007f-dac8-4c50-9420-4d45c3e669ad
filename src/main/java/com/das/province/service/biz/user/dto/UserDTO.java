package com.das.province.service.biz.user.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
public class UserDTO implements Serializable {
    private static final long serialVersionUID = -7463551901321243790L;
    private Long userId;
    private Long companyId;
    private String userName;
    private String loginAccount;
    private String password;
    private String phone;
    private Long departmentId;
    private Long positionId;
    private String postCode;
    private Byte accountStatus;
    private Byte isDelete;
    private Long creator;
    private Long modifier;
    private Date gmtCreate;
    private Date gmtModified;

    private String positionName;

    /**
     * 角色ids
     */
    private List<Long> roleIds;

    /**
     * 角色名称
     */
    private List<String> roleName;

    /**
     * 领导id
     */
    private Long leaderId;

    /**
     * 领导名字
     */
    private String leaderName;
}