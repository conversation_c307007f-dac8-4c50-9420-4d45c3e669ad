package com.das.province.service.biz.user.action;

import com.das.province.common.bo.PageInfoBaseReq;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class QueryRolePageByCondition extends PageInfoBaseReq {
    private static final long serialVersionUID = -4478266782098461790L;
    private Long companyId;
    private String roleName;
    private String sortBy;
}
