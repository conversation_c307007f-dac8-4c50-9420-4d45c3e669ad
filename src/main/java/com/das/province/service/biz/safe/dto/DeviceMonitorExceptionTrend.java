package com.das.province.service.biz.safe.dto;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023/7/14
 */
@Data
public class DeviceMonitorExceptionTrend {

    /**
     * 公司名称
     */
    private String museumName;

    /**
     * 设备位置
     */
    private String deviceAddress;

    /**
     * 监控设备异常数
     */
    private Integer monitorDeviceExceptionCount = 0;

    /**
     * 温度异常数
     */
    private Integer temperatureExceptionCount = 0;

    /**
     * 湿度异常数
     */
    private Integer humidityExceptionCount = 0;

    /**
     * 设备离线数
     */
    private Integer deviceOfflineCount = 0;

    /**
     * 轻度
     */
    private Integer mildExceptionCount = 0;

    /**
     * 中度
     */
    private Integer moderateExceptionCount = 0;

    /**
     * 重度
     */
    private Integer severeExceptionCount = 0;

    /**
     * 异常总数指标
     */
    private DegreeData monitorDeviceException;

    /**
     * 湿度异常数指标
     */
    private DegreeData humidityException;

    /**
     * 温度异常数指标
     */
    private DegreeData temperatureException;

    @Data
    public static class DegreeData {

        /**
         * 0:无异常, 1:轻度, 2:中度, 3:严重
         */
        private String exceptionDegree;

        /**
         * 异常数量
         */
        private Integer exceptionCount=0;

        /**
         * 轻度
         */
        private Integer mildExceptionCount = 0;

        /**
         * 中度
         */
        private Integer moderateExceptionCount = 0;

        /**
         * 重度
         */
        private Integer severeExceptionCount = 0;
    }
}
