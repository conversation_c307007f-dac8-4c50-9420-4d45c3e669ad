package com.das.province.service.biz.collection.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/02/09
 */
@Data
public class AudiencePortraitAgeDTO {

    /**
     * 个人入馆人数
     */
    private Long inMuseumNum;

    /**
     * 成年人数
     */
    private Long adultNum;

    /**
     * 成年人数量占比
     */
    private BigDecimal adultNumRatio;

    /**
     * 成年人环比
     */
    private BigDecimal adultNumRingRatio;

    /**
     * 成年人环比
     */
    private String adultNumRingRatioStr;

    /**
     * 成年人同比
     */
    private BigDecimal adultNumYoy;

    /**
     * 成年人同比
     */
    private String adultNumYoyStr;

    /**
     * 未成年人
     */
    private Long noAdultNum;

    /**
     * 未成年人数量占比
     */
    private BigDecimal noAdultNumRatio;

    /**
     * 未成年人环比
     */
    private BigDecimal noAdultNumRingRatio;

    /**
     * 未成年人环比
     */
    private String noAdultNumRingRatioStr;

    /**
     * 未成年人同比
     */
    private BigDecimal noAdultNumYoy;

    /**
     * 未成年人同比
     */
    private String noAdultNumYoyStr;

    /**
     * 男
     */
    private Long manNum;

    /**
     * 男占比
     */
    private BigDecimal manNumRatio;

    /**
     * 男占比
     */
    private String manNumRatioStr;

    /**
     * 女
     */
    private Long womanNum;

    /**
     * 女占比
     */
    private BigDecimal womanNumRatio;

    /**
     * 女占比
     */
    private String womanNumRatioStr;

    /**
     * 年龄分布
     */
    private List<AgeRangDTO> ageRangList;
}
