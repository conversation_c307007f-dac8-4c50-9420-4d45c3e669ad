package com.das.province.service.biz.user.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class PermissionDTO implements Serializable {
    private static final long serialVersionUID = 2756587586224998287L;
    private Long permissionId;
    private Long companyId;
    private String permissionName;
    private String permissionCode;
    private String permissionType;
    private Long parentId;
    private String parentName;
    private Byte level;
    private String levelName;
    private Byte sortIndex;
    private Byte functionStatus;
    private String resourceUrl;
    private String resourceIcon;
    private List<FunctionPermissionDTO> functionPermissionList;
}