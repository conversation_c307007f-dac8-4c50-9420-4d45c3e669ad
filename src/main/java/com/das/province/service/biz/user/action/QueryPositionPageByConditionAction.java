package com.das.province.service.biz.user.action;

import com.das.province.common.bo.PageInfoBaseReq;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class QueryPositionPageByConditionAction extends PageInfoBaseReq {
    private static final long serialVersionUID = -3412790482546302783L;
    private Long companyId;
    private Long departmentId;
    private String sortBy;
}
