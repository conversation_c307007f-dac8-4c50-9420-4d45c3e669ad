package com.das.province.service.biz.collection;

import com.das.province.common.bo.HolidayBO;
import com.das.province.common.bo.SimplePageInfo;
import com.das.province.common.exception.CommonErrorCodeEnum;
import com.das.province.common.exception.CommonException;
import com.das.province.common.utils.BeanCopyUtils;
import com.das.province.common.utils.DateUtils;
import com.das.province.common.utils.PaginationUtils;
import com.das.province.common.utils.SimplePageInfoUtils;
import com.das.province.infr.dataobject.*;
import com.das.province.infr.dataobjectexpand.GroupVisitorByCityDO;
import com.das.province.infr.dataobjectexpand.GroupVisitorByDateTypeDO;
import com.das.province.infr.dataobjectexpand.GroupVisitorByMuseumDO;
import com.das.province.infr.dataobjectexpand.GroupVisitorYearByCityDO;
import com.das.province.infr.mapper.*;
import com.das.province.service.biz.collection.action.QueryCulturalPageByConditionAction;
import com.das.province.service.biz.collection.dto.*;
import com.das.province.service.biz.museum.MuseumManageService;
import com.das.province.service.enums.ProvinceCodeEnum;
import com.das.province.web.controller.museum.response.MuseumPageVO;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/3/28
 */
@Slf4j
@Service
public class CollectionServiceImpl implements CollectionService, InitializingBean {

    @Resource
    private SyncCulturalRelicsInfoMapper syncCulturalRelicsInfoMapper;

    @Resource
    private SyncCommCollectionRegisterInfoMapper syncCommCollectionRegisterInfoMapper;

    @Resource
    private MuseumManageService museumManageService;

    @Resource
    private SyncOrderBaseMapper syncOrderBaseMapper;

    @Resource
    private MuseumBaseInfoMapper museumBaseInfoMapper;

    @Resource
    private SyncCulturalRelicsMapper syncCulturalRelicsMapper;

    @Resource
    private StatisticVisitorDayMapper statisticVisitorDayMapper;

    @Resource
    private StatisticVisitorMonthMapper statisticVisitorMonthMapper;

    @Resource
    private StatisticVisitorYearMapper statisticVisitorYearMapper;

    private final ConcurrentHashMap<Integer, List<HolidayBO>> holidaysMap = new ConcurrentHashMap<>(16);

    @Resource
    private Environment environment;

    @Value("${system.location.adCode}")
    private String locationAdCode;

    @Override
    public void afterPropertiesSet() throws Exception {
        holidayInit();
    }

    public void holidayInit(){
        // 2022
        List<HolidayBO> l2022 = Lists.newArrayList();
        HolidayBO newYear2022 = new HolidayBO();
        newYear2022.setName("元旦");
        newYear2022.setStartTime(DateUtils.parseDateYYYMMDDHHmmss(environment.getProperty("2022.holiday.newYearStart")));
        newYear2022.setEndTime(DateUtils.parseDateYYYMMDDHHmmss(environment.getProperty("2022.holiday.newYearEnd")));
        l2022.add(newYear2022);

        HolidayBO chineseNewYear2022 = new HolidayBO();
        chineseNewYear2022.setName("春节");
        chineseNewYear2022.setStartTime(DateUtils.parseDateYYYMMDDHHmmss(environment.getProperty("2022.holiday.chineseNewYearStart")));
        chineseNewYear2022.setEndTime(DateUtils.parseDateYYYMMDDHHmmss(environment.getProperty("2022.holiday.chineseNewYearEnd")));
        l2022.add(chineseNewYear2022);

        HolidayBO chingMing2022 = new HolidayBO();
        chingMing2022.setName("清明节");
        chingMing2022.setStartTime(DateUtils.parseDateYYYMMDDHHmmss(environment.getProperty("2022.holiday.chingMingStart")));
        chingMing2022.setEndTime(DateUtils.parseDateYYYMMDDHHmmss(environment.getProperty("2022.holiday.chingMingEnd")));
        l2022.add(chingMing2022);

        HolidayBO labor2022 = new HolidayBO();
        labor2022.setName("劳动节");
        labor2022.setStartTime(DateUtils.parseDateYYYMMDDHHmmss(environment.getProperty("2022.holiday.laborStart")));
        labor2022.setEndTime(DateUtils.parseDateYYYMMDDHHmmss(environment.getProperty("2022.holiday.laborEnd")));
        l2022.add(labor2022);

        HolidayBO dragonBoat2022 = new HolidayBO();
        dragonBoat2022.setName("劳动节");
        dragonBoat2022.setStartTime(DateUtils.parseDateYYYMMDDHHmmss(environment.getProperty("2022.holiday.dragonBoatStart")));
        dragonBoat2022.setEndTime(DateUtils.parseDateYYYMMDDHHmmss(environment.getProperty("2022.holiday.dragonBoatEnd")));
        l2022.add(dragonBoat2022);

        HolidayBO midAutumn2022 = new HolidayBO();
        midAutumn2022.setName("中秋节");
        midAutumn2022.setStartTime(DateUtils.parseDateYYYMMDDHHmmss(environment.getProperty("2022.holiday.midAutumnStart")));
        midAutumn2022.setEndTime(DateUtils.parseDateYYYMMDDHHmmss(environment.getProperty("2022.holiday.midAutumnEnd")));
        l2022.add(midAutumn2022);

        HolidayBO national2022 = new HolidayBO();
        national2022.setName("国庆节");
        national2022.setStartTime(DateUtils.parseDateYYYMMDDHHmmss(environment.getProperty("2022.holiday.nationalStart")));
        national2022.setEndTime(DateUtils.parseDateYYYMMDDHHmmss(environment.getProperty("2022.holiday.nationalEnd")));
        l2022.add(national2022);
        holidaysMap.put(2022, l2022);
        // 2023
        List<HolidayBO> l2023 = Lists.newArrayList();
        HolidayBO newYear2023 = new HolidayBO();
        newYear2023.setName("元旦");
        newYear2023.setStartTime(DateUtils.parseDateYYYMMDDHHmmss(environment.getProperty("2023.holiday.newYearStart")));
        newYear2023.setEndTime(DateUtils.parseDateYYYMMDDHHmmss(environment.getProperty("2023.holiday.newYearEnd")));
        l2023.add(newYear2023);

        HolidayBO chineseNewYear2023 = new HolidayBO();
        chineseNewYear2023.setName("春节");
        chineseNewYear2023.setStartTime(DateUtils.parseDateYYYMMDDHHmmss(environment.getProperty("2023.holiday.chineseNewYearStart")));
        chineseNewYear2023.setEndTime(DateUtils.parseDateYYYMMDDHHmmss(environment.getProperty("2023.holiday.chineseNewYearEnd")));
        l2023.add(chineseNewYear2023);

        HolidayBO chingMing2023 = new HolidayBO();
        chingMing2023.setName("清明节");
        chingMing2023.setStartTime(DateUtils.parseDateYYYMMDDHHmmss(environment.getProperty("2023.holiday.chingMingStart")));
        chingMing2023.setEndTime(DateUtils.parseDateYYYMMDDHHmmss(environment.getProperty("2023.holiday.chingMingEnd")));
        l2023.add(chingMing2023);

        HolidayBO labor2023 = new HolidayBO();
        labor2023.setName("劳动节");
        labor2023.setStartTime(DateUtils.parseDateYYYMMDDHHmmss(environment.getProperty("2023.holiday.laborStart")));
        labor2023.setEndTime(DateUtils.parseDateYYYMMDDHHmmss(environment.getProperty("2023.holiday.laborEnd")));
        l2023.add(labor2023);

        HolidayBO dragonBoat2023 = new HolidayBO();
        dragonBoat2023.setName("端午节");
        dragonBoat2023.setStartTime(DateUtils.parseDateYYYMMDDHHmmss(environment.getProperty("2023.holiday.dragonBoatStart")));
        dragonBoat2023.setEndTime(DateUtils.parseDateYYYMMDDHHmmss(environment.getProperty("2023.holiday.dragonBoatEnd")));
        l2023.add(dragonBoat2023);

        HolidayBO nationalPlus2023 = new HolidayBO();
        nationalPlus2023.setName("中秋节/国庆节");
        nationalPlus2023.setStartTime(DateUtils.parseDateYYYMMDDHHmmss(environment.getProperty("2023.holiday.nationalPlusStart")));
        nationalPlus2023.setEndTime(DateUtils.parseDateYYYMMDDHHmmss(environment.getProperty("2023.holiday.nationalPlusEnd")));
        l2023.add(nationalPlus2023);
        holidaysMap.put(2023, l2023);
    }

    @Override
    public SimplePageInfo<CollectionInfoListDTO> queryCollectionPageByCondition(QueryCulturalPageByConditionAction action) {
        List<String> uniqueCodes = Lists.newArrayList();
        String regionCode = action.getRegionCode();
        if (StringUtils.isNotBlank(action.getMuseumCode())) {
            uniqueCodes.add(action.getMuseumCode());
        }else if (StringUtils.isNotBlank(regionCode)) {
            List<MuseumPageVO> museumPageList = this.museumManageService.getByRegionCode(regionCode);
            if (CollectionUtils.isNotEmpty(museumPageList)) {
                museumPageList.forEach(k -> uniqueCodes.add(k.getMuseumId()));
            }
        }

        if(StringUtils.isNotBlank(action.getMuseumCode()) || StringUtils.isNotBlank(regionCode)){
            if(CollectionUtils.isEmpty(uniqueCodes)){
                return new SimplePageInfo<>();
            }
        }

        // 全省入驻博物馆
        if(CollectionUtils.isEmpty(uniqueCodes)){
            List<MuseumBaseInfoDO> museumBaseInfoDOList =  museumBaseInfoMapper.selectSettleInList((byte)1,null);
            museumBaseInfoDOList.forEach(s -> uniqueCodes.add(s.getId()));
        }

        // 判断是否有质地查询条件
        String selectTexture = null;
        List<Long> ids = Lists.newArrayList();
        if(StringUtils.isNotBlank(action.getTextureName())){
            selectTexture = "是";
            List<SyncCommCollectionRegisterInfoDO> registerInfoDOList = this.syncCommCollectionRegisterInfoMapper.selectList();
            ids = selectRegisterInfoIdsByTexture(action.getTextureName(),registerInfoDOList);
            if(CollectionUtils.isEmpty(ids)){
                return new SimplePageInfo<>();
            }
        }

        action.startPage();
        List<SyncCommCollectionRegisterInfoDO> collectionInfoDOList = this.syncCommCollectionRegisterInfoMapper.queryByCondition(uniqueCodes,action.getCollectionName(),
                action.getLevelName(),action.getCategoryName(),action.getAge(),selectTexture, ids,action.getCompleteDegreeName(),action.getSourceName());

        if (CollectionUtils.isEmpty(collectionInfoDOList)) {
            return new SimplePageInfo<>();
        }
        List<String> uniqueCodeArr = Lists.newArrayList();
        collectionInfoDOList.forEach(c -> {
            uniqueCodeArr.add(c.getUniqueCode());
            String coverUrl = c.getCoverUrl();
            if (StringUtils.isNotBlank(coverUrl)) {
                String coverName = coverUrl.substring(coverUrl.lastIndexOf("/") + 1, coverUrl.lastIndexOf("."));
                c.setCoverCompressionUrl(coverUrl.substring(0, coverUrl.lastIndexOf("/")) + "/0.5_" + coverName + ".jpg");
            }
        });
        List<MuseumPageVO> museumPageVOList = this.museumManageService.getByUniqueCodes(uniqueCodeArr);
        Map<String, String> museumNameMap = Maps.newHashMap();
        museumPageVOList.forEach(k -> museumNameMap.put(k.getMuseumId(), k.getMuseumName()));
        SimplePageInfo<CollectionInfoListDTO> pageInfo = SimplePageInfoUtils.convertSimplePageInfoResult(collectionInfoDOList, CollectionInfoListDTO.class);

        List<CollectionInfoListDTO> collectionInfoListDTOList = collectionInfoDOList.stream().map(collectionInfoDO ->{
            CollectionInfoListDTO collectionInfoListDTO = BeanCopyUtils.copyByJSON(collectionInfoDO,CollectionInfoListDTO.class);
            collectionInfoListDTO.setLevelName(collectionInfoDO.getIdentifyLevelName());
            if(museumNameMap.containsKey(collectionInfoDO.getUniqueCode())){
                collectionInfoListDTO.setMuseumName(museumNameMap.get(collectionInfoDO.getUniqueCode()));
            }

            if(StringUtils.isNotBlank(collectionInfoDO.getTwoModelUrl())){
                collectionInfoListDTO.setTwoModelUrlList(Arrays.asList(collectionInfoDO.getTwoModelUrl().split(",")));
            }

            if(StringUtils.isNotBlank(collectionInfoDO.getThreeModelUrl())){
                collectionInfoListDTO.setThreeModelUrlList(Arrays.asList(collectionInfoDO.getThreeModelUrl().split(",")));
            }

            return collectionInfoListDTO;
        }).collect(Collectors.toList());
        pageInfo.setList(collectionInfoListDTOList);
        return pageInfo;
    }

    private List<Long> selectRegisterInfoIdsByTexture(String texture,List<SyncCommCollectionRegisterInfoDO> attrInfoDOList){
        List<Long> registerInfoIds = Lists.newArrayList();
        if(CollectionUtils.isEmpty(attrInfoDOList)){
            return registerInfoIds;
        }
        List<String> selectTextures = Arrays.asList(texture.split(","));
        attrInfoDOList.forEach(s -> {
            if(StringUtils.isNotBlank(s.getTextureName())){
                List<String> textures = Arrays.asList(s.getTextureName().split(","));
                if(textures.containsAll(selectTextures)){
                    registerInfoIds.add(s.getId());
                }
            }
        });
        return registerInfoIds;
    }

    @Override
    public List<HolidayDTO> holidayList(Integer year, String museumCode, String regionCode) {
        List<HolidayBO> holidayBOList = holidaysMap.get(year);
        if (CollectionUtils.isEmpty(holidayBOList)) {
            return Lists.newArrayList();
        }
        List<String> uniqueCodes = Lists.newArrayList();
        if (StringUtils.isNotBlank(museumCode)) {
            uniqueCodes.add(museumCode);
        }else if (StringUtils.isNotBlank(regionCode)) {
            List<MuseumPageVO> museumPageList = this.museumManageService.getByRegionCode(regionCode);
            if (CollectionUtils.isNotEmpty(museumPageList)) {
                museumPageList.forEach(k -> uniqueCodes.add(k.getMuseumId()));
            }
        }

        if(StringUtils.isNotBlank(museumCode) || StringUtils.isNotBlank(regionCode)){
            if(CollectionUtils.isEmpty(uniqueCodes)){
                return Lists.newArrayList();
            }
        }

        List<HolidayDTO> result = Lists.newArrayList();
        holidayBOList.forEach(v -> {
            HolidayDTO holidayDTO = new HolidayDTO();
            holidayDTO.setHolidayName(v.getName());
            List<SyncOrderBaseDO> personalOrderList = this.syncOrderBaseMapper.listByReserveDate((byte)2,
                    v.getStartTime(), v.getEndTime(), uniqueCodes, null);
            long personalCount = CollectionUtils.isEmpty(personalOrderList) ? 0 : personalOrderList.stream().mapToLong(SyncOrderBaseDO::getTotalPeopleCount).sum();
            holidayDTO.setInNum(personalCount);
            holidayDTO.setYoy((personalCount) * 1.0);
            holidayDTO.setYoyStr("-");
            result.add(holidayDTO);
        });
        List<HolidayBO> previousHolidayBOList = holidaysMap.get(year-1);
        if (CollectionUtils.isEmpty(previousHolidayBOList)) {
            return result;
        }
        List<HolidayDTO> resultOld = Lists.newArrayList();
        previousHolidayBOList.forEach(v -> {
            HolidayDTO holidayDTO = new HolidayDTO();
            holidayDTO.setHolidayName(v.getName());
            List<SyncOrderBaseDO> personalOrderList = this.syncOrderBaseMapper.listByReserveDate((byte)2,
                    v.getStartTime(), v.getEndTime(), uniqueCodes, null);
            long personalCount = CollectionUtils.isEmpty(personalOrderList) ? 0 : personalOrderList.stream().mapToLong(SyncOrderBaseDO::getTotalPeopleCount).sum();
            holidayDTO.setInNum(personalCount);
            holidayDTO.setYoy((personalCount) * 1.0);
            resultOld.add(holidayDTO);
        });
        if (result.size() == resultOld.size()) {
            for (int i=0; i<result.size(); i++) {
                long v1 = result.get(i).getInNum();
                long v2 = resultOld.get(i).getInNum();
                if (v2 == 0) {
                    result.get(i).setYoyStr("-");
                    continue;
                }
                BigDecimal bigDecimal = BigDecimal.valueOf((v1 - v2) / (v2 * 1.0));
                double v3 = bigDecimal.setScale(4, RoundingMode.HALF_UP).doubleValue() * 100;
                result.get(i).setYoy(v3);
                result.get(i).setYoyStr(v3 + "%");
            }
        } else if (result.size() -1 == resultOld.size()) {
            for (int i=0; i<result.size(); i++) {
                if (i == resultOld.size()-1) {
                    result.get(i).setYoy(0.0);
                    result.get(i).setYoyStr("-");
                    continue;
                }
                long v1 = result.get(i).getInNum();
                long v2 = 0L;
                if (i == result.size()-1) {
                    v2 = resultOld.get(i-1).getInNum();
                } else {
                    v2 = resultOld.get(i).getInNum();
                }
                if (v2 == 0) {
                    result.get(i).setYoyStr("-");
                    continue;
                }
                BigDecimal bigDecimal = BigDecimal.valueOf((v1 - v2) / (v2 * 1.0));
                double v3 = bigDecimal.setScale(4, RoundingMode.HALF_UP).doubleValue() * 100;
                result.get(i).setYoy(v3);
                result.get(i).setYoyStr(v3 + "%");
            }
        } else if (result.size() + 1 == resultOld.size()) {
            for (int i=0; i<result.size(); i++) {
                long v1 = result.get(i).getInNum();
                long v2;
                if (i == result.size()-1) {
                    v2 = resultOld.get(i).getInNum() + resultOld.get(i+1).getInNum();
                } else {
                    v2 = resultOld.get(i).getInNum();
                }
                if (v2 == 0) {
                    result.get(i).setYoyStr("-");
                    continue;
                }
                BigDecimal bigDecimal = BigDecimal.valueOf((v1 - v2) / (v2 * 1.0));
                double v3 = bigDecimal.setScale(4, RoundingMode.HALF_UP).doubleValue() * 100;
                result.get(i).setYoy(v3);
                result.get(i).setYoyStr(v3 + "%");
            }
        }
        return result;
    }

    @Override
    public AudiencePortraitAreaDTO audiencePortraitArea(Integer statisticsWay, String startTime, String endTime, String museumCode, String regionCode) {
        AudiencePortraitAreaDTO result = new AudiencePortraitAreaDTO();
        String locationAd;
        List<String> uniqueCodes = Lists.newArrayList();
        if (StringUtils.isNotBlank(museumCode)) {
            MuseumBaseInfoDO museumBaseInfoDO = museumBaseInfoMapper.selectByPrimaryKey(museumCode);
            locationAd = museumBaseInfoDO.getRegionCode();
            uniqueCodes.add(museumCode);
        }else if (StringUtils.isNotBlank(regionCode)) {
            locationAd = regionCode;
            List<MuseumPageVO> museumPageList = this.museumManageService.getByRegionCode(regionCode);
            if (CollectionUtils.isNotEmpty(museumPageList)) {
                museumPageList.forEach(k -> uniqueCodes.add(k.getMuseumId()));
            }
        }else{
            if (StringUtils.isBlank(locationAdCode)) {
                throw new CommonException(CommonErrorCodeEnum.VALIDATE_ERROR, "场馆地理位置未配置");
            }
            locationAd = locationAdCode;
        }


        if(StringUtils.isNotBlank(museumCode) || StringUtils.isNotBlank(regionCode)){
            if(CollectionUtils.isEmpty(uniqueCodes)){
                return result;
            }
        }

        Date timeStart;
        Date timeEnd = new Date();
        //根据类型获取不同维度的统计数据
        if(statisticsWay == 0){
            //今日
            timeStart = DateUtils.getTimesDayMorning();
            timeEnd = DateUtils.getTimesDayNight();
        }else if(statisticsWay == 3){
            //本年
            timeStart = DateUtils.getTimesYearMorning();
        }else if(statisticsWay == 6){
            //昨天
            timeStart = DateUtils.getTimesDayMorning();
            timeEnd = DateUtils.getTimesDayNight();
            Calendar calYoy = Calendar.getInstance();
            calYoy.setTime(timeStart);
            calYoy.add(Calendar.DAY_OF_YEAR, -1);
            timeStart = calYoy.getTime();
            calYoy = Calendar.getInstance();
            calYoy.setTime(timeEnd);
            calYoy.add(Calendar.DAY_OF_YEAR, -1);
            timeEnd = calYoy.getTime();
        }else{
            if(StringUtils.isBlank(startTime)){
                throw new CommonException(CommonErrorCodeEnum.VALIDATE_ERROR, "开始时间不能为空！");
            }
            if(StringUtils.isBlank(endTime)){
                throw new CommonException(CommonErrorCodeEnum.VALIDATE_ERROR, "结束时间不能为空！");
            }
            //自定义时间
            timeStart = DateUtils.parseDateYYYMMDDHHmmss(startTime);
            timeEnd = DateUtils.parseDateYYYMMDDHHmmss(endTime);
        }
        List<SyncOrderBaseDO> personalOrderList = this.syncOrderBaseMapper.listByReserveDate((byte)2, timeStart, timeEnd, uniqueCodes, (byte)1);

        if (CollectionUtils.isEmpty(personalOrderList)) {
            // AudiencePortraitAreaDTO audiencePortraitAreaDTO = new AudiencePortraitAreaDTO();
            result.setInMuseumNum(0L);
            result.setProvinceInNum(0L);
            result.setProvinceInNumRatio(BigDecimal.valueOf(0));
            result.setProvinceInRingRatio(BigDecimal.valueOf(0));
            result.setProvinceInYoy(BigDecimal.valueOf(0));
            result.setProvinceOutNum(0L);
            result.setProvinceOutNumRatio(BigDecimal.valueOf(0));
            result.setProvinceOutRingRatio(BigDecimal.valueOf(0));
            result.setProvinceOutYoy(BigDecimal.valueOf(0));
            result.setOverseasNum(0L);
            result.setOverseasNumRatio(BigDecimal.valueOf(0));
            result.setOverseasRingRatio(BigDecimal.valueOf(0));
            result.setOverseasYoy(BigDecimal.valueOf(0));
            //return audiencePortraitAreaDTO;
        } else {
            result.setInMuseumNum((long)personalOrderList.size());
            AtomicLong provinceInNum = new AtomicLong(0L);
            AtomicLong provinceOutNum = new AtomicLong(0L);
            AtomicLong overseasNum = new AtomicLong(0L);
            personalOrderList.forEach(v -> {
                Byte touristCertificateType = v.getTouristCertificateType();
                String touristCertificateNo = v.getTouristCertificateNo();
                if ((byte)1 == touristCertificateType) {
                    if (StringUtils.isEmpty(touristCertificateNo)) {
                        return;
                    }
                    if (touristCertificateNo.startsWith(locationAd)) {
                        provinceInNum.getAndIncrement();
                    } else {
                        provinceOutNum.getAndIncrement();
                    }
                } else if ((byte)3 == touristCertificateType) {
                    provinceOutNum.getAndIncrement();
                } else if((byte)2 == touristCertificateType) {
                    overseasNum.getAndIncrement();
                }
            });
            result.setProvinceInNum(provinceInNum.get());
            result.setProvinceOutNum(provinceOutNum.get());
            result.setOverseasNum(overseasNum.get());
            BigDecimal bigDecimal = BigDecimal.valueOf(provinceInNum.get() / (result.getInMuseumNum() * 1.0));
            result.setProvinceInNumRatio(bigDecimal.setScale(2, RoundingMode.HALF_UP));
            bigDecimal = BigDecimal.valueOf(provinceOutNum.get() / (result.getInMuseumNum() * 1.0));
            result.setProvinceOutNumRatio(bigDecimal.setScale(2, RoundingMode.HALF_UP));
            result.setOverseasNumRatio(new BigDecimal(1).subtract(result.getProvinceInNumRatio()).subtract(result.getProvinceOutNumRatio()));
        }

        Date ringRatioTimeStart;
        Date ringRatioTimeEnd;
        if(statisticsWay == 3){
            //本年
            Calendar temp = Calendar.getInstance();
            temp.setTime(new Date());
            temp.add(Calendar.YEAR, -1);
            Calendar ringRatioYoy = Calendar.getInstance();
            ringRatioYoy.clear();
            ringRatioYoy.set(Calendar.YEAR, temp.get(Calendar.YEAR));
            ringRatioYoy.roll(Calendar.DAY_OF_YEAR, -1);
            ringRatioTimeEnd = ringRatioYoy.getTime();

            ringRatioYoy = Calendar.getInstance();
            ringRatioYoy.clear();
            ringRatioYoy.set(Calendar.YEAR, temp.get(Calendar.YEAR));
            ringRatioTimeStart = ringRatioYoy.getTime();
        } else {
            return result;
        }
        List<SyncOrderBaseDO> personalOrderListTemp = this.syncOrderBaseMapper.listByReserveDate((byte)2, ringRatioTimeStart, ringRatioTimeEnd, uniqueCodes, (byte)1);
        if (CollectionUtils.isEmpty(personalOrderListTemp)) {
            result.setOverseasYoy(BigDecimal.valueOf(result.getOverseasNum()));
            result.setOverseasYoyStr("-");
            result.setProvinceInYoy(BigDecimal.valueOf(result.getProvinceInNum()));
            result.setProvinceInYoyStr("-");
            result.setProvinceOutYoy(BigDecimal.valueOf(result.getProvinceOutNum()));
            result.setProvinceOutYoyStr("-");
        } else {
            AtomicLong provinceInNumTemp = new AtomicLong(0L);
            AtomicLong provinceOutNumTemp = new AtomicLong(0L);
            AtomicLong overseasNumTemp = new AtomicLong(0L);
            personalOrderListTemp.forEach(v -> {
                Byte touristCertificateType = v.getTouristCertificateType();
                String touristCertificateNo = v.getTouristCertificateNo();
                if ((byte)1 == touristCertificateType) {
                    if (StringUtils.isEmpty(touristCertificateNo)) {
                        return;
                    }
                    if (touristCertificateNo.startsWith(locationAdCode)) {
                        provinceInNumTemp.getAndIncrement();
                    } else {
                        provinceOutNumTemp.getAndIncrement();
                    }
                } else if ((byte)3 == touristCertificateType) {
                    provinceOutNumTemp.getAndIncrement();
                } else {
                    overseasNumTemp.getAndIncrement();
                }
            });
            if (provinceInNumTemp.get() == 0) {
                result.setProvinceInYoy(BigDecimal.valueOf(result.getProvinceInNum()));
                result.setProvinceInYoyStr("-");
            } else {
                BigDecimal bigDecimalTemp = BigDecimal.valueOf((result.getProvinceInNum() - provinceInNumTemp.get()) / (provinceInNumTemp.get() * 1.0));
                BigDecimal v1 = bigDecimalTemp.setScale(4, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100));
                result.setProvinceInYoy(v1);
                result.setProvinceInYoyStr(v1 + "%");
            }
            if (provinceOutNumTemp.get() == 0) {
                result.setProvinceOutYoy(BigDecimal.valueOf(result.getProvinceOutNum()));
                result.setProvinceOutYoyStr("-");
            } else {
                BigDecimal bigDecimalTemp = BigDecimal.valueOf((result.getProvinceOutNum() - provinceOutNumTemp.get()) / (provinceOutNumTemp.get() * 1.0));
                BigDecimal v1 = bigDecimalTemp.setScale(4, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100));
                result.setProvinceOutYoy(v1);
                result.setProvinceOutYoyStr(v1 + "%");
            }
            if (overseasNumTemp.get() == 0) {
                result.setOverseasYoy(BigDecimal.valueOf(result.getOverseasNum()));
                result.setOverseasYoyStr("-");
            } else {
                BigDecimal bigDecimalTemp = BigDecimal.valueOf((result.getOverseasNum() - overseasNumTemp.get()) / (overseasNumTemp.get() * 1.0));
                BigDecimal v1 = bigDecimalTemp.setScale(4, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100));
                result.setOverseasYoy(v1);
                result.setOverseasYoyStr(v1 + "%");
            }
        }
        return result;
    }

    @Override
    public AudiencePortraitAgeDTO audiencePortraitAge(Integer statisticsWay, String startTime, String endTime, String museumCode, String regionCode) {
        AudiencePortraitAgeDTO result = new AudiencePortraitAgeDTO();
        List<String> uniqueCodes = Lists.newArrayList();
        if (StringUtils.isNotBlank(museumCode)) {
            uniqueCodes.add(museumCode);
        }
        if (StringUtils.isNotBlank(regionCode)) {
            List<MuseumPageVO> museumPageList = this.museumManageService.getByRegionCode(regionCode);
            if (CollectionUtils.isNotEmpty(museumPageList)) {
                museumPageList.forEach(k -> uniqueCodes.add(k.getMuseumId()));
            }
        }
        if(StringUtils.isNotBlank(museumCode) || StringUtils.isNotBlank(regionCode)){
            if(CollectionUtils.isEmpty(uniqueCodes)){
                return result;
            }
        }

        Date timeStart;
        Date timeEnd = new Date();
        //根据类型获取不同维度的统计数据
        if(statisticsWay == 0){
            //今日
            timeStart = DateUtils.getTimesDayMorning();
            timeEnd = DateUtils.getTimesDayNight();
        }else if(statisticsWay == 3){
            //本年
            timeStart = DateUtils.getTimesYearMorning();
        }else if(statisticsWay == 6){
            //昨天
            timeStart = DateUtils.getTimesDayMorning();
            timeEnd = DateUtils.getTimesDayNight();
            Calendar calYoy = Calendar.getInstance();
            calYoy.setTime(timeStart);
            calYoy.add(Calendar.DAY_OF_YEAR, -1);
            timeStart = calYoy.getTime();
            calYoy = Calendar.getInstance();
            calYoy.setTime(timeEnd);
            calYoy.add(Calendar.DAY_OF_YEAR, -1);
            timeEnd = calYoy.getTime();
        }else{
            if(StringUtils.isBlank(startTime)){
                throw new CommonException(CommonErrorCodeEnum.VALIDATE_ERROR, "开始时间不能为空！");
            }
            if(StringUtils.isBlank(endTime)){
                throw new CommonException(CommonErrorCodeEnum.VALIDATE_ERROR, "结束时间不能为空！");
            }
            //自定义时间
            timeStart = DateUtils.parseDateYYYMMDDHHmmss(startTime);
            timeEnd = DateUtils.parseDateYYYMMDDHHmmss(endTime);
        }
        List<SyncOrderBaseDO> personalOrderList = this.syncOrderBaseMapper.listByReserveDate((byte)2, timeStart, timeEnd, uniqueCodes, (byte)1);
        personalOrderList = personalOrderList.stream().filter(s -> s.getTouristCertificateType() == 1).collect(Collectors.toList());

        AgeRangDTO r1 = new AgeRangDTO("0-10岁", 0L, BigDecimal.valueOf(0), "0%");
        AgeRangDTO r2 = new AgeRangDTO("10-20岁", 0L, BigDecimal.valueOf(0), "0%");
        AgeRangDTO r3 = new AgeRangDTO("20-30岁", 0L, BigDecimal.valueOf(0), "0%");
        AgeRangDTO r4 = new AgeRangDTO("30-40岁", 0L, BigDecimal.valueOf(0), "0%");
        AgeRangDTO r5 = new AgeRangDTO("40-50岁", 0L, BigDecimal.valueOf(0), "0%");
        AgeRangDTO r6 = new AgeRangDTO("50-60岁", 0L, BigDecimal.valueOf(0), "0%");
        AgeRangDTO r7 = new AgeRangDTO("60-70岁", 0L, BigDecimal.valueOf(0), "0%");
        AgeRangDTO r8 = new AgeRangDTO("70岁", 0L, BigDecimal.valueOf(0), "0%");
        List<AgeRangDTO> ageRangList = Lists.newArrayList(r1, r2, r3, r4, r5, r6, r7, r8);
        result.setAgeRangList(ageRangList);
        if (CollectionUtils.isEmpty(personalOrderList)) {
            // AudiencePortraitAgeDTO audiencePortraitAgeDTO = new AudiencePortraitAgeDTO();
            result.setAdultNum(0L);
            result.setAdultNumRatio(BigDecimal.valueOf(0));
            result.setAdultNumRingRatio(BigDecimal.valueOf(0));
            result.setAdultNumYoy(BigDecimal.valueOf(0));
            result.setNoAdultNum(0L);
            result.setNoAdultNumRatio(BigDecimal.valueOf(0));
            result.setNoAdultNumRingRatio(BigDecimal.valueOf(0));
            result.setNoAdultNumYoy(BigDecimal.valueOf(0));
            result.setManNum(0L);
            result.setManNumRatio(BigDecimal.valueOf(0));
            result.setWomanNum(0L);
            result.setWomanNumRatio(BigDecimal.valueOf(0));
            // return audiencePortraitAgeDTO;
        } else {
            result.setInMuseumNum((long)personalOrderList.size());
            AtomicLong adultNum = new AtomicLong(0L);
            AtomicLong noAdultNum = new AtomicLong(0L);
            AtomicLong manNum = new AtomicLong(0L);
            AtomicLong womanNum = new AtomicLong(0L);
            AtomicLong l1 = new AtomicLong(0L);
            AtomicLong l2 = new AtomicLong(0L);
            AtomicLong l3 = new AtomicLong(0L);
            AtomicLong l4 = new AtomicLong(0L);
            AtomicLong l5 = new AtomicLong(0L);
            AtomicLong l6 = new AtomicLong(0L);
            AtomicLong l7 = new AtomicLong(0L);
            AtomicLong l8 = new AtomicLong(0L);
            personalOrderList.forEach(v -> {
                Byte touristCertificateType = v.getTouristCertificateType();
                String touristCertificateNo = v.getTouristCertificateNo();
                if ((byte)1 != touristCertificateType) {
                    return;
                }
                int age = countAge(touristCertificateNo);
                if (age == -1) {
                    return;
                }
                if (age < 18) {
                    noAdultNum.getAndIncrement();
                } else {
                    adultNum.getAndIncrement();
                }
                if (age > 0 && age <= 10) {
                    l1.incrementAndGet();
                } else if (age > 10 && age <= 20) {
                    l2.incrementAndGet();
                } else if (age > 20 && age <= 30) {
                    l3.incrementAndGet();
                } else if (age > 30 && age <= 40) {
                    l4.incrementAndGet();
                } else if (age > 40 && age <= 50) {
                    l5.incrementAndGet();
                } else if (age > 50 && age <= 60) {
                    l6.incrementAndGet();
                } else if (age > 60 && age <= 70) {
                    l7.incrementAndGet();
                } else {
                    l8.incrementAndGet();
                }
                String sex = getSex(touristCertificateNo);
                if ("男".equals(sex)) {
                    manNum.incrementAndGet();
                } else {
                    womanNum.incrementAndGet();
                }
            });
            BigDecimal bigDecimalL1 = BigDecimal.valueOf(l1.get() / (personalOrderList.size() * 1.0));
            r1.setKeyNum(l1.get());
            r1.setKeyNumRatio(bigDecimalL1.setScale(4, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100)));
            r1.setKeyNumRatioStr(r1.getKeyNumRatio() + "%");
            BigDecimal bigDecimalL2 = BigDecimal.valueOf(l2.get() / (personalOrderList.size() * 1.0));
            r2.setKeyNum(l2.get());
            r2.setKeyNumRatio(bigDecimalL2.setScale(4, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100)));
            r2.setKeyNumRatioStr(r2.getKeyNumRatio() + "%");
            BigDecimal bigDecimalL3 = BigDecimal.valueOf(l3.get() / (personalOrderList.size() * 1.0));
            r3.setKeyNum(l3.get());
            r3.setKeyNumRatio(bigDecimalL3.setScale(4, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100)));
            r3.setKeyNumRatioStr(r3.getKeyNumRatio() + "%");
            BigDecimal bigDecimalL4 = BigDecimal.valueOf(l4.get() / (personalOrderList.size() * 1.0));
            r4.setKeyNum(l4.get());
            r4.setKeyNumRatio(bigDecimalL4.setScale(4, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100)));
            r4.setKeyNumRatioStr(r4.getKeyNumRatio() + "%");
            BigDecimal bigDecimalL5 = BigDecimal.valueOf(l5.get() / (personalOrderList.size() * 1.0));
            r5.setKeyNum(l5.get());
            r5.setKeyNumRatio(bigDecimalL5.setScale(4, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100)));
            r5.setKeyNumRatioStr(r5.getKeyNumRatio() + "%");
            BigDecimal bigDecimalL6 = BigDecimal.valueOf(l6.get() / (personalOrderList.size() * 1.0));
            r6.setKeyNum(l6.get());
            r6.setKeyNumRatio(bigDecimalL6.setScale(4, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100)));
            r6.setKeyNumRatioStr(r6.getKeyNumRatio() + "%");
            BigDecimal bigDecimalL7 = BigDecimal.valueOf(l7.get() / (personalOrderList.size() * 1.0));
            r7.setKeyNum(l7.get());
            r7.setKeyNumRatio(bigDecimalL7.setScale(4, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100)));
            r7.setKeyNumRatioStr(r7.getKeyNumRatio() + "%");
            r8.setKeyNum(l8.get());
            r8.setKeyNumRatio(new BigDecimal(100).subtract(r1.getKeyNumRatio().add(r2.getKeyNumRatio())
                    .add(r3.getKeyNumRatio()).add(r4.getKeyNumRatio()).add(r5.getKeyNumRatio()).add(r6.getKeyNumRatio()).add(r7.getKeyNumRatio())));
            r8.setKeyNumRatioStr(r8.getKeyNumRatio() + "%");

            result.setInMuseumNum((long)personalOrderList.size());
            result.setAdultNum(adultNum.get());
            result.setNoAdultNum(noAdultNum.get());
            BigDecimal bigDecimal = BigDecimal.valueOf(adultNum.get() / (result.getInMuseumNum() * 1.0));
            result.setAdultNumRatio(bigDecimal.setScale(4, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100)));
            result.setNoAdultNumRatio(new BigDecimal(100).subtract(result.getAdultNumRatio()));

            result.setManNum(manNum.get());
            result.setWomanNum(womanNum.get());
            BigDecimal bigDecimal9 = BigDecimal.valueOf(manNum.get() / (result.getInMuseumNum() * 1.0));
            result.setManNumRatio(bigDecimal9.setScale(4, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100)));
            result.setManNumRatioStr(result.getManNumRatio() + "%");
            result.setWomanNumRatio(new BigDecimal(100).subtract(result.getManNumRatio()));
            result.setWomanNumRatioStr(result.getWomanNumRatio() + "%");
        }

        Date ringRatioTimeStart;
        Date ringRatioTimeEnd;
        if(statisticsWay == 3){
            //本年
            Calendar temp = Calendar.getInstance();
            temp.setTime(new Date());
            temp.add(Calendar.YEAR, -1);
            Calendar ringRatioYoy = Calendar.getInstance();
            ringRatioYoy.clear();
            ringRatioYoy.set(Calendar.YEAR, temp.get(Calendar.YEAR));
            ringRatioYoy.roll(Calendar.DAY_OF_YEAR, -1);
            ringRatioTimeEnd = ringRatioYoy.getTime();

            ringRatioYoy = Calendar.getInstance();
            ringRatioYoy.clear();
            ringRatioYoy.set(Calendar.YEAR, temp.get(Calendar.YEAR));
            ringRatioTimeStart = ringRatioYoy.getTime();
        } else {
            return result;
        }
        List<SyncOrderBaseDO> personalOrderListTemp = this.syncOrderBaseMapper.listByReserveDate((byte)2, ringRatioTimeEnd, ringRatioTimeStart, uniqueCodes, (byte)1);
        personalOrderListTemp = personalOrderList.stream().filter(s -> s.getTouristCertificateType() == 1).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(personalOrderListTemp)) {
            result.setAdultNumYoy(BigDecimal.valueOf(result.getAdultNum()));
            result.setAdultNumYoyStr("-");
            result.setNoAdultNumYoy(BigDecimal.valueOf(result.getNoAdultNum()));
            result.setNoAdultNumYoyStr("-");
        } else {
            AtomicLong adultNumTemp = new AtomicLong(0L);
            AtomicLong noAdultNumTemp = new AtomicLong(0L);
            personalOrderListTemp.forEach(v -> {
                Byte touristCertificateType = v.getTouristCertificateType();
                String touristCertificateNo = v.getTouristCertificateNo();
                if ((byte)1 != touristCertificateType) {
                    return;
                }
                int age = countAge(touristCertificateNo);
                if (age == -1) {
                    return;
                }
                if (age < 18) {
                    noAdultNumTemp.getAndIncrement();
                } else {
                    adultNumTemp.getAndIncrement();
                }
            });
            if (adultNumTemp.get() == 0) {
                result.setAdultNumYoy(BigDecimal.valueOf(result.getAdultNum()));
                result.setAdultNumYoyStr("-");
            } else {
                BigDecimal bigDecimalTemp = BigDecimal.valueOf((result.getAdultNum() - adultNumTemp.get()) / (adultNumTemp.get() * 1.0));
                BigDecimal v1 = bigDecimalTemp.setScale(4, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100));
                result.setAdultNumYoy(v1);
                result.setAdultNumYoyStr(v1 + "%");
            }
            if (noAdultNumTemp.get() == 0) {
                result.setNoAdultNumYoy(BigDecimal.valueOf(result.getNoAdultNum()));
                result.setNoAdultNumYoyStr("-");
            } else {
                BigDecimal bigDecimalTemp = BigDecimal.valueOf((result.getNoAdultNum() - noAdultNumTemp.get()) / (noAdultNumTemp.get() * 1.0));
                BigDecimal v1 = bigDecimalTemp.setScale(4, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100));
                result.setNoAdultNumYoy(v1);
                result.setNoAdultNumYoyStr(v1 + "%");
            }
        }
        return result;
    }

    @Override
    public List<StatisticsByProvinceDTO> statisticsByProvince(Integer statisticsWay, String startTime, String endTime, String museumCode, String regionCode) {
        List<String> uniqueCodes = Lists.newArrayList();
        if (StringUtils.isNotBlank(museumCode)) {
            uniqueCodes.add(museumCode);
        }
        if (StringUtils.isNotBlank(regionCode)) {
            List<MuseumPageVO> museumPageList = this.museumManageService.getByRegionCode(regionCode);
            if (CollectionUtils.isNotEmpty(museumPageList)) {
                museumPageList.forEach(k -> uniqueCodes.add(k.getMuseumId()));
            }
        }

        if(StringUtils.isNotBlank(museumCode) || StringUtils.isNotBlank(regionCode)){
            if(CollectionUtils.isEmpty(uniqueCodes)){
                return Lists.newArrayList();
            }
        }

        Date timeStart;
        Date timeEnd = new Date();
        //根据类型获取不同维度的统计数据
        if(statisticsWay == 0){
            //今日
            timeStart = DateUtils.getTimesDayMorning();
            timeEnd = DateUtils.getTimesDayNight();
        }else if(statisticsWay == 3){
            //本年
            timeStart = DateUtils.getTimesYearMorning();
        }else if(statisticsWay == 6){
            //昨天
            timeStart = DateUtils.getTimesDayMorning();
            timeEnd = DateUtils.getTimesDayNight();
            Calendar calYoy = Calendar.getInstance();
            calYoy.setTime(timeStart);
            calYoy.add(Calendar.DAY_OF_YEAR, -1);
            timeStart = calYoy.getTime();
            calYoy = Calendar.getInstance();
            calYoy.setTime(timeEnd);
            calYoy.add(Calendar.DAY_OF_YEAR, -1);
            timeEnd = calYoy.getTime();
        }else{
            if(StringUtils.isBlank(startTime)){
                throw new CommonException(CommonErrorCodeEnum.VALIDATE_ERROR, "开始时间不能为空！");
            }
            if(StringUtils.isBlank(endTime)){
                throw new CommonException(CommonErrorCodeEnum.VALIDATE_ERROR, "结束时间不能为空！");
            }
            //自定义时间
            timeStart = DateUtils.parseDateYYYMMDDHHmmss(startTime);
            timeEnd = DateUtils.parseDateYYYMMDDHHmmss(endTime);
        }

        List<ListGroupByProvinceDO> listGroupByProvinceDO = this.syncOrderBaseMapper.listGroupByProvince((byte) 2,timeStart,timeEnd, uniqueCodes, (byte) 1);
        if(CollectionUtils.isEmpty(listGroupByProvinceDO)){
            return Lists.newArrayList();
        }

        return listGroupByProvinceDO.stream().map(groupByProvinceDO -> {
            StatisticsByProvinceDTO dto = new StatisticsByProvinceDTO();
            dto.setProvinceCode(groupByProvinceDO.getProvinceCode());
            try {
                dto.setProvinceName(ProvinceCodeEnum.fromCode(groupByProvinceDO.getProvinceCode()).getDesc());
            }catch (Exception e){
                dto.setProvinceName("未知省份");
            }
            dto.setVisitorNum(groupByProvinceDO.getVisitorNum());
            return dto;
        }).collect(Collectors.toList());
    }

    @Override
    public VisitorStatisticsDTO visitorList(Integer statisticsWay, String startTime, String endTime, String museumCode, String regionCode) {
        VisitorStatisticsDTO visitorStatistics = new VisitorStatisticsDTO();
        List<String> uniqueCodes = Lists.newArrayList();
        if (StringUtils.isNotBlank(museumCode)) {
            uniqueCodes.add(museumCode);
        }
        if (StringUtils.isNotBlank(regionCode)) {
            List<MuseumPageVO> museumPageList = this.museumManageService.getByRegionCode(regionCode);
            if (CollectionUtils.isNotEmpty(museumPageList)) {
                museumPageList.forEach(k -> uniqueCodes.add(k.getMuseumId()));
            }
        }
        if(StringUtils.isNotBlank(museumCode) || StringUtils.isNotBlank(regionCode)){
            if(CollectionUtils.isEmpty(uniqueCodes)){
                return visitorStatistics;
            }
        }

        int peopleNum = 0;
        //根据类型获取不同维度的统计数据
        if(statisticsWay == 0){
            //今日
            List<StatisticVisitorDayDO> dayDOList  = statisticVisitorDayMapper.listByMuseumIds(DateUtils.formatDateYYYMMDD(),uniqueCodes);
            if(CollectionUtils.isNotEmpty(dayDOList)){
                peopleNum = dayDOList.stream().mapToInt(StatisticVisitorDayDO::getOfflineNum).sum();
            }
        }else if(statisticsWay == 3){
            //本年
            List<StatisticVisitorYearDO> yearDOList = statisticVisitorYearMapper.selectListMuseumIds(uniqueCodes);
            if(CollectionUtils.isNotEmpty(yearDOList)){
                peopleNum = yearDOList.stream().mapToInt(StatisticVisitorYearDO::getOfflineNum).sum();
            }
        }else if(statisticsWay == 6){
            //昨天
            List<StatisticVisitorDayDO> dayDOList  = statisticVisitorDayMapper.listByMuseumIds(DateUtils.formatDateYYYMMDD(DateUtils.getYesterdayDate()),uniqueCodes);
            if(CollectionUtils.isNotEmpty(dayDOList)){
                peopleNum = dayDOList.stream().mapToInt(StatisticVisitorDayDO::getOfflineNum).sum();
            }
        }else{
//            if(StringUtils.isBlank(startTime)){
//                throw new CommonException(CommonErrorCodeEnum.VALIDATE_ERROR, "开始时间不能为空！");
//            }
//            if(StringUtils.isBlank(endTime)){
//                throw new CommonException(CommonErrorCodeEnum.VALIDATE_ERROR, "结束时间不能为空！");
//            }
//            //自定义时间
//            Date timeStart = DateUtils.parseDateYYYMMDDHHmmss(startTime);
//            Date timeEnd = DateUtils.parseDateYYYMMDDHHmmss(endTime);
//
//            List<StatisticVisitorDayDO> dayDOList  = statisticVisitorDayMapper.
//                    listByTimes(DateUtils.formatDateYYYMMDD(timeStart),DateUtils.formatDateYYYMMDD(timeEnd),uniqueCodes);
//            if(CollectionUtils.isNotEmpty(dayDOList)){
//                peopleNum = dayDOList.stream().mapToInt(StatisticVisitorDayDO::getOfflineNum).sum();
//            }
        }



//        int peopleNum = this.syncOrderBaseMapper.countByReserveDate((byte) 2,
//                timeStart, timeEnd, uniqueCodes, null);
//        AtomicLong allTotalNum = new AtomicLong();
//        AtomicLong allInNum = new AtomicLong();
//        AtomicLong personalInNum = new AtomicLong();
//        AtomicLong teamInNum = new AtomicLong();
//        AtomicLong teamManInNum = new AtomicLong();
//        AtomicLong allOnlineInNum = new AtomicLong();
//
//        if (CollectionUtils.isEmpty(orderList)) {
//            visitorStatistics.setAllTotalNum(0L);
//            visitorStatistics.setAllInNum(0L);
//            visitorStatistics.setPersonalInNum(0L);
//            visitorStatistics.setTeamInNum(0L);
//            visitorStatistics.setTeamManInNum(0L);
//            visitorStatistics.setAllOnlineInNum(0L);
//        } else {
//            orderList.forEach(order -> {
//                allTotalNum.addAndGet(order.getTotalPeopleCount());
//                allInNum.addAndGet(order.getTotalPeopleCount());
//                if ((byte)1 == order.getOrderType()) {
//                    personalInNum.addAndGet(order.getTotalPeopleCount());
//                } else {
//                    teamInNum.addAndGet(1);
//                    teamManInNum.addAndGet(order.getTotalPeopleCount());
//                }
//            });
//            visitorStatistics.setAllTotalNum(allTotalNum.get());
//            visitorStatistics.setAllInNum(allInNum.get());
//            visitorStatistics.setPersonalInNum(personalInNum.get());
//            visitorStatistics.setTeamInNum(teamInNum.get());
//            visitorStatistics.setTeamManInNum(teamManInNum.get());
//        }
//        List<SyncCulturalRelicsDO> syncCulturalRelicsDOList = this.syncCulturalRelicsMapper.listByCondition(timeStart, timeEnd, uniqueCodes);
//        if (CollectionUtils.isNotEmpty(syncCulturalRelicsDOList)) {
//            syncCulturalRelicsDOList.forEach(c -> allOnlineInNum.addAndGet(c.getHolographicAccessCount()));
//            visitorStatistics.setAllTotalNum(allTotalNum.get() + allOnlineInNum.get());
//        }

        int lastPeopleNum = 0;
        if(statisticsWay == 0){
            //昨日
            List<StatisticVisitorDayDO> dayDOList  = statisticVisitorDayMapper.listByMuseumIds(DateUtils.formatDateYYYMMDD(DateUtils.getYesterdayDate()),uniqueCodes);
            if(CollectionUtils.isNotEmpty(dayDOList)){
                lastPeopleNum = dayDOList.stream().mapToInt(StatisticVisitorDayDO::getOfflineNum).sum();
            }
        }else if(statisticsWay == 3){
            //去年
            Calendar temp = Calendar.getInstance();
            temp.setTime(new Date());
            temp.add(Calendar.YEAR, -1);
            Calendar ringRatioYoy = Calendar.getInstance();
            ringRatioYoy.clear();
            ringRatioYoy.set(Calendar.YEAR, temp.get(Calendar.YEAR));
            ringRatioYoy.roll(Calendar.DAY_OF_YEAR, -1);
            Date timeEnd = ringRatioYoy.getTime();

            ringRatioYoy = Calendar.getInstance();
            ringRatioYoy.clear();
            ringRatioYoy.set(Calendar.YEAR, temp.get(Calendar.YEAR));
            Date timeStart = ringRatioYoy.getTime();
            lastPeopleNum = this.syncOrderBaseMapper.countByReserveDate((byte) 2,
                    timeStart, timeEnd, uniqueCodes, null);
        }else if(statisticsWay == 6){
            //前日
            List<StatisticVisitorDayDO> dayDOList  = statisticVisitorDayMapper.listByMuseumIds(DateUtils.formatDateYYYMMDD(DateUtils.getBeforeYesterdayDate()),uniqueCodes);
            if(CollectionUtils.isNotEmpty(dayDOList)){
                lastPeopleNum = dayDOList.stream().mapToInt(StatisticVisitorDayDO::getOfflineNum).sum();
            }
        }else{

        }

        visitorStatistics.setAllTotalNum((long)peopleNum);
        visitorStatistics.setAllInNum((long)peopleNum);
        visitorStatistics.setAddMan((long)peopleNum - lastPeopleNum);
        return visitorStatistics;
    }

    @Override
    public List<VisitorCityRankDTO> visitorCityRank(Integer statisticsWay, String startTime, String endTime){
        List<VisitorCityRankDTO> dtoList = Lists.newArrayList();
        //根据类型获取不同维度的统计数据
        if(statisticsWay == 0){
            //今日
            String today  = DateUtils.formatDateYYYMMDD(DateUtils.getTimesDayMorning());
            List<GroupVisitorByCityDO> groupVisitorByCityDOList = this.statisticVisitorDayMapper.groupVisitorByCity(today,today);
            if(CollectionUtils.isNotEmpty(groupVisitorByCityDOList)){
                for(int i=0;i<groupVisitorByCityDOList.size();i++){
                    VisitorCityRankDTO visitorCityRankDTO = new VisitorCityRankDTO();
                    GroupVisitorByCityDO groupVisitorByCityDO = groupVisitorByCityDOList.get(i);
                    visitorCityRankDTO.setRegionCode(groupVisitorByCityDO.getRegionCode());
                    visitorCityRankDTO.setCityName(groupVisitorByCityDO.getCityName());
                    visitorCityRankDTO.setVisitorNum(groupVisitorByCityDO.getOfflineNum());
                    visitorCityRankDTO.setId(i+1);
                    dtoList.add(visitorCityRankDTO);
                }
            }
        }else if(statisticsWay == 3){
            //本年
            List<GroupVisitorYearByCityDO> groupVisitorYearByCityDOList = this.statisticVisitorYearMapper.groupVisitorYearByCity();
            if(CollectionUtils.isNotEmpty(groupVisitorYearByCityDOList)){
                for(int i=0;i<groupVisitorYearByCityDOList.size();i++){
                    VisitorCityRankDTO visitorCityRankDTO = new VisitorCityRankDTO();
                    GroupVisitorYearByCityDO cityDO = groupVisitorYearByCityDOList.get(i);
                    visitorCityRankDTO.setRegionCode(cityDO.getRegionCode());
                    visitorCityRankDTO.setCityName(cityDO.getCityName());
                    visitorCityRankDTO.setVisitorNum(cityDO.getOfflineNum().intValue());
                    visitorCityRankDTO.setId(i+1);
                    dtoList.add(visitorCityRankDTO);
                }
            }
        }else if(statisticsWay == 6){
            //昨天
            String lastDay = DateUtils.getOrderDay(new Date(),-1,0);
            List<GroupVisitorByCityDO> groupVisitorByCityDOList = this.statisticVisitorDayMapper.groupVisitorByCity(lastDay,lastDay);
            if(CollectionUtils.isNotEmpty(groupVisitorByCityDOList)){
                for(int i=0;i<groupVisitorByCityDOList.size();i++){
                    GroupVisitorByCityDO groupVisitorByCityDO = groupVisitorByCityDOList.get(i);
                    VisitorCityRankDTO visitorCityRankDTO = new VisitorCityRankDTO();
                    visitorCityRankDTO.setRegionCode(groupVisitorByCityDO.getRegionCode());
                    visitorCityRankDTO.setCityName(groupVisitorByCityDO.getCityName());
                    visitorCityRankDTO.setVisitorNum(groupVisitorByCityDO.getOfflineNum());
                    visitorCityRankDTO.setId(i+1);
                    dtoList.add(visitorCityRankDTO);
                }
            }
        }else{
            if(StringUtils.isBlank(startTime)){
                throw new CommonException(CommonErrorCodeEnum.VALIDATE_ERROR, "开始时间不能为空！");
            }
            if(StringUtils.isBlank(endTime)){
                throw new CommonException(CommonErrorCodeEnum.VALIDATE_ERROR, "结束时间不能为空！");
            }
            //自定义时间
            startTime = DateUtils.formatDateYYYMMDD(DateUtils.parseDateYYYMMDDHHmmss(startTime));
            endTime = DateUtils.formatDateYYYMMDD(DateUtils.parseDateYYYMMDDHHmmss(endTime));
            List<GroupVisitorByCityDO> groupVisitorByCityDOList = this.statisticVisitorDayMapper.groupVisitorByCity(startTime,endTime);
            if(CollectionUtils.isNotEmpty(groupVisitorByCityDOList)){
                for(int i=0;i<groupVisitorByCityDOList.size();i++){
                    GroupVisitorByCityDO groupVisitorByCityDO = groupVisitorByCityDOList.get(i);
                    VisitorCityRankDTO visitorCityRankDTO = new VisitorCityRankDTO();
                    visitorCityRankDTO.setRegionCode(groupVisitorByCityDO.getRegionCode());
                    visitorCityRankDTO.setCityName(groupVisitorByCityDO.getCityName());
                    visitorCityRankDTO.setVisitorNum(groupVisitorByCityDO.getOfflineNum());
                    visitorCityRankDTO.setId(i+1);
                    dtoList.add(visitorCityRankDTO);
                }
            }
        }
        return dtoList;
    }

    @Override
    public List<VisitorMuseumRankDTO> visitorMuseumRank(Integer statisticsWay, String startTime, String endTime, String regionCode, String keyword, String museumId, Byte orderBy){
        List<VisitorMuseumRankDTO> dtoList;
        // 查询所有入驻博物馆
        List<MuseumBaseInfoDO> museumBaseInfoDOList =  museumBaseInfoMapper.selectListByParam((byte)1,regionCode,museumId);

        List<String> uniqueCodes = Lists.newArrayList();
        if (StringUtils.isNotBlank(regionCode)) {
            List<MuseumPageVO> museumPageList = this.museumManageService.getByRegionCode(regionCode);
            if (CollectionUtils.isNotEmpty(museumPageList)) {
                museumPageList.forEach(k -> uniqueCodes.add(k.getMuseumId()));
            }
        }else{
            museumBaseInfoDOList.forEach(k -> uniqueCodes.add(k.getId()));
        }

        Map<String,Integer> visitorMap = Maps.newHashMap();
        //根据类型获取不同维度的统计数据
        if(statisticsWay == 0){
            //今日
            String today  = DateUtils.formatDateYYYMMDD(DateUtils.getTimesDayMorning());
            List<GroupVisitorByMuseumDO> groupVisitorByMuseumDOList = this.statisticVisitorDayMapper.groupVisitorByMuseum(uniqueCodes,today,today);
            groupVisitorByMuseumDOList.forEach(s -> visitorMap.put(s.getMuseumId(),s.getOfflineNum()));
//            if(CollectionUtils.isNotEmpty(groupVisitorByMuseumDOList)){
//                for(int i=0;i<groupVisitorByMuseumDOList.size();i++){
//                    VisitorMuseumRankDTO visitorMuseumRankDTO = new VisitorMuseumRankDTO();
//                    GroupVisitorByMuseumDO museumDO = groupVisitorByMuseumDOList.get(i);
//                    visitorMuseumRankDTO.setMuseumCode(museumDO.getMuseumId());
//                    visitorMuseumRankDTO.setMuseumName(museumDO.getMuseumName());
//                    visitorMuseumRankDTO.setVisitorNum(museumDO.getOfflineNum());
//                    dtoList.add(visitorMuseumRankDTO);
//                }
//            }
        }else if(statisticsWay == 3){
            //本年
            List<StatisticVisitorYearDO> groupVisitorYearByMuseumDOList = this.statisticVisitorYearMapper.selectListMuseumIds(uniqueCodes);
            groupVisitorYearByMuseumDOList.forEach(s -> visitorMap.put(s.getMuseumId(),s.getOfflineNum()));
//            if(CollectionUtils.isNotEmpty(groupVisitorYearByMuseumDOList)){
//                for(int i=0;i<groupVisitorYearByMuseumDOList.size();i++){
//                    VisitorMuseumRankDTO visitorMuseumRankDTO = new VisitorMuseumRankDTO();
//                    StatisticVisitorYearDO museumDO = groupVisitorYearByMuseumDOList.get(i);
//                    visitorMuseumRankDTO.setMuseumCode(museumDO.getMuseumId());
//                    visitorMuseumRankDTO.setMuseumName(museumDO.getMuseumName());
//                    visitorMuseumRankDTO.setVisitorNum(museumDO.getOfflineNum());
//                    dtoList.add(visitorMuseumRankDTO);
//                }
//            }
        }else if(statisticsWay == 6){
            //昨天
            String lastDay = DateUtils.getOrderDay(new Date(),-1,0);
            List<GroupVisitorByMuseumDO> groupVisitorByMuseumDOList = this.statisticVisitorDayMapper.groupVisitorByMuseum(uniqueCodes,lastDay,lastDay);
            groupVisitorByMuseumDOList.forEach(s -> visitorMap.put(s.getMuseumId(),s.getOfflineNum()));
//            if(CollectionUtils.isNotEmpty(groupVisitorByMuseumDOList)){
//                for(int i=0;i<groupVisitorByMuseumDOList.size();i++){
//                    VisitorMuseumRankDTO visitorMuseumRankDTO = new VisitorMuseumRankDTO();
//                    GroupVisitorByMuseumDO museumDO = groupVisitorByMuseumDOList.get(i);
//                    visitorMuseumRankDTO.setMuseumCode(museumDO.getMuseumId());
//                    visitorMuseumRankDTO.setMuseumName(museumDO.getMuseumName());
//                    visitorMuseumRankDTO.setVisitorNum(museumDO.getOfflineNum());
//                    dtoList.add(visitorMuseumRankDTO);
//                }
//            }
        }else{
            if(StringUtils.isBlank(startTime)){
                throw new CommonException(CommonErrorCodeEnum.VALIDATE_ERROR, "开始时间不能为空！");
            }
            if(StringUtils.isBlank(endTime)){
                throw new CommonException(CommonErrorCodeEnum.VALIDATE_ERROR, "结束时间不能为空！");
            }
            //自定义时间
            startTime = DateUtils.formatDateYYYMMDD(DateUtils.parseDateYYYMMDDHHmmss(startTime));
            endTime = DateUtils.formatDateYYYMMDD(DateUtils.parseDateYYYMMDDHHmmss(endTime));
            List<GroupVisitorByMuseumDO> groupVisitorByMuseumDOList = this.statisticVisitorDayMapper.groupVisitorByMuseum(uniqueCodes,startTime,endTime);
            groupVisitorByMuseumDOList.forEach(s -> visitorMap.put(s.getMuseumId(),s.getOfflineNum()));
//            if(CollectionUtils.isNotEmpty(groupVisitorByMuseumDOList)){
//                for(int i=0;i<groupVisitorByMuseumDOList.size();i++){
//                    VisitorMuseumRankDTO visitorMuseumRankDTO = new VisitorMuseumRankDTO();
//                    GroupVisitorByMuseumDO museumDO = groupVisitorByMuseumDOList.get(i);
//                    visitorMuseumRankDTO.setMuseumCode(museumDO.getMuseumId());
//                    visitorMuseumRankDTO.setMuseumName(museumDO.getMuseumName());
//                    visitorMuseumRankDTO.setVisitorNum(museumDO.getOfflineNum());
//                    dtoList.add(visitorMuseumRankDTO);
//                }
//            }
        }

        dtoList = museumBaseInfoDOList.stream().map(s -> {
            VisitorMuseumRankDTO dto = new VisitorMuseumRankDTO();
            dto.setMuseumCode(s.getId());
            dto.setMuseumName(s.getMuseumName());
            dto.setVisitorNum(visitorMap.getOrDefault(s.getId(), 0));
            return dto;
        }).collect(Collectors.toList());


        if(StringUtils.isNotBlank(keyword)){
            dtoList = dtoList.stream().filter(s -> s.getMuseumName().contains(keyword)).collect(Collectors.toList());
        }

        if(orderBy == 0){
            dtoList = dtoList.stream().sorted(Comparator.comparing(VisitorMuseumRankDTO::getVisitorNum).reversed()).collect(Collectors.toList());
        }else{
            dtoList = dtoList.stream().sorted(Comparator.comparing(VisitorMuseumRankDTO::getVisitorNum)).collect(Collectors.toList());
        }

        // 增加序号
        if(CollectionUtils.isNotEmpty(dtoList)){
            for(int i=0;i<dtoList.size();i++){
                dtoList.get(i).setId(i+1);
            }
        }
        return dtoList;
    }

    @Override
    public List<TimePeriodStatisticsDTO> timePeriodStatistics(Integer statisticsWay, String startTime, String endTime,
                                                        String museumCode, String regionCode) {
        List<String> uniqueCodes = Lists.newArrayList();
        if (StringUtils.isNotBlank(museumCode)) {
            uniqueCodes.add(museumCode);
        }
        if (StringUtils.isNotBlank(regionCode)) {
            List<MuseumPageVO> museumPageList = this.museumManageService.getByRegionCode(regionCode);
            if (CollectionUtils.isNotEmpty(museumPageList)) {
                museumPageList.forEach(k -> uniqueCodes.add(k.getMuseumId()));
            }
        }

        if(StringUtils.isNotBlank(museumCode) || StringUtils.isNotBlank(regionCode)){
            if(CollectionUtils.isEmpty(uniqueCodes)){
                return Lists.newArrayList();
            }
        }

        Date timeStart;
        Date timeEnd = new Date();
        //根据类型获取不同维度的统计数据
        if(statisticsWay == 0){
            //今日
            timeStart = DateUtils.getTimesDayMorning();
            timeEnd = DateUtils.getTimesDayNight();
        }else if(statisticsWay == 3){
            //本年
            timeStart = DateUtils.getTimesYearMorning();
        }else if(statisticsWay == 6){
            //昨天
            timeStart = DateUtils.getTimesDayMorning();
            timeEnd = DateUtils.getTimesDayNight();
            Calendar calYoy = Calendar.getInstance();
            calYoy.setTime(timeStart);
            calYoy.add(Calendar.DAY_OF_YEAR, -1);
            timeStart = calYoy.getTime();
            calYoy = Calendar.getInstance();
            calYoy.setTime(timeEnd);
            calYoy.add(Calendar.DAY_OF_YEAR, -1);
            timeEnd = calYoy.getTime();
        }else{
            if(StringUtils.isBlank(startTime)){
                throw new CommonException(CommonErrorCodeEnum.VALIDATE_ERROR, "开始时间不能为空！");
            }
            if(StringUtils.isBlank(endTime)){
                throw new CommonException(CommonErrorCodeEnum.VALIDATE_ERROR, "结束时间不能为空！");
            }
            //自定义时间
            timeStart = DateUtils.parseDateYYYMMDDHHmmss(startTime);
            timeEnd = DateUtils.parseDateYYYMMDDHHmmss(endTime);
        }
        List<SyncOrderBaseDO> orderList = this.syncOrderBaseMapper.listByReserveDate((byte) 2,
                timeStart, timeEnd, uniqueCodes, null);
        TimePeriodStatisticsDTO t1 = new TimePeriodStatisticsDTO("8:00-10:00", 0L, 0);
        TimePeriodStatisticsDTO t2 = new TimePeriodStatisticsDTO("10:00-12:00", 0L, 1);
        TimePeriodStatisticsDTO t3= new TimePeriodStatisticsDTO("12:00-14:00", 0L, 2);
        TimePeriodStatisticsDTO t4 = new TimePeriodStatisticsDTO("14:00-16:00", 0L, 3);
        TimePeriodStatisticsDTO t5 = new TimePeriodStatisticsDTO("16:00-18:00", 0L, 4);
        List<TimePeriodStatisticsDTO> result = Lists.newArrayList(t1, t2, t3, t4, t5);
        if (CollectionUtils.isNotEmpty(orderList)) {
            orderList.forEach(order -> {
                if (Objects.isNull(order.getWriteOffDate())) {
                    return;
                }
                String[] key = t1.getKey().split("-");
                Date start = DateUtils.parseDateHHmm(key[0]);
                Date end = DateUtils.parseDateHHmm(key[1]);
                Date currDate = DateUtils.parseDateHHmm(DateUtils.formatDateHHmm(order.getWriteOffDate()));
                if (currDate.after(start) && currDate.before(end)) {
                    t1.setInNum(t1.getInNum() + 1);
                    return;
                }
                key = t2.getKey().split("-");
                start = DateUtils.parseDateHHmm(key[0]);
                end = DateUtils.parseDateHHmm(key[1]);
                if (currDate.after(start) && currDate.before(end)) {
                    t2.setInNum(t2.getInNum() + 1);
                    return;
                }
                key = t3.getKey().split("-");
                start = DateUtils.parseDateHHmm(key[0]);
                end = DateUtils.parseDateHHmm(key[1]);
                if (currDate.after(start) && currDate.before(end)) {
                    t3.setInNum(t3.getInNum() + 1);
                    return;
                }
                key = t4.getKey().split("-");
                start = DateUtils.parseDateHHmm(key[0]);
                end = DateUtils.parseDateHHmm(key[1]);
                if (currDate.after(start) && currDate.before(end)) {
                    t4.setInNum(t4.getInNum() + 1);
                    return;
                }
                key = t5.getKey().split("-");
                start = DateUtils.parseDateHHmm(key[0]);
                end = DateUtils.parseDateHHmm(key[1]);
                if (currDate.after(start) && currDate.before(end)) {
                    t5.setInNum(t5.getInNum() + 1);
                }
            });
        }
        return result;
    }

    @Override
    public List<VisitorTendencyDTO> visitorTendency(Integer statisticsWay, String startTime, String endTime, String regionCode, String museumCode){

        List<String> uniqueCodes = Lists.newArrayList();
        if (StringUtils.isNotBlank(museumCode)) {
            uniqueCodes.add(museumCode);
        }else if(StringUtils.isNotBlank(regionCode)) {
            List<MuseumPageVO> museumPageList = this.museumManageService.getByRegionCode(regionCode);
            if (CollectionUtils.isNotEmpty(museumPageList)) {
                museumPageList.forEach(k -> uniqueCodes.add(k.getMuseumId()));
            }
        }else{
            // 查询所有入驻博物馆
            List<MuseumBaseInfoDO> museumBaseInfoDOList =  museumBaseInfoMapper.selectListByParam((byte)1,null,null);
            museumBaseInfoDOList.forEach(k -> uniqueCodes.add(k.getId()));
        }

        if(CollectionUtils.isEmpty(uniqueCodes)){
            return Lists.newArrayList();
        }

        List<GroupVisitorByDateTypeDO> doList = Lists.newArrayList();
        // 所有的时间刻度
        List<String> dateStrList = Lists.newArrayList();

        Date currentDate = new Date();
        String dateStart;
        String dateEnd;
        if(statisticsWay == 1){
            // 近一周
            dateStart = DateUtils.getOrderDay(currentDate,-6,0);
            dateEnd = DateUtils.formatDateYYYMMDD(currentDate);
            doList = statisticVisitorDayMapper.groupVisitorByDateType(10,dateStart,null,uniqueCodes);
            dateStrList = DateUtils.getDateList(dateStart,dateEnd,"yyyy-MM-dd",2);
        }else if(statisticsWay == 2){
            // 近一月
            dateStart = DateUtils.getOrderDay(currentDate,-30,0);
            dateEnd = DateUtils.formatDateYYYMMDD(currentDate);
            doList = statisticVisitorDayMapper.groupVisitorByDateType(10,dateStart,null,uniqueCodes);
            dateStrList = DateUtils.getDateList(dateStart,dateEnd,"yyyy-MM-dd",2);
        }else if(statisticsWay == 3){
            // 本年
            Date thisYearStart = DateUtils.getTimesYearMorning();
            dateStart = DateUtils.formatDateYYYMMDD(thisYearStart);
            dateEnd = DateUtils.formatDateYYYMMDD(currentDate);
            int days = DateUtils.calculateDateDif(currentDate,thisYearStart);
            if(days <= 31){
                // 按天统计
                doList = statisticVisitorDayMapper.groupVisitorByDateType(10,dateStart,null,uniqueCodes);
                dateStrList = DateUtils.getDateList(dateStart,dateEnd,"yyyy-MM-dd",2);
            }else{
                // 按月统计
                doList = statisticVisitorDayMapper.groupVisitorByDateType(7,dateStart,null,uniqueCodes);
                dateStrList = DateUtils.getDateList(dateStart,dateEnd,"yyyy-MM",1);
            }
        }else if(statisticsWay == 4){
            // 自定义
            if(StringUtils.isBlank(startTime)){
                throw new CommonException(CommonErrorCodeEnum.VALIDATE_ERROR, "开始时间不能为空！");
            }
            if(StringUtils.isBlank(endTime)){
                throw new CommonException(CommonErrorCodeEnum.VALIDATE_ERROR, "结束时间不能为空！");
            }

            dateStart = startTime;
            dateEnd = endTime;
            Date startDate = DateUtils.parseDateYYYMMDD(startTime);
            Date endDate = DateUtils.parseDateYYYMMDD(endTime);

            int days = DateUtils.calculateDateDif(endDate,startDate);
            if(days <= 31){
                // 按天统计
                doList = statisticVisitorDayMapper.groupVisitorByDateType(10,dateStart,dateEnd,uniqueCodes);
                dateStrList = DateUtils.getDateList(dateStart,dateEnd,"yyyy-MM-dd",2);
            }else if(days <= 366){
                // 按月统计
                doList = statisticVisitorDayMapper.groupVisitorByDateType(7,dateStart,dateEnd,uniqueCodes);
                dateStrList = DateUtils.getDateList(dateStart,dateEnd,"yyyy-MM",1);
            }else{
                // 按年统计
                doList = statisticVisitorDayMapper.groupVisitorByDateType(4,dateStart,dateEnd,uniqueCodes);
                dateStrList = DateUtils.getDateList(dateStart,dateEnd,"yyyy-MM-dd",4);
            }
        }

        Map<String,Integer> visitorMap = new HashMap<>();
        doList.forEach(s -> visitorMap.put(s.getDateStr(),s.getVisitorNum()));

        return dateStrList.stream().map(str ->{
            VisitorTendencyDTO dto = new VisitorTendencyDTO();
            dto.setDateStr(str);
            dto.setVisitorNum(visitorMap.getOrDefault(str, 0));
            return dto;
        }).collect(Collectors.toList());
    }

    @Override
    public SimplePageInfo<VisitorVenueDistributeDTO> visitorVenueDistribute(Integer statisticsWay, String startTime, String endTime, String regionCode,
                                                                            String museumCode, Integer sortField, Integer orderBy,Integer pageNum, Integer pageSize){
        List<String> uniqueCodes = Lists.newArrayList();
        if (StringUtils.isNotBlank(museumCode)) {
            uniqueCodes.add(museumCode);
        }else if(StringUtils.isNotBlank(regionCode)) {
            List<MuseumPageVO> museumPageList = this.museumManageService.getByRegionCode(regionCode);
            if (CollectionUtils.isNotEmpty(museumPageList)) {
                museumPageList.forEach(k -> uniqueCodes.add(k.getMuseumId()));
            }
        }else{
            // 查询所有入驻博物馆
            List<MuseumBaseInfoDO> museumBaseInfoDOList =  museumBaseInfoMapper.selectListByParam((byte)1,null,null);
            museumBaseInfoDOList.forEach(k -> uniqueCodes.add(k.getId()));
        }

        if(CollectionUtils.isEmpty(uniqueCodes)){
            return new SimplePageInfo<>();
        }

        Date currentDate = new Date();
        String dateStart;
        String dateEnd;
        if(statisticsWay == 0){
            // 今日
            dateStart = DateUtils.formatDateYYYMMDD(currentDate);
            dateEnd = dateStart;
        }else if(statisticsWay == 6){
            // 昨日
            dateStart = DateUtils.getOrderDay(currentDate,-1,0);
            dateEnd = dateStart;
        }else if(statisticsWay == 3){
            // 本年
            dateStart = DateUtils.formatDateYYYMMDD(DateUtils.getTimesYearMorning());
            dateEnd = DateUtils.formatDateYYYMMDD(currentDate);
        }else if(statisticsWay == 5){
            // 自定义
            if(StringUtils.isBlank(startTime)){
                throw new CommonException(CommonErrorCodeEnum.VALIDATE_ERROR, "开始时间不能为空！");
            }
            if(StringUtils.isBlank(endTime)){
                throw new CommonException(CommonErrorCodeEnum.VALIDATE_ERROR, "结束时间不能为空！");
            }
            dateStart = startTime;
            dateEnd = endTime;
        }else{
            throw new CommonException(CommonErrorCodeEnum.VALIDATE_ERROR, "statisticsWay:"+ statisticsWay +"不存在！");
        }

        List<MuseumBaseInfoDO> museumList = museumBaseInfoMapper.selectListByIds(uniqueCodes);

        // 按博物馆统计
        List<GroupVisitorByMuseumDO> groupByMuseum = statisticVisitorDayMapper.groupVisitorByMuseum(uniqueCodes,dateStart,dateEnd);
        Map<String,GroupVisitorByMuseumDO> groupByMuseumMap = new HashMap<>();
        groupByMuseum.forEach(s -> groupByMuseumMap.put(s.getMuseumId(),s));

        //统计各市接待观众数量
        List<GroupVisitorByCityDO> groupByCity = statisticVisitorDayMapper.groupVisitorByCity(dateStart,dateEnd);
        Map<String,Integer> groupByCityMap = new HashMap<>();
        groupByCity.forEach(s -> {
            groupByCityMap.put(s.getRegionCode(),s.getOfflineNum());
        });

        //全省接待观众数量
        Integer provinceNum = groupByCity.stream().mapToInt(GroupVisitorByCityDO::getOfflineNum).sum();

        List<VisitorVenueDistributeDTO> dtoList = museumList.stream().map(museumBaseInfoDO ->{
            VisitorVenueDistributeDTO dto = new VisitorVenueDistributeDTO();
            BigDecimal defaultValue = new BigDecimal(0.0);
            dto.setMuseumCode(museumBaseInfoDO.getId());
            dto.setMuseumName(museumBaseInfoDO.getMuseumName());
            dto.setCityName(museumBaseInfoDO.getCityName());
            dto.setVisitorNum(0);
            dto.setNoAdultNumRatio(defaultValue);
            dto.setLocalNumRatio(defaultValue);
            dto.setOffsiteNumRatio(defaultValue);
            dto.setAbroadNumRatio(defaultValue);
            dto.setManNumRatio(defaultValue);
            dto.setWomanNumRatio(defaultValue);
            String cityCode = museumBaseInfoDO.getRegionCode().substring(0,4);
            if(groupByMuseumMap.containsKey(museumBaseInfoDO.getId())){
                GroupVisitorByMuseumDO visitorMuseumDO = groupByMuseumMap.get(museumBaseInfoDO.getId());
                if(visitorMuseumDO.getOfflineNum() != 0){
                    dto.setVisitorNum(visitorMuseumDO.getOfflineNum());
                    BigDecimal offlineNum = new BigDecimal(visitorMuseumDO.getOfflineNum());
                    BigDecimal personalNum = new BigDecimal(visitorMuseumDO.getPersonalNum());
                    BigDecimal childrenNum = new BigDecimal(visitorMuseumDO.getChildrenNum());
                    BigDecimal localNum = new BigDecimal(visitorMuseumDO.getProvinceNum());
                    BigDecimal offsiteNum = new BigDecimal(visitorMuseumDO.getOutsideNum());
                    BigDecimal abroadNum = new BigDecimal(visitorMuseumDO.getAbroadNum());
                    BigDecimal manNum = new BigDecimal(visitorMuseumDO.getManNum());
                    BigDecimal womanNum = new BigDecimal(visitorMuseumDO.getWomanNum());
                    if(groupByCityMap.containsKey(cityCode) && groupByCityMap.get(cityCode) != 0){
                        dto.setInCityRatio(offlineNum.divide(new BigDecimal(groupByCityMap.get(cityCode)),4, RoundingMode.HALF_UP).multiply(new BigDecimal(100)));
                    }
                    if(provinceNum != 0){
                        dto.setInProvinceRatio(offlineNum.divide(new BigDecimal(provinceNum),4, RoundingMode.HALF_UP).multiply(new BigDecimal(100)));
                    }
                    if(personalNum.compareTo(BigDecimal.ZERO)!=0){
                        dto.setNoAdultNumRatio(childrenNum.divide(personalNum,4,RoundingMode.HALF_UP).multiply(new BigDecimal(100)));
                        dto.setLocalNumRatio(localNum.divide(personalNum,4,RoundingMode.HALF_UP).multiply(new BigDecimal(100)));
                        dto.setOffsiteNumRatio(offsiteNum.divide(personalNum,4,RoundingMode.HALF_UP).multiply(new BigDecimal(100)));
                        dto.setAbroadNumRatio(abroadNum.divide(personalNum,4,RoundingMode.HALF_UP).multiply(new BigDecimal(100)));
                        dto.setManNumRatio(manNum.divide(personalNum,4,RoundingMode.HALF_UP).multiply(new BigDecimal(100)));
                        dto.setWomanNumRatio(womanNum.divide(personalNum,4,RoundingMode.HALF_UP).multiply(new BigDecimal(100)));
                    }
                }
            }
            return dto;
        }).collect(Collectors.toList());

        if(sortField == 1){
            if(orderBy == 1){
                dtoList = dtoList.stream().sorted(Comparator.comparing(VisitorVenueDistributeDTO::getVisitorNum).reversed()).collect(Collectors.toList());
            }else{
                dtoList = dtoList.stream().sorted(Comparator.comparing(VisitorVenueDistributeDTO::getVisitorNum)).collect(Collectors.toList());
            }
        }else if(sortField == 2){
            if(orderBy == 1){
                dtoList = dtoList.stream().sorted(Comparator.comparing(VisitorVenueDistributeDTO::getInProvinceRatio).reversed()).collect(Collectors.toList());
            }else{
                dtoList = dtoList.stream().sorted(Comparator.comparing(VisitorVenueDistributeDTO::getInProvinceRatio)).collect(Collectors.toList());
            }
        }else if(sortField == 3){
            if(orderBy == 1){
                dtoList = dtoList.stream().sorted(Comparator.comparing(VisitorVenueDistributeDTO::getInProvinceRatio).reversed()).collect(Collectors.toList());
            }else{
                dtoList = dtoList.stream().sorted(Comparator.comparing(VisitorVenueDistributeDTO::getInProvinceRatio)).collect(Collectors.toList());
            }
        }else if(sortField == 4){
            if(orderBy == 1){
                dtoList = dtoList.stream().sorted(Comparator.comparing(VisitorVenueDistributeDTO::getNoAdultNumRatio).reversed()).collect(Collectors.toList());
            }else{
                dtoList = dtoList.stream().sorted(Comparator.comparing(VisitorVenueDistributeDTO::getNoAdultNumRatio)).collect(Collectors.toList());
            }
        }else if(sortField == 5){
            if(orderBy == 1){
                dtoList = dtoList.stream().sorted(Comparator.comparing(VisitorVenueDistributeDTO::getLocalNumRatio).reversed()).collect(Collectors.toList());
            }else{
                dtoList = dtoList.stream().sorted(Comparator.comparing(VisitorVenueDistributeDTO::getLocalNumRatio)).collect(Collectors.toList());
            }
        }else if(sortField == 6){
            if(orderBy == 1){
                dtoList = dtoList.stream().sorted(Comparator.comparing(VisitorVenueDistributeDTO::getOffsiteNumRatio).reversed()).collect(Collectors.toList());
            }else{
                dtoList = dtoList.stream().sorted(Comparator.comparing(VisitorVenueDistributeDTO::getOffsiteNumRatio)).collect(Collectors.toList());
            }
        }else if(sortField == 7){
            if(orderBy == 1){
                dtoList = dtoList.stream().sorted(Comparator.comparing(VisitorVenueDistributeDTO::getAbroadNumRatio).reversed()).collect(Collectors.toList());
            }else{
                dtoList = dtoList.stream().sorted(Comparator.comparing(VisitorVenueDistributeDTO::getAbroadNumRatio)).collect(Collectors.toList());
            }
        }else if(sortField == 8){
            if(orderBy == 1){
                dtoList = dtoList.stream().sorted(Comparator.comparing(VisitorVenueDistributeDTO::getManNumRatio).reversed()).collect(Collectors.toList());
            }else{
                dtoList = dtoList.stream().sorted(Comparator.comparing(VisitorVenueDistributeDTO::getManNumRatio)).collect(Collectors.toList());
            }
        }else if(sortField == 9){
            if(orderBy == 1){
                dtoList = dtoList.stream().sorted(Comparator.comparing(VisitorVenueDistributeDTO::getWomanNumRatio).reversed()).collect(Collectors.toList());
            }else{
                dtoList = dtoList.stream().sorted(Comparator.comparing(VisitorVenueDistributeDTO::getWomanNumRatio)).collect(Collectors.toList());
            }
        }

        return PaginationUtils.pagination(dtoList, pageNum, pageSize);
    }

    /**
     * 根据身份证的号码算出当前身份证持有者的年龄
     *
     * @return
     */
    private int countAge(String idNumber) {
        int age = -1;
        try {
            if(idNumber.length() != 18 && idNumber.length() != 15){
                return age;
            }
            String year;
            String yue;
            String day;
            if(idNumber.length() == 18){
                year = idNumber.substring(6).substring(0, 4);// 得到年份
                yue = idNumber.substring(10).substring(0, 2);// 得到月份
                day = idNumber.substring(12).substring(0,2);//得到日
            }else{
                year = "19" + idNumber.substring(6, 8);// 年份
                yue = idNumber.substring(8, 10);// 月份
                day = idNumber.substring(10, 12);//日
            }
            Date date = new Date();// 得到当前的系统时间
            SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
            String fyear = format.format(date).substring(0, 4);// 当前年份
            String fyue = format.format(date).substring(5, 7);// 月份
            String fday=format.format(date).substring(8,10);//

            if(Integer.parseInt(yue) == Integer.parseInt(fyue)){//如果月份相同
                //说明已经过了生日或者今天是生日
                if (Integer.parseInt(day) <= Integer.parseInt(fday)) {
                    age = Integer.parseInt(fyear) - Integer.parseInt(year);
                } else {
                    age = Integer.parseInt(fyear) - Integer.parseInt(year) - 1;
                }
            }else{

                if(Integer.parseInt(yue) < Integer.parseInt(fyue)){
                    //如果当前月份大于出生月份
                    age = Integer.parseInt(fyear) - Integer.parseInt(year);
                }else{
                    //如果当前月份小于出生月份,说明生日还没过
                    age = Integer.parseInt(fyear) - Integer.parseInt(year) - 1;
                }
            }
        }catch (Exception e){
            log.error("身份证号异常："+idNumber);
        }
        return age;
    }

    /**
     * 15位身份证号
     */
    private static final Integer FIFTEEN_ID_CARD=15;
    /**
     * 18位身份证号
     */
    private static final Integer EIGHTEEN_ID_CARD=18;

    /**
     * 根据身份证号获取性别
     * @param IDCard
     * @return
     */
    private static String getSex(String IDCard){
        String sex ="";
        try{
            if (StringUtils.isNotBlank(IDCard)){
                //15位身份证号
                if (IDCard.length() == FIFTEEN_ID_CARD){
                    if (Integer.parseInt(IDCard.substring(14, 15)) % 2 == 0) {
                        sex = "女";
                    } else {
                        sex = "男";
                    }
                    //18位身份证号
                }else if(IDCard.length() == EIGHTEEN_ID_CARD){
                    // 判断性别
                    if (Integer.parseInt(IDCard.substring(16).substring(0, 1)) % 2 == 0) {
                        sex = "女";
                    } else {
                        sex = "男";
                    }
                }
            }
        }catch (Exception e){
            log.error("身份证号异常："+IDCard);
        }
        return sex;
    }

    public static void main(String[] args) {
        Date timeStart = DateUtils.getTimesDayMorning();
        Date timeEnd = DateUtils.getTimesDayNight();
        Calendar calYoy = Calendar.getInstance();
        calYoy.setTime(timeStart);
        calYoy.add(Calendar.DAY_OF_YEAR, -1);
        timeStart = calYoy.getTime();
        System.out.println(DateUtils.formatDateYYYMMDDHHmmss(timeStart));
        calYoy = Calendar.getInstance();
        calYoy.setTime(timeEnd);
        calYoy.add(Calendar.DAY_OF_YEAR, -1);
        timeEnd = calYoy.getTime();
        System.out.println(DateUtils.formatDateYYYMMDDHHmmss(timeEnd));

        System.out.println(DateUtils.formatDateYYYMMDDHHmm(DateUtils.parseDateHHmm("8:00")));
        System.out.println(DateUtils.parseDateHHmm(DateUtils.formatDateHHmm(timeStart)));
        System.out.println(DateUtils.formatDateHHmm(timeEnd));
    }
}
