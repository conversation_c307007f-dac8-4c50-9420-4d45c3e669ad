package com.das.province.service.biz.user;

import com.das.province.common.bo.SimplePageInfo;
import com.das.province.common.exception.CommonErrorCodeEnum;
import com.das.province.common.exception.CommonException;
import com.das.province.common.utils.BeanCopyUtils;
import com.das.province.common.utils.SimplePageInfoUtils;
import com.das.province.infr.dataobject.AccPermissionDO;
import com.das.province.infr.dataobject.AccRoleDO;
import com.das.province.infr.dataobject.AccRolePermissionRelDO;
import com.das.province.infr.dataobject.AccUserRoleRelDO;
import com.das.province.infr.entity.RoleEntity;
import com.das.province.infr.mapper.AccPermissionMapper;
import com.das.province.infr.mapper.AccRoleMapper;
import com.das.province.infr.mapper.AccRolePermissionRelMapper;
import com.das.province.infr.mapper.AccUserRoleRelMapper;
import com.das.province.infr.repository.RoleRepository;
import com.das.province.service.biz.user.action.*;
import com.das.province.service.biz.user.dto.RoleDTO;
import com.das.province.service.biz.user.dto.RolePageDTO;
import com.das.province.service.biz.user.dto.RolePermissionDTO;
import com.das.province.service.enums.AccountStatusEnum;
import com.das.province.service.enums.DeleteStatusEnum;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@Service
public class RoleServiceImpl implements RoleService {

    @Resource
    private AccRoleMapper accRoleMapper;

    @Resource
    private AccUserRoleRelMapper accUserRoleRelMapper;

    @Resource
    private AccRolePermissionRelMapper accRolePermissionRelMapper;

    @Resource
    private AccPermissionMapper accPermissionMapper;

    @Resource
    private RoleRepository roleRepository;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long addRole(AddRoleAction action) {
        Long companyId = action.getCompanyId();
        String roleName = action.getRoleName();
        AccRoleDO roleDO = this.accRoleMapper.selectByRoleName(companyId, action.getRoleName(),
                DeleteStatusEnum.否.getCode().byteValue());
        if (Objects.nonNull(roleDO)) {
            throw new CommonException(CommonErrorCodeEnum.VALIDATE_ERROR, "该[" + roleName + "]角色已存在");
        }
        RoleEntity roleEntity = new RoleEntity();
        roleEntity.setCompanyId(companyId);
        roleEntity.setRoleName(roleName);
        roleEntity.setStatus(AccountStatusEnum.启用.getCode().byteValue());
        roleEntity.setIsDelete(DeleteStatusEnum.否.getCode().byteValue());
        roleEntity.setCreator(action.getCreator());
        roleEntity.setModifier(action.getModifier());
        this.roleRepository.saveRoleEntity(roleEntity);
        List<String> permissionCodeList = action.getPermissionCodeList();
        if (CollectionUtils.isEmpty(permissionCodeList)) {
            return roleEntity.getRoleId();
        }
        List<AccPermissionDO> permissionDOList = this.accPermissionMapper
                .listByPermissionCodes(companyId, permissionCodeList, DeleteStatusEnum.否.getCode().byteValue());
        if (CollectionUtils.isEmpty(permissionDOList) || action.getPermissionCodeList().size() != permissionDOList.size()) {
            throw new CommonException(CommonErrorCodeEnum.VALIDATE_ERROR, "权限信息不存在");
        }
        this.accRolePermissionRelMapper.deleteByRoleId(companyId, roleEntity.getRoleId());
        permissionCodeList.forEach(permissionCode -> {
            AccRolePermissionRelDO rolePermissionRelDO = new AccRolePermissionRelDO();
            rolePermissionRelDO.setCompanyId(companyId);
            rolePermissionRelDO.setRoleId(roleEntity.getRoleId());
            rolePermissionRelDO.setPermissionCode(permissionCode);
            rolePermissionRelDO.setCreator(action.getCreator());
            rolePermissionRelDO.setModifier(action.getCreator());
            this.accRolePermissionRelMapper.insertSelective(rolePermissionRelDO);
        });
        return roleEntity.getRoleId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void editRoleById(EditRoleByIdAction action) {
        Long companyId = action.getCompanyId();
        String roleName = action.getRoleName();
        Long roleId = action.getRoleId();
        AccRoleDO roleDO = this.accRoleMapper.selectByRoleName(companyId, action.getRoleName(),
                DeleteStatusEnum.否.getCode().byteValue());
        if (Objects.nonNull(roleDO) && !roleId.equals(roleDO.getId())) {
            throw new CommonException(CommonErrorCodeEnum.VALIDATE_ERROR, "该[" + roleName + "]角色已存在");
        }
        AccRoleDO accRoleDO = this.accRoleMapper.selectByRoleId(companyId, roleId,
                DeleteStatusEnum.否.getCode().byteValue());
        if (Objects.isNull(accRoleDO)) {
            throw new CommonException(CommonErrorCodeEnum.VALIDATE_ERROR, "该[" + roleName + "]角色不存在");
        }
        RoleEntity roleEntity = new RoleEntity();
        roleEntity.setRoleId(roleId);
        roleEntity.setRoleName(roleName);
        roleEntity.setModifier(action.getModifier());
        this.roleRepository.editRoleEntity(roleEntity);
        List<String> permissionCodeList = action.getPermissionCodeList();
        if (CollectionUtils.isEmpty(permissionCodeList)) {
            return;
        }
        List<AccPermissionDO> permissionDOList = this.accPermissionMapper
                .listByPermissionCodes(companyId, permissionCodeList, DeleteStatusEnum.否.getCode().byteValue());
        if (CollectionUtils.isEmpty(permissionDOList) || action.getPermissionCodeList().size() != permissionDOList.size()) {
            throw new CommonException(CommonErrorCodeEnum.VALIDATE_ERROR, "权限信息不存在");
        }
        this.accRolePermissionRelMapper.deleteByRoleId(companyId, roleEntity.getRoleId());
        permissionCodeList.forEach(permissionCode -> {
            AccRolePermissionRelDO rolePermissionRelDO = new AccRolePermissionRelDO();
            rolePermissionRelDO.setCompanyId(companyId);
            rolePermissionRelDO.setRoleId(roleEntity.getRoleId());
            rolePermissionRelDO.setPermissionCode(permissionCode);
            rolePermissionRelDO.setCreator(action.getModifier());
            rolePermissionRelDO.setModifier(action.getModifier());
            this.accRolePermissionRelMapper.insertSelective(rolePermissionRelDO);
        });
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delRoleById(DelRoleByIdAction action) {
        Long companyId = action.getCompanyId();
        Long roleId = action.getRoleId();
        List<AccUserRoleRelDO> userRoleRelDOList = this.accUserRoleRelMapper.listByRoleId(companyId, roleId);
        if (CollectionUtils.isNotEmpty(userRoleRelDOList)) {
            throw new CommonException(CommonErrorCodeEnum.VALIDATE_ERROR, "该角色存在用户使用不能删除, 请解绑关系后重试");
        }
        RoleEntity delRoleEntity = new RoleEntity();
        delRoleEntity.setRoleId(roleId);
        delRoleEntity.setIsDelete(DeleteStatusEnum.是.getCode().byteValue());
        delRoleEntity.setStatus(AccountStatusEnum.禁用.getCode().byteValue());
        delRoleEntity.setModifier(action.getModifier());
        this.roleRepository.editRoleEntity(delRoleEntity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchDelRoleByIds(BatchDelRoleByIdsAction action) {
        Long companyId = action.getCompanyId();
        List<Long> roleIds = action.getRoleIds();
        List<AccUserRoleRelDO> userRoleRelDOList = this.accUserRoleRelMapper.listByRoleIds(companyId, roleIds);
        if (CollectionUtils.isNotEmpty(userRoleRelDOList)) {
            throw new CommonException(CommonErrorCodeEnum.VALIDATE_ERROR, "角色存在用户使用不能删除, 请解绑关系后重试");
        }
        AccRoleDO delRoleDO = new AccRoleDO();
        delRoleDO.setIsDelete(DeleteStatusEnum.是.getCode().byteValue());
        delRoleDO.setStatus(AccountStatusEnum.禁用.getCode().byteValue());
        delRoleDO.setModifier(action.getModifier());
        this.accRoleMapper.updateByRoleIds(delRoleDO, companyId, roleIds);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void enableRole(EnableRoleAction action) {
        Long companyId = action.getCompanyId();
        Long roleId = action.getRoleId();
        AccRoleDO roleDo = this.accRoleMapper.selectByRoleId(companyId, roleId, DeleteStatusEnum.否.getCode().byteValue());
        if (Objects.isNull(roleDo)) {
            throw new CommonException(CommonErrorCodeEnum.VALIDATE_ERROR, "角色信息不存在");
        }
        if (AccountStatusEnum.启用.getCode().byteValue() == roleDo.getStatus()) {
            return;
        }
        roleDo.setStatus(AccountStatusEnum.启用.getCode().byteValue());
        roleDo.setModifier(action.getModifier());
        this.accRoleMapper.updateByPrimaryKeySelective(roleDo);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchEnableRole(BatchEnableRoleAction action) {
        Long companyId = action.getCompanyId();
        List<Long> roleIds = action.getRoleIds();
        List<AccRoleDO> roleDOList = this.accRoleMapper.listByRoleIds(companyId, roleIds, DeleteStatusEnum.否.getCode().byteValue());
        if (CollectionUtils.isEmpty(roleDOList) || roleDOList.size() != roleIds.size()) {
            throw new CommonException(CommonErrorCodeEnum.VALIDATE_ERROR, "角色信息不存在");
        }
        long disabledUserCount = roleDOList.stream()
                .filter(accRoleDO -> AccountStatusEnum.禁用.getCode().byteValue() == accRoleDO.getStatus()).count();
        if (roleIds.size() != disabledUserCount) {
            throw new CommonException(CommonErrorCodeEnum.VALIDATE_ERROR, "存在状态[启用]的角色");
        }
        AccRoleDO roleDo = new AccRoleDO();
        roleDo.setStatus(AccountStatusEnum.启用.getCode().byteValue());
        roleDo.setModifier(action.getModifier());
        this.accRoleMapper.updateByRoleIds(roleDo, companyId, roleIds);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void disabledRole(DisabledRoleAction action) {
        Long companyId = action.getCompanyId();
        Long roleId = action.getRoleId();
        AccRoleDO roleDo = this.accRoleMapper.selectByRoleId(companyId, roleId, DeleteStatusEnum.否.getCode().byteValue());
        if (Objects.isNull(roleDo)) {
            throw new CommonException(CommonErrorCodeEnum.VALIDATE_ERROR, "角色信息不存在");
        }
        if (AccountStatusEnum.禁用.getCode().byteValue() == roleDo.getStatus()) {
            return;
        }
        roleDo.setStatus(AccountStatusEnum.禁用.getCode().byteValue());
        roleDo.setModifier(action.getModifier());
        this.accRoleMapper.updateByPrimaryKeySelective(roleDo);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchDisabledRole(BatchDisabledRoleAction action) {
        Long companyId = action.getCompanyId();
        List<Long> roleIds = action.getRoleIds();
        List<AccRoleDO> roleDOList = this.accRoleMapper.listByRoleIds(companyId, roleIds, DeleteStatusEnum.否.getCode().byteValue());
        if (CollectionUtils.isEmpty(roleDOList) || roleDOList.size() != roleIds.size()) {
            throw new CommonException(CommonErrorCodeEnum.VALIDATE_ERROR, "角色信息不存在");
        }
        long disabledUserCount = roleDOList.stream()
                .filter(accRoleDO -> AccountStatusEnum.启用.getCode().byteValue() == accRoleDO.getStatus()).count();
        if (roleIds.size() != disabledUserCount) {
            throw new CommonException(CommonErrorCodeEnum.VALIDATE_ERROR, "存在状态[禁用]的角色");
        }
        AccRoleDO roleDo = new AccRoleDO();
        roleDo.setStatus(AccountStatusEnum.禁用.getCode().byteValue());
        roleDo.setModifier(action.getModifier());
        this.accRoleMapper.updateByRoleIds(roleDo, companyId, roleIds);
    }

    @Override
    public SimplePageInfo<RolePageDTO> queryPageByCondition(QueryRolePageByCondition action) {
        Long companyId = action.getCompanyId();
        String roleName = action.getRoleName();
        action.startPage();
        List<AccRoleDO> roleDOList = this.accRoleMapper.listByCondition(companyId, roleName, DeleteStatusEnum.否.getCode().byteValue());
        if (CollectionUtils.isEmpty(roleDOList)) {
            return new SimplePageInfo<>();
        }
        List<Long> roleIds = roleDOList.stream().map(AccRoleDO::getId).collect(Collectors.toList());
        List<AccUserRoleRelDO> userRoleRelDOList = this.accUserRoleRelMapper.listByRoleIds(companyId, roleIds);
        Map<Long, Long> userCountMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(userRoleRelDOList)) {
            userCountMap = userRoleRelDOList.stream().collect(Collectors.groupingBy(AccUserRoleRelDO::getRoleId, Collectors.counting()));
        }
        SimplePageInfo<RolePageDTO> pageInfo = SimplePageInfoUtils
                .convertSimplePageInfoResult(roleDOList, RolePageDTO.class);
        Map<Long, Long> finalUserCountMap = userCountMap;
        List<RolePageDTO> rolePageDTOList = roleDOList.stream().map(roleDO -> {
            RolePageDTO rolePageDTO = BeanCopyUtils.copyByJSON(roleDO, RolePageDTO.class);
            rolePageDTO.setRoleId(roleDO.getId());
            rolePageDTO.setUserCount(finalUserCountMap.get(roleDO.getId()));
            return rolePageDTO;
        }).collect(Collectors.toList());
        pageInfo.setList(rolePageDTOList);
        return pageInfo;
    }

    @Override
    public RoleDTO queryByRoleId(Long companyId, Long roleId) {
        AccRoleDO roleDo = this.accRoleMapper.selectByRoleId(companyId, roleId, DeleteStatusEnum.否.getCode().byteValue());
        if (Objects.isNull(roleDo)) {
            return null;
        }
        RoleDTO roleDTO = BeanCopyUtils.copyByJSON(roleDo, RoleDTO.class);
        roleDTO.setRoleId(roleId);
        return roleDTO;
    }

    @Override
    public List<RoleDTO> queryByRoleIds(Long companyId, List<Long> roleIds){
        if(CollectionUtils.isEmpty(roleIds)){
            return Lists.newArrayList();
        }
        List<AccRoleDO> roleDOList = this.accRoleMapper.listByRoleIds(companyId, roleIds, DeleteStatusEnum.否.getCode().byteValue());
        if(CollectionUtils.isEmpty(roleDOList)){
            return Lists.newArrayList();
        }

        return roleDOList.stream().map(roleDO -> {
            RoleDTO roleDTO = new RoleDTO();
            roleDTO.setRoleId(roleDO.getId());
            roleDTO.setRoleName(roleDO.getRoleName());
            roleDTO.setCompanyId(roleDO.getCompanyId());
            roleDTO.setStatus(roleDO.getStatus());
            return roleDTO;
        }).collect(Collectors.toList());
    }

    @Override
    public RolePermissionDTO queryPermissionByRoleId(Long companyId, Long roleId) {
        AccRoleDO roleDo = this.accRoleMapper.selectByRoleId(companyId, roleId, DeleteStatusEnum.否.getCode().byteValue());
        if (Objects.isNull(roleDo)) {
            throw new CommonException(CommonErrorCodeEnum.VALIDATE_ERROR, "角色信息不存在");
        }
        RolePermissionDTO rolePermissionDTO = BeanCopyUtils.copyByJSON(roleDo, RolePermissionDTO.class);
        rolePermissionDTO.setRoleId(roleId);
        List<AccRolePermissionRelDO> rolePermissionRelDOList = this.accRolePermissionRelMapper.listByRoleId(companyId, roleId);
        if (CollectionUtils.isEmpty(rolePermissionRelDOList)) {
            rolePermissionDTO.setPermissionCodeList(Lists.newArrayList());
            return rolePermissionDTO;
        }
        List<String> permissionCodeList = rolePermissionRelDOList.stream()
                .map(AccRolePermissionRelDO::getPermissionCode).collect(Collectors.toList());
        rolePermissionDTO.setPermissionCodeList(permissionCodeList);
        return rolePermissionDTO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void roleAuthByRoleId(RoleAuthByRoleIdAction action) {
        Long companyId = action.getCompanyId();
        Long roleId = action.getRoleId();
        Long modifier = action.getModifier();
        List<String> permissionCodeList = action.getPermissionCodeList();
        AccRoleDO roleDo = this.accRoleMapper.selectByRoleId(companyId, roleId, DeleteStatusEnum.否.getCode().byteValue());
        if (Objects.isNull(roleDo)) {
            throw new CommonException(CommonErrorCodeEnum.VALIDATE_ERROR, "角色信息不存在");
        }
        List<AccPermissionDO> permissionDOList = this.accPermissionMapper
                .listByPermissionCodes(companyId, permissionCodeList, DeleteStatusEnum.否.getCode().byteValue());
        if (CollectionUtils.isEmpty(permissionDOList) || permissionCodeList.size() != permissionDOList.size()) {
            throw new CommonException(CommonErrorCodeEnum.VALIDATE_ERROR, "权限信息不存在");
        }
        this.accRolePermissionRelMapper.deleteByRoleId(companyId, roleId);
        permissionCodeList.forEach(permissionCode -> {
            AccRolePermissionRelDO rolePermissionRelDO = new AccRolePermissionRelDO();
            rolePermissionRelDO.setCompanyId(companyId);
            rolePermissionRelDO.setRoleId(roleId);
            rolePermissionRelDO.setPermissionCode(permissionCode);
            rolePermissionRelDO.setCreator(modifier);
            rolePermissionRelDO.setModifier(modifier);
            this.accRolePermissionRelMapper.insertSelective(rolePermissionRelDO);
        });
    }

    @Override
    public List<RoleDTO> listRole(Long companyId){
        List<AccRoleDO> roleDOList = this.accRoleMapper.selectRoleList(companyId);
        if(CollectionUtils.isEmpty(roleDOList)){
            return Lists.newArrayList();
        }
        return roleDOList.stream().map(roleDO -> {
            RoleDTO roleDTO = new RoleDTO();
            roleDTO.setRoleId(roleDO.getId());
            roleDTO.setRoleName(roleDO.getRoleName());
            roleDTO.setCompanyId(roleDO.getCompanyId());
            roleDTO.setStatus(roleDO.getStatus());
            return roleDTO;
        }).collect(Collectors.toList());
    }
}
