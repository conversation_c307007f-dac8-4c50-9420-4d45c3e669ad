package com.das.province.service.biz.safe.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2023/7/11
 */
@Data
public class DeviceMonitorThExceptionDTO implements Serializable {

    private static final long serialVersionUID = 1233679487538468803L;

    /**
     * 公司名称
     */
    private String museumName;

    /**
     * 公司编码
     */
    private String uniqueCode;

    /**
     * 所属地区编码
     */
    private String regionCode;

    /**
     * 所在区域(1展厅,2库房,3其他)
     */
    private Byte regionName;

    private String regionNameStr;

    /**
     * 设备编码
     */
    private String deviceCode;

    /**
     * 设备位置
     */
    private String deviceAddress;

    /**
     * 最低温湿度阈值
     */
    private String minTh;

    /**
     * 最高温湿度阈值
     */
    private String maxTh;

    private String thLen;

    /**
     * 温湿度
     */
    private String thValue;

    /**
     * 温湿度异常程度: 1:轻度, 2:中度, 3:严重
     */
    private String exceptionDegree;

    private String exceptionDegreeStr;

    /**
     * 异常类型 1:温度异常, 2:湿度异常, 3:离线异常
     */
    private String exceptionType;

    /**
     * 异常类型 1:温度异常, 2:湿度异常, 3:离线异常
     */
    private String exceptionTypeStr;

    /**
     * 上报时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date reportTime;

    private String reportTimeStr;
}
