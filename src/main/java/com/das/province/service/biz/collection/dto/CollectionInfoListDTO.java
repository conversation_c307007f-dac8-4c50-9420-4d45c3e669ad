package com.das.province.service.biz.collection.dto;

import lombok.Data;

import java.util.List;

@Data
public class CollectionInfoListDTO {

    /**
     * 唯一id
     */
    private Long id;

    /**
     * 博物馆编码
     */
    private String uniqueCode;

    /**
     * 博物馆名称
     */
    private String museumName;

    /**
     * 文物名称
     */
    private String name;

    /**
     * 文物级别名称
     */
    private String levelName;

    /**
     * 文物类别名称
     */
    private String categoryName;

    /**
     * 年代名称
     */
    private String age;

    /**
     * 完成程度名称
     */
    private String completeDegreeName;

    /**
     * 质地名称
     */
    private String textureName;

    /**
     * 来源名称
     */
    private String sourceName;

    /**
     * 藏品图片url
     */
    private String coverUrl;

    private String coverCompressionUrl;

    /**
     * 具体年代
     */
    private String era;

    /**
     * 具体尺寸
     */
    private String size;

    /**
     * 二维影像路径集合
     */
    private List<String> twoModelUrlList;

    /**
     * 三维模型地址
     */
    private List<String> threeModelUrlList;

}
