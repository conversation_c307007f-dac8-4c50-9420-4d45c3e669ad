package com.das.province.service.biz.user.action;

import com.das.province.service.enums.PostCodeEnum;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

@Data
public class EditUserByUserIdAction implements Serializable {
    private static final long serialVersionUID = 2788351680895033950L;
    private Long userId;
    private Long companyId;
    private String userName;
    private String realName;
    private String password;
    private String phone;
    private Long departmentId;
    private Long positionId;
    private PostCodeEnum postCode;
    private Long modifier;

    /**
     * 角色ids
     */
    @NotNull(message = "角色不能为空")
    private List<Long> roleIds;

    /**
     * 职位名称
     */
    private String positionName;

    /**
     * 领导id
     */
    private Long leaderId;
}
