package com.das.province.service.biz.user.action;

import com.das.province.service.enums.PermissionTypeEnum;
import lombok.Data;

import java.io.Serializable;

@Data
public class SaveFunctionPermissionAction implements Serializable {
    private static final long serialVersionUID = 998449226682098241L;
    private Long permissionId;
    private String permissionName;
    private String permissionCode;
    private PermissionTypeEnum permissionType;
    private Long parentId;
    private Byte level;
    private Byte sortIndex;
}
