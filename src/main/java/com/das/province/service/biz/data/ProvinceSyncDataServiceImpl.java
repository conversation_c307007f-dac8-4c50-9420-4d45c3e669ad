package com.das.province.service.biz.data;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONException;
import com.das.province.common.extension.BizScenario;
import com.das.province.common.extension.ExtensionCoordinate;
import com.das.province.common.extension.ExtensionExecutor;
import com.das.province.common.utils.AesSecureUtil;
import com.das.province.common.utils.UUIDUtils;
import com.das.province.infr.dataobject.SyncMuseumAuthDO;
import com.das.province.service.biz.authentication.ProvinceAuthService;
import com.das.province.service.config.GlobalExecutor;
import com.das.province.service.extension.ProvinceSyncDataExtPt;
import com.das.province.service.extension.ProvinceSyncDataScenario;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.javatuples.Pair;
import org.slf4j.MDC;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.io.Serializable;
import java.util.Objects;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.BlockingQueue;

/**
 * <AUTHOR>
 * @date 2023/3/20
 */
@Slf4j
@Service
public class ProvinceSyncDataServiceImpl implements ProvinceSyncDataService {

    private volatile SyncDataNotifier notifier = new SyncDataNotifier();

    @Resource
    private ProvinceAuthService provinceAuthService;

    @Resource
    private ExtensionExecutor extensionExecutor;

    private static final String TRACE_ID = "TraceId";

    @PostConstruct
    public void init() {
        GlobalExecutor.submitNotifyTask(notifier);
    }

    private static final String DELIMITER = "!@#%";

    @Override
    public void provinceSyncDataReceive(Long museumBaseId, String dataBody) {
        SyncMuseumAuthDO authServiceMuseumAuth = provinceAuthService.getMuseumAuth(museumBaseId);
        if (Objects.isNull(authServiceMuseumAuth) || StringUtils.isBlank(authServiceMuseumAuth.getKey())
                || StringUtils.isBlank(authServiceMuseumAuth.getUniqueCode())) {
            log.error("ProvinceSyncDataServiceImpl provinceSyncDataReceive museumBaseId: [{}] key info is not exit", museumBaseId);
            return;
        }
        log.info("provinceSyncDataReceive add before task count: [{}]", notifier.getTaskSize());
        notifier.addTask(new Pair<>(extensionExecutor, new Pair<>(authServiceMuseumAuth.getKey() + DELIMITER + authServiceMuseumAuth.getUniqueCode(), dataBody)));
        log.info("provinceSyncDataReceive add after task count: [{}]", notifier.getTaskSize());
    }

    public static class SyncDataNotifier implements Runnable {

        private BlockingQueue<Pair<ExtensionExecutor, Pair<String, String>>> tasks = new ArrayBlockingQueue<>(1024 * 1024);

        void addTask(Pair<ExtensionExecutor, Pair<String, String>> pair) {
            if (Objects.isNull(pair)) {
                return;
            }
            tasks.offer(pair);
        }

        int getTaskSize() {
            return tasks.size();
        }

        @Override
        public void run() {
            while (!Thread.currentThread().isInterrupted()) {
                try {
                    String requestId = UUIDUtils.generateUUID(16);
                    MDC.put(TRACE_ID, requestId);
                    Pair<ExtensionExecutor, Pair<String, String>> pair = tasks.take();
                    handle(pair);
                    log.error("处理任务 task count: [{}]", tasks.size());
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt(); // 恢复中断状态
                    log.error("SyncDataNotifier thread interrupted", e);
                    break;
                } catch (Throwable e) {
                    log.error("SyncDataNotifier Error while handling notifying task", e);
                } finally {
                    MDC.remove(TRACE_ID);
                }
            }
        }

        private void handle(Pair<ExtensionExecutor, Pair<String, String>> pair) {
            try {
                ExtensionExecutor extensionExecutor = pair.getValue0();
                if (Objects.isNull(extensionExecutor)) {
                    return;
                }
                Pair<String, String> pair1 = pair.getValue1();
                String auth = pair1.getValue0();
                String[] authArr = auth.split(DELIMITER);
                String data = AesSecureUtil.decrypt(authArr[0], pair1.getValue1());
                SendDataBO sendDataBO = JSON.parseObject(data, SendDataBO.class);
                if (Objects.isNull(sendDataBO)
                        || StringUtils.isBlank(sendDataBO.getData())
                        || StringUtils.isBlank(sendDataBO.getDataType())) {
                    log.warn("SyncDataNotifier notifying data object is null");
                    return;
                }
                BizScenario bizScenario = BizScenario.valueOf(ProvinceSyncDataScenario.BIZ_ID, ProvinceSyncDataScenario.USE_CASE_SAVE,
                        ProvinceSyncDataScenario.getSyncDataSaveScenario(sendDataBO.getDataType()));
                ExtensionCoordinate extensionCoordinate = new ExtensionCoordinate(ProvinceSyncDataExtPt.class, bizScenario);
                log.error("开始入库数据：{}",authArr[1]);
                extensionExecutor.executeVoid(extensionCoordinate,
                        extension -> ((ProvinceSyncDataExtPt) extension).saveData(authArr[1], sendDataBO.getData()));
            } catch (JSONException e) {
                log.error("JSON parsing failed, invalid data format", e);
            } catch (Throwable e) {
                log.error("Unexpected error in handle()", e);
            }
        }
    }

    @Data
    private static class SendDataBO implements Serializable {

        private static final long serialVersionUID = 6935642940559631489L;

        private String data;

        private String dataType;
    }
}
