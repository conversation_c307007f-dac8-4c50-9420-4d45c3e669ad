package com.das.province.service.biz.data;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONException;
import com.das.province.common.extension.BizScenario;
import com.das.province.common.extension.ExtensionCoordinate;
import com.das.province.common.extension.ExtensionExecutor;
import com.das.province.common.utils.AesSecureUtil;
import com.das.province.common.utils.UUIDUtils;
import com.das.province.infr.dataobject.SyncMuseumAuthDO;
import com.das.province.service.biz.authentication.ProvinceAuthService;
import com.das.province.service.config.GlobalExecutor;
import com.das.province.service.extension.ProvinceSyncDataExtPt;
import com.das.province.service.extension.ProvinceSyncDataScenario;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.javatuples.Pair;
import org.slf4j.MDC;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.io.Serializable;
import java.util.Objects;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.BlockingQueue;

/**
 * <AUTHOR>
 * @date 2023/3/20
 */
@Slf4j
@Service
public class ProvinceSyncDataServiceImpl implements ProvinceSyncDataService {

    private volatile SyncDataNotifier notifier = new SyncDataNotifier();

    @Resource
    private ProvinceAuthService provinceAuthService;

    @Resource
    private ExtensionExecutor extensionExecutor;

    private static final String TRACE_ID = "TraceId";

    @PostConstruct
    public void init() {
        GlobalExecutor.submitNotifyTask(notifier);
        // 启动队列监控任务
        GlobalExecutor.submitNotifyTask(new QueueMonitorTask());
    }

    /**
     * 获取当前任务队列大小
     */
    public int getCurrentTaskSize() {
        return notifier.getTaskSize();
    }

    /**
     * 队列监控任务
     */
    private class QueueMonitorTask implements Runnable {
        @Override
        public void run() {
            while (!Thread.currentThread().isInterrupted()) {
                try {
                    Thread.sleep(30000); // 每30秒监控一次
                    int taskSize = notifier.getTaskSize();
                    if (taskSize > 0) {
                        log.info("任务队列监控 - 当前待处理任务数: [{}]", taskSize);
                    }

                    // 如果队列积压过多，发出警告
                    if (taskSize > 100) {
                        log.error("任务队列积压严重，当前待处理任务数: [{}]，请检查处理性能", taskSize);
                    }
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    log.info("队列监控任务被中断");
                    break;
                } catch (Exception e) {
                    log.error("队列监控任务异常: {}", e.getMessage(), e);
                }
            }
        }
    }

    private static final String DELIMITER = "!@#%";

    @Override
    public void provinceSyncDataReceive(Long museumBaseId, String dataBody) {
        if (Objects.isNull(museumBaseId)) {
            log.error("museumBaseId 为空，跳过处理");
            return;
        }

        if (StringUtils.isBlank(dataBody)) {
            log.error("dataBody 为空，museumBaseId: {}", museumBaseId);
            return;
        }

        SyncMuseumAuthDO authServiceMuseumAuth = provinceAuthService.getMuseumAuth(museumBaseId);
        if (Objects.isNull(authServiceMuseumAuth) || StringUtils.isBlank(authServiceMuseumAuth.getKey())
                || StringUtils.isBlank(authServiceMuseumAuth.getUniqueCode())) {
            log.error("博物馆认证信息不存在或不完整，museumBaseId: [{}]", museumBaseId);
            return;
        }

        int beforeTaskCount = notifier.getTaskSize();
        log.info("接收同步数据，museumBaseId: [{}], uniqueCode: [{}], 添加前任务数: [{}]",
                museumBaseId, authServiceMuseumAuth.getUniqueCode(), beforeTaskCount);

        boolean addResult = notifier.addTask(new Pair<>(extensionExecutor,
                new Pair<>(authServiceMuseumAuth.getKey() + DELIMITER + authServiceMuseumAuth.getUniqueCode(), dataBody)));

        int afterTaskCount = notifier.getTaskSize();
        if (addResult) {
            log.info("任务添加成功，museumBaseId: [{}], uniqueCode: [{}], 添加后任务数: [{}]",
                    museumBaseId, authServiceMuseumAuth.getUniqueCode(), afterTaskCount);
        } else {
            log.error("任务添加失败，可能队列已满，museumBaseId: [{}], uniqueCode: [{}], 当前任务数: [{}]",
                    museumBaseId, authServiceMuseumAuth.getUniqueCode(), afterTaskCount);
        }
    }

    public static class SyncDataNotifier implements Runnable {

        private BlockingQueue<Pair<ExtensionExecutor, Pair<String, String>>> tasks = new ArrayBlockingQueue<>(1024 * 1024);

        boolean addTask(Pair<ExtensionExecutor, Pair<String, String>> pair) {
            if (Objects.isNull(pair)) {
                log.error("尝试添加空任务，跳过");
                return false;
            }

            boolean result = tasks.offer(pair);
            if (!result) {
                log.error("任务队列已满，无法添加新任务，当前队列大小: {}", tasks.size());
            }
            return result;
        }

        int getTaskSize() {
            return tasks.size();
        }

        @Override
        public void run() {
            log.info("SyncDataNotifier 任务处理线程启动");
            while (!Thread.currentThread().isInterrupted()) {
                try {
                    String requestId = UUIDUtils.generateUUID(16);
                    MDC.put(TRACE_ID, requestId);

                    // 从队列中获取任务，如果队列为空会阻塞等待
                    Pair<ExtensionExecutor, Pair<String, String>> pair = tasks.take();
                    log.error("开始处理任务，剩余任务数: [{}], TraceId: [{}]", tasks.size(), requestId);

                    long startTime = System.currentTimeMillis();
                    handle(pair);
                    long endTime = System.currentTimeMillis();

                    log.error("任务处理完成，耗时: [{}]ms, 剩余任务数: [{}], TraceId: [{}]",
                            (endTime - startTime), tasks.size(), requestId);

                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt(); // 恢复中断状态
                    log.error("SyncDataNotifier 线程被中断", e);
                    break;
                } catch (Throwable e) {
                    log.error("SyncDataNotifier 处理任务时发生未预期异常，继续处理下一个任务", e);
                    // 不中断循环，继续处理下一个任务
                } finally {
                    MDC.remove(TRACE_ID);
                }
            }
            log.error("SyncDataNotifier 任务处理线程退出");
        }

        private void handle(Pair<ExtensionExecutor, Pair<String, String>> pair) {
            String uniqueCode = "unknown";
            String dataType = "unknown";

            try {
                ExtensionExecutor extensionExecutor = pair.getValue0();
                if (Objects.isNull(extensionExecutor)) {
                    log.error("ExtensionExecutor 为空，跳过处理");
                    return;
                }

                Pair<String, String> pair1 = pair.getValue1();
                if (Objects.isNull(pair1)) {
                    log.error("数据对象为空，跳过处理");
                    return;
                }

                String auth = pair1.getValue0();
                if (StringUtils.isBlank(auth)) {
                    log.error("认证信息为空，跳过处理");
                    return;
                }

                String[] authArr = auth.split(DELIMITER);
                if (authArr.length < 2) {
                    log.error("认证信息格式错误，跳过处理，auth: {}", auth);
                    return;
                }

                uniqueCode = authArr[1];

                String encryptedData = pair1.getValue1();
                if (StringUtils.isBlank(encryptedData)) {
                    log.error("加密数据为空，uniqueCode: {}", uniqueCode);
                    return;
                }

                String data = AesSecureUtil.decrypt(authArr[0], encryptedData);
                if (StringUtils.isBlank(data)) {
                    log.error("解密后数据为空，uniqueCode: {}", uniqueCode);
                    return;
                }

                SendDataBO sendDataBO = JSON.parseObject(data, SendDataBO.class);
                if (Objects.isNull(sendDataBO)) {
                    log.error("解析数据对象为空，uniqueCode: {}", uniqueCode);
                    return;
                }

                if (StringUtils.isBlank(sendDataBO.getData()) || StringUtils.isBlank(sendDataBO.getDataType())) {
                    log.error("数据内容或数据类型为空，uniqueCode: {}, dataType: {}, hasData: {}",
                            uniqueCode, sendDataBO.getDataType(), !StringUtils.isBlank(sendDataBO.getData()));
                    return;
                }

                dataType = sendDataBO.getDataType();
                String scenario = ProvinceSyncDataScenario.getSyncDataSaveScenario(dataType);
                if (StringUtils.isBlank(scenario)) {
                    log.error("未找到对应的处理场景，uniqueCode: {}, dataType: {}", uniqueCode, dataType);
                    return;
                }

                BizScenario bizScenario = BizScenario.valueOf(ProvinceSyncDataScenario.BIZ_ID,
                        ProvinceSyncDataScenario.USE_CASE_SAVE, scenario);
                ExtensionCoordinate extensionCoordinate = new ExtensionCoordinate(ProvinceSyncDataExtPt.class, bizScenario);

                log.info("开始处理数据入库，uniqueCode: {}, dataType: {}, scenario: {}", uniqueCode, dataType, scenario);

                String finalUniqueCode = uniqueCode;
                extensionExecutor.executeVoid(extensionCoordinate,
                        extension -> ((ProvinceSyncDataExtPt) extension).saveData(finalUniqueCode, sendDataBO.getData()));

                log.info("数据入库处理完成，uniqueCode: {}, dataType: {}", uniqueCode, dataType);

            } catch (JSONException e) {
                log.error("JSON解析失败，数据格式无效，uniqueCode: {}, dataType: {}, 错误: {}",
                        uniqueCode, dataType, e.getMessage(), e);
            } catch (Exception e) {
                log.error("数据解密失败，uniqueCode: {}, dataType: {}, 错误: {}",
                        uniqueCode, dataType, e.getMessage(), e);
            } catch (Throwable e) {
                log.error("处理数据时发生未预期异常，uniqueCode: {}, dataType: {}, 错误: {}",
                        uniqueCode, dataType, e.getMessage(), e);
            }
        }
    }

    @Data
    private static class SendDataBO implements Serializable {

        private static final long serialVersionUID = 6935642940559631489L;

        private String data;

        private String dataType;
    }
}
