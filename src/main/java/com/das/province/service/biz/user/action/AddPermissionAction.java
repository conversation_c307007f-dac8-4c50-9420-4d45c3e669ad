package com.das.province.service.biz.user.action;

import com.das.province.service.enums.FunctionStatusEnum;
import com.das.province.service.enums.PermissionTypeEnum;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class AddPermissionAction implements Serializable {
    private static final long serialVersionUID = 5947500425611034536L;
    private Long companyId;
    private String permissionName;
    private String permissionCode;
    private PermissionTypeEnum permissionType;
    private Long parentId;
    private Byte level;
    private Byte sortIndex;
    private FunctionStatusEnum functionStatus;
    private String resourceUrl;
    private String resourceIcon;
    private Long creator;
    private Long modifier;
    private List<SaveFunctionPermissionAction> functionPermissionList;
}
