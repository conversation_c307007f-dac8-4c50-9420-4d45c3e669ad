package com.das.province.service.biz.common.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
public class CommonDocumentDTO implements Serializable {
    private static final long serialVersionUID = -9111941902608696208L;
    private Long documentId;
    private String fileType;
    private String fileFormat;
    private String sourceFileName;
    private String saveFileName;
    private Long fileSize;
    private String url;
    private String description;
    private Long creator;
    private String creatorName;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date gmtCreate;
    private List<CommDocumentExtendDTO> documentExtendList;

    // 三维模型相关参数
    /**
     * 三维资源使用的三维模型路径
     */
    private String threeModelUrl;
    /**
     * 三维资源状态 0未转换 1转换中 2转换成功 3转换失败
     */
    private Byte threeModelStatus;
    /**
     * 错误信息
     */
    private String errorInfo;
}
