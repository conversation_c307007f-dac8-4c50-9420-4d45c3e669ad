package com.das.province.service.extension.ext;

import com.alibaba.fastjson.JSON;
import com.das.province.common.extension.Extension;
import com.das.province.common.utils.DateUtils;
import com.das.province.infr.dataobject.SyncCulturalRelicsDO;
import com.das.province.infr.dataobject.SyncCulturalRelicsInfoDO;
import com.das.province.infr.dataobject.SyncOrderBaseDO;
import com.das.province.infr.mapper.SyncCulturalRelicsInfoMapper;
import com.das.province.infr.mapper.SyncCulturalRelicsMapper;
import com.das.province.service.extension.ProvinceSyncDataExtPt;
import com.das.province.service.extension.ProvinceSyncDataScenario;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.TransactionStatus;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/3/21
 */
@Slf4j
@Component
@Extension(bizId = ProvinceSyncDataScenario.BIZ_ID, useCase = ProvinceSyncDataScenario.USE_CASE_SAVE,
        scenario = ProvinceSyncDataScenario.CULTURAL_INFO_SAVE_SCENARIO)
public class SyncCulturalInfoSaveExt implements ProvinceSyncDataExtPt {

    @Resource
    private SyncCulturalRelicsInfoMapper syncCulturalRelicsInfoMapper;


    @Override
    public void saveData(String uniqueCode, String data) {
        if (StringUtils.isBlank(data)) {
            return;
        }
        // 删除所有，再新增
        this.syncCulturalRelicsInfoMapper.deleteByUniqueCode(uniqueCode);
        List<SyncCulturalRelicsInfoDO> culturalRelicsList = JSON.parseArray(data, SyncCulturalRelicsInfoDO.class);
        if (CollectionUtils.isEmpty(culturalRelicsList)) {
            return;
        }

        List<SyncCulturalRelicsInfoDO> needInsertList = new ArrayList<>();
        culturalRelicsList.forEach(u -> {
            if ("-1000L".equals(u.getNumber())) {
                return;
            }
            u.setUniqueCode(uniqueCode);
            u.setGmtCreate(new Date());
            u.setGmtModified(new Date());
            needInsertList.add(u);
            // 100条插入1次 或者 已经是最后一条直接插入
            if (needInsertList.size() >= 100 || culturalRelicsList.size() == culturalRelicsList.indexOf(u) + 1) {
                if(CollectionUtils.isNotEmpty(needInsertList)){
                    this.syncCulturalRelicsInfoMapper.batchInsert(needInsertList);
                    needInsertList.clear();
                }
            }
        });
        culturalRelicsList.clear();
    }
}
