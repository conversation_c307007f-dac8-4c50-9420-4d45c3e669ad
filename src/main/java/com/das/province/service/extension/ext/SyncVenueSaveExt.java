package com.das.province.service.extension.ext;

import com.alibaba.fastjson.JSON;
import com.das.province.common.extension.Extension;
import com.das.province.infr.dataobject.SyncVenueDO;
import com.das.province.infr.dataobject.SyncVenueInfoDO;
import com.das.province.infr.mapper.SyncVenueInfoMapper;
import com.das.province.service.extension.ProvinceSyncDataExtPt;
import com.das.province.service.extension.ProvinceSyncDataScenario;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.Objects;

@Slf4j
@Component
@Extension(bizId = ProvinceSyncDataScenario.BIZ_ID, useCase = ProvinceSyncDataScenario.USE_CASE_SAVE,
        scenario = ProvinceSyncDataScenario.VENUE_SAVE_SCENARIO)
public class SyncVenueSaveExt implements ProvinceSyncDataExtPt {

    @Resource
    private SyncVenueInfoMapper syncVenueInfoMapper;

    @Override
    public void saveData(String uniqueCode, String data) {
        if (StringUtils.isBlank(data)) {
            return;
        }
        SyncVenueDO syncVenueDO = JSON.parseObject(data, SyncVenueDO.class);
        SyncVenueInfoDO syncVenueInfoDO = syncVenueInfoMapper.selectByMuseumId(uniqueCode);
        if(Objects.nonNull(syncVenueInfoDO)){
            syncVenueInfoDO.setVenueClose(syncVenueDO.getVenueClose());
            syncVenueInfoDO.setGmtModified(new Date());
            syncVenueInfoMapper.updateByUniqueCode(syncVenueInfoDO);
        }else{
            syncVenueInfoDO = new SyncVenueInfoDO();
            syncVenueInfoDO.setUniqueCode(uniqueCode);
            syncVenueInfoDO.setVenueClose(syncVenueDO.getVenueClose());
            syncVenueInfoMapper.insertSelective(syncVenueInfoDO);
        }
    }
}
