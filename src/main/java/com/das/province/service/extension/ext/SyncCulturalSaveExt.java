package com.das.province.service.extension.ext;

import com.alibaba.fastjson.JSON;
import com.das.province.common.extension.Extension;
import com.das.province.common.utils.DateUtils;
import com.das.province.infr.dataobject.SyncCulturalRelicsDO;
import com.das.province.infr.mapper.SyncCulturalRelicsMapper;
import com.das.province.service.extension.ProvinceSyncDataExtPt;
import com.das.province.service.extension.ProvinceSyncDataScenario;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023/3/21
 */
@Slf4j
@Component
@Extension(bizId = ProvinceSyncDataScenario.BIZ_ID, useCase = ProvinceSyncDataScenario.USE_CASE_SAVE,
        scenario = ProvinceSyncDataScenario.CULTURAL_SAVE_SCENARIO)
public class SyncCulturalSaveExt implements ProvinceSyncDataExtPt {

    @Resource
    private SyncCulturalRelicsMapper syncCulturalRelicsMapper;

    @Override
    public void saveData(String uniqueCode, String data) {
        if (StringUtils.isBlank(data)) {
            return;
        }
        List<SyncCulturalRelicsDO> culturalRelicsList = JSON.parseArray(data, SyncCulturalRelicsDO.class);
        if (CollectionUtils.isEmpty(culturalRelicsList)) {
            return;
        }
        List<SyncCulturalRelicsDO> culturalRelicsListQuery = this.syncCulturalRelicsMapper.listUniqueCode(uniqueCode);
        Map<String, SyncCulturalRelicsDO> culturalRelicsMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(culturalRelicsListQuery)) {
            culturalRelicsListQuery.forEach(k ->
                    culturalRelicsMap.put(k.getNumber() + "_" + DateUtils.formatDateYYYMMDD(k.getStatisticsDate()), k));
        }
        culturalRelicsList.forEach(u -> {
            if ("-1000L".equals(u.getNumber())) {
                this.syncCulturalRelicsMapper.deleteByUniqueCode(uniqueCode);
                return;
            }
            /*SyncCulturalRelicsDO queryCultural = this.syncCulturalRelicsMapper
                    .selectUniqueCodeAndNumberAndStatisticsDate(uniqueCode, u.getNumber(), u.getStatisticsDate());*/
            try {
                SyncCulturalRelicsDO queryCultural = culturalRelicsMap.get(u.getNumber() + "_" + DateUtils.formatDateYYYMMDD(u.getStatisticsDate()));
                if (Objects.isNull(queryCultural)) {
                    u.setUniqueCode(uniqueCode);
                    this.syncCulturalRelicsMapper.insertSelective(u);
                } else {
                    queryCultural.setId(queryCultural.getId());
                    this.syncCulturalRelicsMapper.updateByPrimaryKeySelective(queryCultural);
                }
            } catch (Exception e) {
                log.error("SyncCulturalSaveExt saveData uniqueCode: [{}] data: [{}] Exception: ", uniqueCode, JSON.toJSONString(u), e);
            }
        });

    }
}
