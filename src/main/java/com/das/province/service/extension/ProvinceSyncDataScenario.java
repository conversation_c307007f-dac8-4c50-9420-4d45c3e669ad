package com.das.province.service.extension;

import com.das.province.service.enums.SyncDataBizTypeEnum;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 * @date 2023/3/21
 */
public class ProvinceSyncDataScenario {

    private ProvinceSyncDataScenario() {
    }
    public static final String BIZ_ID = "syncData";
    public static final String USE_CASE_SAVE = "save";
    public static final String USER_SAVE_SCENARIO = "userSave";
    public static final String COLLECTION_SAVE_SCENARIO = "collectionSave";
    public static final String CULTURAL_SAVE_SCENARIO = "culturalSave";
    public static final String CULTURAL_INFO_SAVE_SCENARIO = "culturalInfoSave";
    public static final String ORDER_SAVE_SCENARIO = "orderSave";
    public static final String HISTORY_ORDER_SAVE_SCENARIO = "historyOrderSave";
    public static final String COLLECTION_DIGITAL_SAVE_SCENARIO = "collectionDigitalSave";
    public static final String VENUE_SAVE_SCENARIO = "venueSave";
    public static final String DEVICE_SAVE_SCENARIO = "deviceSave";
    public static final String DEVICE_REC_SAVE_SCENARIO = "deviceRecSave";
    private static final Map<String, String> SYNC_DATA_SCENARIO_DEL_MAP = new ConcurrentHashMap<>();
    static {
        SYNC_DATA_SCENARIO_DEL_MAP.put(SyncDataBizTypeEnum.UZ.getCode(), USER_SAVE_SCENARIO);
        SYNC_DATA_SCENARIO_DEL_MAP.put(SyncDataBizTypeEnum.CP.getCode(), COLLECTION_SAVE_SCENARIO);
        SYNC_DATA_SCENARIO_DEL_MAP.put(SyncDataBizTypeEnum.OD.getCode(), ORDER_SAVE_SCENARIO);
        SYNC_DATA_SCENARIO_DEL_MAP.put(SyncDataBizTypeEnum.HISTORY_OD.getCode(), HISTORY_ORDER_SAVE_SCENARIO);
        SYNC_DATA_SCENARIO_DEL_MAP.put(SyncDataBizTypeEnum.CL.getCode(), CULTURAL_SAVE_SCENARIO);
        SYNC_DATA_SCENARIO_DEL_MAP.put(SyncDataBizTypeEnum.CL_INFO.getCode(), CULTURAL_INFO_SAVE_SCENARIO);
        SYNC_DATA_SCENARIO_DEL_MAP.put(SyncDataBizTypeEnum.CP_DIGITAL.getCode(), COLLECTION_DIGITAL_SAVE_SCENARIO);
        SYNC_DATA_SCENARIO_DEL_MAP.put(SyncDataBizTypeEnum.VE.getCode(), VENUE_SAVE_SCENARIO);
        SYNC_DATA_SCENARIO_DEL_MAP.put(SyncDataBizTypeEnum.DER.getCode(), DEVICE_REC_SAVE_SCENARIO);
        SYNC_DATA_SCENARIO_DEL_MAP.put(SyncDataBizTypeEnum.DE.getCode(), DEVICE_SAVE_SCENARIO);
    }
    public static String getSyncDataSaveScenario(String bizType) {
        return SYNC_DATA_SCENARIO_DEL_MAP.get(bizType);
    }
}
