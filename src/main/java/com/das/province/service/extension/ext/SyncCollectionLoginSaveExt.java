package com.das.province.service.extension.ext;

import com.alibaba.fastjson.JSON;
import com.das.province.common.extension.Extension;
import com.das.province.infr.dataobject.DataChangeDetailTemporaryDO;
import com.das.province.infr.dataobject.SyncCommCollectionRegisterInfoDO;
import com.das.province.infr.dataobject.SyncOrderBaseDO;
import com.das.province.infr.mapper.DataChangeDetailTemporaryMapper;
import com.das.province.infr.mapper.SyncCommCollectionRegisterInfoMapper;
import com.das.province.service.extension.ProvinceSyncDataExtPt;
import com.das.province.service.extension.ProvinceSyncDataScenario;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/3/21
 */
@Slf4j
@Component
@Extension(bizId = ProvinceSyncDataScenario.BIZ_ID, useCase = ProvinceSyncDataScenario.USE_CASE_SAVE,
        scenario = ProvinceSyncDataScenario.COLLECTION_SAVE_SCENARIO)
public class SyncCollectionLoginSaveExt implements ProvinceSyncDataExtPt {

    @Resource
    private SyncCommCollectionRegisterInfoMapper syncCommCollectionRegisterInfoMapper;

    @Resource
    private DataChangeDetailTemporaryMapper dataChangeDetailTemporaryMapper;

    @Override
    public void saveData(String uniqueCode, String data) {
        if (StringUtils.isBlank(data) || StringUtils.isBlank(uniqueCode)) {
            return;
        }
        List<SyncCommCollectionRegisterInfoDO> collectionRegisterList = JSON.parseArray(data, SyncCommCollectionRegisterInfoDO.class);
        // 监测藏品异动
        createCollectionChangeData(uniqueCode,collectionRegisterList);

        // 先删除所有藏品，再新增
        syncCommCollectionRegisterInfoMapper.deleteByUniqueCode(uniqueCode);
        if (CollectionUtils.isEmpty(collectionRegisterList)) {
            return;
        }

        List<SyncCommCollectionRegisterInfoDO> needInsertList = new ArrayList<>();
        collectionRegisterList.forEach(u -> {
            if (-1000L == u.getCollectionId()) {
                return;
            }
            u.setUniqueCode(uniqueCode);
            u.setGmtCreate(new Date());
            u.setGmtModified(new Date());
            needInsertList.add(u);
            // 100条插入1次 或者 已经是最后一条直接插入
            if (needInsertList.size() >= 100 || collectionRegisterList.size() == collectionRegisterList.indexOf(u) + 1) {
                if(CollectionUtils.isNotEmpty(needInsertList)){
                    this.syncCommCollectionRegisterInfoMapper.batchInsert(needInsertList);
                    needInsertList.clear();
                }
            }
        });
        collectionRegisterList.clear();
    }

    /**
     * 藏品异动产生逻辑
     * @param uniqueCode
     * @param collectionRegisterList
     */
    public void createCollectionChangeData(String uniqueCode,List<SyncCommCollectionRegisterInfoDO> collectionRegisterList){
        // 历史藏品数量
        int historyTotalNum = syncCommCollectionRegisterInfoMapper.countByUniqueCode(uniqueCode,null);
        //不存在历史数据不产生异动
        if(historyTotalNum <= 0){
            return;
        }
        // 本次同步数据
        int totalNum = collectionRegisterList.size();
        // 已注销
        int logOffNum = (int)collectionRegisterList.stream().filter(registerInfoDO -> "ALGF".equals(registerInfoDO.getRegisterStatus())).count();

        // 历史数据已注销数量
        int historyLogOffNum = syncCommCollectionRegisterInfoMapper.countByUniqueCode(uniqueCode,"ALGF");

        // 藏品总量变少的时候不提醒
        if(totalNum <= historyTotalNum){
            return;
        }

        // 本次注销
        int thisLogOff = logOffNum - historyLogOffNum;
        if(thisLogOff > 0){
            DataChangeDetailTemporaryDO dataChangeDetailTemporaryDO = new DataChangeDetailTemporaryDO();
            dataChangeDetailTemporaryDO.setMuseumId(uniqueCode);
            dataChangeDetailTemporaryDO.setUpDown((byte)2);
            dataChangeDetailTemporaryDO.setChangeData(thisLogOff);
            dataChangeDetailTemporaryMapper.insertSelective(dataChangeDetailTemporaryDO);
        }

        // 本次新登录
        int thisRegister = totalNum - historyTotalNum;
        DataChangeDetailTemporaryDO dataChangeDetailTemporary = new DataChangeDetailTemporaryDO();
        dataChangeDetailTemporary.setMuseumId(uniqueCode);
        dataChangeDetailTemporary.setUpDown((byte)1);
        dataChangeDetailTemporary.setChangeData(thisRegister);
        dataChangeDetailTemporaryMapper.insertSelective(dataChangeDetailTemporary);
    }
}
