package com.das.province.service.extension.ext;

import com.alibaba.fastjson.JSON;
import com.das.province.common.extension.Extension;
import com.das.province.infr.dataobject.SyncCollectionDigitalDO;
import com.das.province.infr.mapper.SyncCollectionDigitalMapper;
import com.das.province.service.extension.ProvinceSyncDataExtPt;
import com.das.province.service.extension.ProvinceSyncDataScenario;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionDefinition;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023/4/11
 */
@Slf4j
@Component
@Extension(bizId = ProvinceSyncDataScenario.BIZ_ID, useCase = ProvinceSyncDataScenario.USE_CASE_SAVE,
        scenario = ProvinceSyncDataScenario.COLLECTION_DIGITAL_SAVE_SCENARIO)
public class SyncCollectionDigitalSaveExt implements ProvinceSyncDataExtPt {

    @Resource
    private SyncCollectionDigitalMapper syncCollectionDigitalMapper;

    @Autowired
    private PlatformTransactionManager platformTransactionManager;

    @Autowired
    private TransactionDefinition transactionDefinition;

    @Override
    public void saveData(String uniqueCode, String data) {
        if (StringUtils.isBlank(data)) {
            return;
        }
        SyncCollectionDigitalDO collectionDigital = JSON.parseObject(data, SyncCollectionDigitalDO.class);
        if (Objects.isNull(collectionDigital)) {
            return;
        }
        if (-1000 == collectionDigital.getTwoModelNum()) {
            this.syncCollectionDigitalMapper.deleteByUniqueCode(uniqueCode);
            return;
        }
        SyncCollectionDigitalDO queryCollectionDigital = this.syncCollectionDigitalMapper.selectByUniqueCode(uniqueCode);
        if (Objects.isNull(queryCollectionDigital)) {
            collectionDigital.setUniqueCode(uniqueCode);
            this.syncCollectionDigitalMapper.insertSelective(collectionDigital);
            return;
        }
        if (collectionDigital.getTwoModelNum().equals(queryCollectionDigital.getTwoModelNum())
                && collectionDigital.getThreeModelNum().equals(queryCollectionDigital.getThreeModelNum())) {
            return;
        }
        queryCollectionDigital.setTwoModelNum(collectionDigital.getTwoModelNum());
        queryCollectionDigital.setThreeModelNum(collectionDigital.getThreeModelNum());
        this.syncCollectionDigitalMapper.updateByPrimaryKeySelective(queryCollectionDigital);
    }
}
