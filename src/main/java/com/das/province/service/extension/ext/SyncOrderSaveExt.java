package com.das.province.service.extension.ext;

import com.alibaba.fastjson.JSON;
import com.das.province.common.extension.Extension;
import com.das.province.common.utils.DateUtils;
import com.das.province.infr.dataobject.SyncOrderBaseDO;
import com.das.province.infr.mapper.SyncOrderBaseMapper;
import com.das.province.service.extension.ProvinceSyncDataExtPt;
import com.das.province.service.extension.ProvinceSyncDataScenario;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2023/3/21
 */
@Slf4j
@Component
@Extension(bizId = ProvinceSyncDataScenario.BIZ_ID, useCase = ProvinceSyncDataScenario.USE_CASE_SAVE,
        scenario = ProvinceSyncDataScenario.ORDER_SAVE_SCENARIO)
public class SyncOrderSaveExt implements ProvinceSyncDataExtPt {

    @Resource
    private SyncOrderBaseMapper syncOrderBaseMapper;

    @Override
    public void saveData(String uniqueCode, String data) {
        if (StringUtils.isBlank(data)) {
            return;
        }
        // 先删除当天观众，再新增
        this.syncOrderBaseMapper.deleteByUniqueCode(uniqueCode, DateUtils.getTimesDayMorning(),null);
        List<SyncOrderBaseDO> orderBaseList = JSON.parseArray(data, SyncOrderBaseDO.class);
        if (CollectionUtils.isEmpty(orderBaseList)) {
            return;
        }
        List<SyncOrderBaseDO> needInsertList = new ArrayList<>();
        orderBaseList.forEach(order -> {
            order.setUniqueCode(uniqueCode);
            if(order.getReserveType() == null){
                order.setReserveType((byte)2);
            }
            order.setGmtCreate(new Date());
            order.setGmtModified(new Date());
            needInsertList.add(order);
            // 100条插入1次 或者 已经是最后一条直接插入
            if (needInsertList.size() >= 100 || orderBaseList.size() == orderBaseList.indexOf(order) + 1) {
                if(CollectionUtils.isNotEmpty(needInsertList)){
                    syncOrderBaseMapper.batchInsert(needInsertList);
                    needInsertList.clear();
                }
            }
        });
        orderBaseList.clear();
    }
}
