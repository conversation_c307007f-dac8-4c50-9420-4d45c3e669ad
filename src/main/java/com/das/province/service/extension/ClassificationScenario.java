package com.das.province.service.extension;

import com.das.province.service.enums.ClassificationBizTypeEnum;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

public class ClassificationScenario {
    private ClassificationScenario() {
    }
    public static final String BIZ_ID = "classification";
    public static final String USE_CASE_DELETE = "delete";
    public static final String DICTIONARY_DELETE_SCENARIO = "dictionaryDelete";
    public static final String WAREHOUSE_DELETE_SCENARIO = "warehouseDelete";
    public static final String DEPARTMENT_DELETE_SCENARIO = "departmentDelete";
    private static final Map<Integer, String> CLASSIFICATION_SCENARIO_DEL_MAP = new ConcurrentHashMap<>();
    static {
        CLASSIFICATION_SCENARIO_DEL_MAP.put(ClassificationBizTypeEnum.DIC.getCode(), DICTIONARY_DELETE_SCENARIO);
        CLASSIFICATION_SCENARIO_DEL_MAP.put(ClassificationBizTypeEnum.WHS.getCode(), WAREHOUSE_DELETE_SCENARIO);
        CLASSIFICATION_SCENARIO_DEL_MAP.put(ClassificationBizTypeEnum.DPTMT.getCode(), DEPARTMENT_DELETE_SCENARIO);
    }
    public static String getDelClassificationScenario(Integer bizType) {
        return CLASSIFICATION_SCENARIO_DEL_MAP.get(bizType);
    }
}
