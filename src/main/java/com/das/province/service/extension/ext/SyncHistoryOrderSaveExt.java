package com.das.province.service.extension.ext;

import com.alibaba.fastjson.JSON;
import com.das.province.common.extension.Extension;
import com.das.province.common.utils.DateUtils;
import com.das.province.infr.dataobject.SyncOrderBaseDO;
import com.das.province.infr.mapper.SyncOrderBaseMapper;
import com.das.province.service.extension.ProvinceSyncDataExtPt;
import com.das.province.service.extension.ProvinceSyncDataScenario;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/7/26
 */
@Slf4j
@Component
@Extension(bizId = ProvinceSyncDataScenario.BIZ_ID, useCase = ProvinceSyncDataScenario.USE_CASE_SAVE,
        scenario = ProvinceSyncDataScenario.HISTORY_ORDER_SAVE_SCENARIO)
public class SyncHistoryOrderSaveExt implements ProvinceSyncDataExtPt {

    @Resource
    private SyncOrderBaseMapper syncOrderBaseMapper;

    @Override
    public void saveData(String uniqueCode, String data) {
        log.error("接收历史观众数据：{}", uniqueCode);
        if (StringUtils.isBlank(data)) {
            log.warn("历史观众数据为空，uniqueCode: {}", uniqueCode);
            return;
        }

        try {
            // 先删除历史所有，再新增
            int deletedCount = this.syncOrderBaseMapper.deleteByUniqueCode(uniqueCode, DateUtils.getTimesYearMorning(), null);
            log.info("删除历史观众数据，uniqueCode: {}, 删除条数: {}", uniqueCode, deletedCount);

            List<SyncOrderBaseDO> orderBaseList = JSON.parseArray(data, SyncOrderBaseDO.class);
            if (CollectionUtils.isEmpty(orderBaseList)) {
                log.warn("解析后的历史观众数据为空，uniqueCode: {}", uniqueCode);
                return;
            }

            log.info("开始处理历史观众数据，uniqueCode: {}, 总条数: {}", uniqueCode, orderBaseList.size());

            List<SyncOrderBaseDO> needInsertList = new ArrayList<>();
            int processedCount = 0;

            for (int i = 0; i < orderBaseList.size(); i++) {
                SyncOrderBaseDO order = orderBaseList.get(i);
                order.setUniqueCode(uniqueCode);
                if(order.getReserveType() == null){
                    order.setReserveType((byte)2);
                }
                order.setGmtCreate(new Date());
                order.setGmtModified(new Date());
                needInsertList.add(order);

                // 100条插入1次 或者 已经是最后一条直接插入
                if (needInsertList.size() >= 100 || i == orderBaseList.size() - 1) {
                    if(CollectionUtils.isNotEmpty(needInsertList)){
                        try {
                            syncOrderBaseMapper.batchInsert(needInsertList);
                            processedCount += needInsertList.size();
                            log.info("批量插入历史观众数据成功，uniqueCode: {}, 本次插入: {}, 已处理: {}/{}",
                                    uniqueCode, needInsertList.size(), processedCount, orderBaseList.size());
                            needInsertList.clear();
                        } catch (Exception e) {
                            log.error("批量插入历史观众数据失败，uniqueCode: {}, 本次数据量: {}, 错误: {}",
                                    uniqueCode, needInsertList.size(), e.getMessage(), e);
                            needInsertList.clear();
                            // 继续处理后续数据，不中断整个流程
                        }
                    }
                }
            }

            log.info("历史观众数据处理完成，uniqueCode: {}, 成功处理: {}/{}", uniqueCode, processedCount, orderBaseList.size());

        } catch (Exception e) {
            log.error("处理历史观众数据异常，uniqueCode: {}, 错误: {}", uniqueCode, e.getMessage(), e);
        } finally {
            // 确保释放内存
            try {
                // 这里不需要显式清理，让GC处理
            } catch (Exception e) {
                log.warn("清理资源时发生异常: {}", e.getMessage());
            }
        }
    }
}
