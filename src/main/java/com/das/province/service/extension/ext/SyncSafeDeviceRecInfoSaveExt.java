package com.das.province.service.extension.ext;

import com.alibaba.fastjson.JSON;
import com.das.province.common.extension.Extension;
import com.das.province.common.utils.BeanCopyUtils;
import com.das.province.infr.dataobject.*;
import com.das.province.infr.mapper.*;
import com.das.province.service.extension.ProvinceSyncDataExtPt;
import com.das.province.service.extension.ProvinceSyncDataScenario;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/7/15
 */
@Slf4j
@Component
@Extension(bizId = ProvinceSyncDataScenario.BIZ_ID, useCase = ProvinceSyncDataScenario.USE_CASE_SAVE,
        scenario = ProvinceSyncDataScenario.DEVICE_REC_SAVE_SCENARIO)
public class SyncSafeDeviceRecInfoSaveExt implements ProvinceSyncDataExtPt {

    @Resource
    private SyncSafeTemperatureHumidityDeviceMapper syncSafeTemperatureHumidityDeviceMapper;

    @Resource
    private SyncSafeTemperatureHumidityRecordMapper syncSafeTemperatureHumidityRecordMapper;

    @Resource
    private SyncSafeDeviceInfoMapper syncSafeDeviceInfoMapper;

    @Resource
    private SyncSafeDeviceInfoRealtimeMapper syncSafeDeviceInfoRealtimeMapper;

    @Autowired
    private PlatformTransactionManager platformTransactionManager;

    @Autowired
    private TransactionDefinition transactionDefinition;

    @Resource
    private MuseumBaseInfoMapper museumBaseInfoMapper;

    @Resource
    private DataChangeDetailTemporaryMapper dataChangeDetailTemporaryMapper;

    @Resource
    private DataChangeInfoMapper dataChangeInfoMapper;

    @Resource
    private DataChangeDetailInfoMapper dataChangeDetailInfoMapper;

    @Override
    public void saveData(String uniqueCode, String data) {
        log.info("SyncSafeDeviceRecInfoSaveExt uniqueCode:[{}], data: [{}]", uniqueCode, data);
        //TransactionStatus transactionStatus = platformTransactionManager.getTransaction(transactionDefinition);
        try {
            if (StringUtils.isBlank(data)) {
                return;
            }
            List<SyncSafeTemperatureHumidityRecordDO> deviceRecordList = JSON.parseArray(data, SyncSafeTemperatureHumidityRecordDO.class);
            if (CollectionUtils.isEmpty(deviceRecordList)) {
                return;
            }
            MuseumBaseInfoDO museumBaseInfoDO = this.museumBaseInfoMapper.selectByPrimaryKey(uniqueCode);
            if (Objects.isNull(museumBaseInfoDO)) {
                return;
            }
            List<SyncSafeTemperatureHumidityDeviceDO> deviceDOList = this.syncSafeTemperatureHumidityDeviceMapper.selectByUniqueCode(uniqueCode);
            if (CollectionUtils.isEmpty(deviceDOList)) {
                return;
            }

            Map<String, SyncSafeTemperatureHumidityDeviceDO> deviceMap = new HashMap<>();
            deviceDOList.forEach(d -> deviceMap.put(d.getDeviceCode(), d));
            AtomicInteger offlineCount = new AtomicInteger();
            AtomicReference<Date> offlineTime = new AtomicReference<>(new Date());
            Set<String> deviceCodeSet = new HashSet<>();
            List<Long> deviceCodeList = new ArrayList<>();
            deviceRecordList.forEach(d -> {
                SyncSafeTemperatureHumidityRecordDO recordDO = BeanCopyUtils.copyByJSON(d, SyncSafeTemperatureHumidityRecordDO.class);
                recordDO.setUniqueCode(uniqueCode);
                this.syncSafeTemperatureHumidityRecordMapper.insertSelective(recordDO);
                if (!deviceMap.containsKey(d.getDeviceCode())) {
                    return;
                }
                boolean saveFlag = false;
                SyncSafeTemperatureHumidityDeviceDO deviceDO = deviceMap.get(d.getDeviceCode());
                SyncSafeDeviceInfoDO deviceInfoDO = new SyncSafeDeviceInfoDO();
                deviceInfoDO.setUniqueCode(uniqueCode);
                deviceInfoDO.setRegionCode(museumBaseInfoDO.getRegionCode());
                deviceInfoDO.setRegionName(deviceDO.getRegionName());
                deviceInfoDO.setDeviceCode(d.getDeviceCode());
                deviceInfoDO.setDeviceAddress(deviceDO.getDeviceAddress());
                deviceInfoDO.setMinTemperature(deviceDO.getMinTemperature());
                deviceInfoDO.setMaxTemperature(deviceDO.getMaxTemperature());
                deviceInfoDO.setMinHumidity(deviceDO.getMinHumidity());
                deviceInfoDO.setMaxHumidity(deviceDO.getMaxHumidity());
                deviceInfoDO.setTemperature(d.getCurrentTemperature());
                deviceInfoDO.setHumidity(d.getCurrentHumidity());
                deviceInfoDO.setReportTime(d.getReportTime());
                deviceInfoDO.setCreator(-1L);
                deviceInfoDO.setModifier(-1L);
                deviceInfoDO.setHumidityExceptionDegree("0");
                deviceInfoDO.setTemperatureExceptionDegree("0");
                DataChangeInfoDO dataChangeInfoDO = null;
                if ("-".equalsIgnoreCase(d.getCurrentHumidity()) && "-".equalsIgnoreCase(d.getCurrentTemperature())) {
                    deviceInfoDO.setDeviceStatus((byte) 0);
                    if (!deviceCodeSet.contains(d.getDeviceCode())) {
                        offlineCount.getAndIncrement();
                    }
                    saveFlag = true;
                    offlineTime.set(d.getReportTime());
                }else if ("故障".equalsIgnoreCase(d.getCurrentHumidity()) || "故障".equalsIgnoreCase(d.getCurrentTemperature())) {
                    deviceInfoDO.setDeviceStatus((byte) 0);
                    if (!deviceCodeSet.contains(d.getDeviceCode())) {
                        offlineCount.getAndIncrement();
                    }
                    saveFlag = true;
                    offlineTime.set(d.getReportTime());
                } else {
                    deviceInfoDO.setDeviceStatus((byte) 1);
                    double maxT = Double.parseDouble(deviceDO.getMaxTemperature());
                    double minT = Double.parseDouble(deviceDO.getMinTemperature());
                    double currT = Double.parseDouble(d.getCurrentTemperature());
                    if (currT > maxT || currT < minT) {
                        dataChangeInfoDO = new DataChangeInfoDO();
                        dataChangeInfoDO.setHappenTime(d.getReportTime());
                        dataChangeInfoDO.setChangeType((byte)3);
                        String desc = museumBaseInfoDO.getMuseumName() + deviceDO.getDeviceAddress();
                        double val = Math.max(minT - currT, currT - maxT);
                        if (val <= 2){
                            deviceInfoDO.setTemperatureExceptionDegree("1");
                            desc += "温度轻度异常";
                        } else if (val > 5) {
                            deviceInfoDO.setTemperatureExceptionDegree("3");
                            desc += "温度重度异常";
                        } else {
                            deviceInfoDO.setTemperatureExceptionDegree("2");
                            desc += "温度中度异常";
                        }
                        dataChangeInfoDO.setChangeDescription(desc);
                    }
                    double maxH = Double.parseDouble(deviceDO.getMaxHumidity());
                    double minH = Double.parseDouble(deviceDO.getMinHumidity());
                    double currH = Double.parseDouble(d.getCurrentHumidity());
                    if (currH > maxH || currH < minH) {
                        dataChangeInfoDO = new DataChangeInfoDO();
                        dataChangeInfoDO.setHappenTime(d.getReportTime());
                        dataChangeInfoDO.setChangeType((byte)4);
                        String desc = museumBaseInfoDO.getMuseumName() + deviceDO.getDeviceAddress();
                        double val = Math.max(minH - currH, currH - maxH);
                        if (val <= 2){
                            deviceInfoDO.setHumidityExceptionDegree("1");
                            desc += "湿度轻度异常";
                        } else if (val > 5) {
                            deviceInfoDO.setHumidityExceptionDegree("3");
                            desc += "湿度重度异常";
                        } else {
                            deviceInfoDO.setHumidityExceptionDegree("2");
                            desc += "湿度中度异常";
                        }
                        dataChangeInfoDO.setChangeDescription(desc);
                    }
                }
                this.syncSafeDeviceInfoMapper.insertSelective(deviceInfoDO);

                // 实时表
                SyncSafeDeviceInfoRealtimeDO syncSafeDeviceInfoRealtimeDO = new SyncSafeDeviceInfoRealtimeDO();
                BeanUtils.copyProperties(deviceInfoDO,syncSafeDeviceInfoRealtimeDO);
                syncSafeDeviceInfoRealtimeDO.setGmtCreate(new Date());
                syncSafeDeviceInfoRealtimeDO.setGmtModified(new Date());
                SyncSafeDeviceInfoRealtimeDO realtimeDO = this.syncSafeDeviceInfoRealtimeMapper.selectByDeviceCode(d.getDeviceCode());
                if(Objects.isNull(realtimeDO)){
                    this.syncSafeDeviceInfoRealtimeMapper.insertSelective(syncSafeDeviceInfoRealtimeDO);
                }else{
                    this.syncSafeDeviceInfoRealtimeMapper.updateByDeviceCode(syncSafeDeviceInfoRealtimeDO);
                }

                if (Objects.nonNull(dataChangeInfoDO)) {
                    saveDataChangeInfo(dataChangeInfoDO, String.valueOf(deviceInfoDO.getId()), museumBaseInfoDO.getId());
                }
                if (saveFlag) {
                    deviceCodeList.add(deviceInfoDO.getId());
                }
            });
            if (offlineCount.get() != 0) {
                DataChangeInfoDO dataChangeInfoDO = new DataChangeInfoDO();
                dataChangeInfoDO.setHappenTime(offlineTime.get());
                dataChangeInfoDO.setChangeType((byte)5);
                dataChangeInfoDO.setChangeDescription(museumBaseInfoDO.getMuseumName() + "有" + offlineCount.get() + "温湿度监测设备离线");
                saveDataChangeInfo(dataChangeInfoDO, deviceCodeList.stream().map(String::valueOf).collect(Collectors.joining(",")), uniqueCode);
            }
            //platformTransactionManager.commit(transactionStatus);
        } catch (Exception e) {
            //platformTransactionManager.rollback(transactionStatus);
            log.error("SyncSafeDeviceRecInfoSaveExt uniqueCode:[{}] saveData: [{}] Exception:", uniqueCode, data, e);
        }
    }

    private void saveDataChangeInfo(DataChangeInfoDO dataChangeInfoDO, String deviceInfoIds, String museumId) {
        this.dataChangeInfoMapper.insertSelective(dataChangeInfoDO);
        DataChangeDetailInfoDO dataChangeDetailInfoDO = new DataChangeDetailInfoDO();
        dataChangeDetailInfoDO.setChangeInfoId(dataChangeInfoDO.getId());
        dataChangeDetailInfoDO.setMuseumId(museumId);
        dataChangeDetailInfoDO.setChangeData(deviceInfoIds);
        this.dataChangeDetailInfoMapper.insertSelective(dataChangeDetailInfoDO);
    }

}
