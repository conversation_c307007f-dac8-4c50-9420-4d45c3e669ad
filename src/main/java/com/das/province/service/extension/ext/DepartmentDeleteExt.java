package com.das.province.service.extension.ext;

import com.das.province.common.exception.CommonErrorCodeEnum;
import com.das.province.common.exception.CommonException;
import com.das.province.common.extension.Extension;
import com.das.province.infr.dataobject.AccUserDO;
import com.das.province.infr.entity.PositionEntity;
import com.das.province.infr.mapper.AccUserMapper;
import com.das.province.infr.repository.PositionRepository;
import com.das.province.service.extension.ClassificationDeleteExtPt;
import com.das.province.service.extension.ClassificationScenario;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

@Component
@Extension(bizId = ClassificationScenario.BIZ_ID, useCase = ClassificationScenario.USE_CASE_DELETE,
        scenario = ClassificationScenario.DEPARTMENT_DELETE_SCENARIO)
public class DepartmentDeleteExt implements ClassificationDeleteExtPt {

    @Resource
    private PositionRepository positionRepository;

    @Resource
    private AccUserMapper accUserMapper;

    @Override
    public boolean deleteHandler(Long companyId, Long userId, List<Long> classificationIds) {
        /*List<PositionEntity> positionEntityList = this.positionRepository.listByDepartmentIds(companyId, classificationIds, (byte) 0);
        if (CollectionUtils.isNotEmpty(positionEntityList)) {
            throw new CommonException(CommonErrorCodeEnum.VALIDATE_ERROR, "该部门下存在职位信息, 请删除后重试");
        }*/
        List<AccUserDO> accUserList = this.accUserMapper.listByDepartmentIds(companyId, classificationIds, (byte) 0);
        if (CollectionUtils.isNotEmpty(accUserList)) {
            throw new CommonException(CommonErrorCodeEnum.VALIDATE_ERROR, "该部门下存在用户信息, 请删除后重试");
        }
        return true;
    }
}
