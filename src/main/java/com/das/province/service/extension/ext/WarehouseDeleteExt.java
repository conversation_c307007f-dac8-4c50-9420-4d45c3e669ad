package com.das.province.service.extension.ext;

import com.das.province.common.extension.Extension;
import com.das.province.service.extension.ClassificationDeleteExtPt;
import com.das.province.service.extension.ClassificationScenario;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@Extension(bizId = ClassificationScenario.BIZ_ID, useCase = ClassificationScenario.USE_CASE_DELETE,
        scenario = ClassificationScenario.WAREHOUSE_DELETE_SCENARIO)
public class WarehouseDeleteExt implements ClassificationDeleteExtPt {
    @Override
    public boolean deleteHandler(Long companyId, Long userId, List<Long> classificationIds) {
        return true;
    }
}
