package com.das.province.service.extension.ext;

import com.alibaba.fastjson.JSON;
import com.das.province.common.extension.Extension;
import com.das.province.common.utils.DateUtils;
import com.das.province.infr.dataobject.SyncUserInfoDO;
import com.das.province.infr.mapper.SyncUserInfoMapper;
import com.das.province.service.extension.ProvinceSyncDataExtPt;
import com.das.province.service.extension.ProvinceSyncDataScenario;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.TransactionStatus;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023/3/21
 */
@Slf4j
@Component
@Extension(bizId = ProvinceSyncDataScenario.BIZ_ID, useCase = ProvinceSyncDataScenario.USE_CASE_SAVE,
        scenario = ProvinceSyncDataScenario.USER_SAVE_SCENARIO)
public class SyncUserSaveExt implements ProvinceSyncDataExtPt {

    @Resource
    private SyncUserInfoMapper syncUserInfoMapper;

    @Autowired
    private PlatformTransactionManager platformTransactionManager;

    @Autowired
    private TransactionDefinition transactionDefinition;

    @Override
    public void saveData(String uniqueCode, String data) {
        TransactionStatus transactionStatus = platformTransactionManager.getTransaction(transactionDefinition);
        try {
            if (StringUtils.isBlank(data)) {
                return;
            }
            List<SyncUserInfoDO> syncUserInfoDOList = JSON.parseArray(data, SyncUserInfoDO.class);
            if (CollectionUtils.isEmpty(syncUserInfoDOList)) {
                return;
            }
            List<SyncUserInfoDO> userInfoList = this.syncUserInfoMapper.listUniqueCode(uniqueCode);
            Map<String, SyncUserInfoDO> userInfoMap = Maps.newHashMap();
            if (CollectionUtils.isNotEmpty(userInfoList)) {
                userInfoList.forEach(k -> userInfoMap.put(k.getPostCode() + "_" + DateUtils.formatDateYYYMMDD(k.getStatisticsDate()), k));
            }
            syncUserInfoDOList.forEach(u -> {
                if ("-1000L".equals(u.getPostCode())) {
                    this.syncUserInfoMapper.deleteByUniqueCode(uniqueCode);
                    return;
                }
                /*SyncUserInfoDO queryUser = this.syncUserInfoMapper
                        .selectUniqueCodeAndPostCodeAndstatisticsDate(uniqueCode,u.getPostCode(), u.getStatisticsDate());*/
                SyncUserInfoDO queryUser = userInfoMap.get(u.getPostCode() + "_" + DateUtils.formatDateYYYMMDD(u.getStatisticsDate()));
                if (Objects.isNull(queryUser)) {
                    u.setUniqueCode(uniqueCode);
                    this.syncUserInfoMapper.insertSelective(u);
                } else {
                    queryUser.setCount(u.getCount());
                    this.syncUserInfoMapper.updateByPrimaryKeySelective(queryUser);
                }
            });
            platformTransactionManager.commit(transactionStatus);
        } catch (Exception e) {
            platformTransactionManager.rollback(transactionStatus);
            log.error("SyncUserSaveExt uniqueCode:[{}] saveData: [{}] Exception:", uniqueCode, data, e);
        }
    }
}
