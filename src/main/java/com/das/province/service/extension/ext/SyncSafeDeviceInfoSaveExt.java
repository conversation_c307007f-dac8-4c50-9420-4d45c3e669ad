package com.das.province.service.extension.ext;

import com.alibaba.fastjson.JSON;
import com.das.province.common.extension.Extension;
import com.das.province.infr.dataobject.SyncSafeTemperatureHumidityDeviceDO;
import com.das.province.infr.mapper.SyncSafeTemperatureHumidityDeviceMapper;
import com.das.province.service.extension.ProvinceSyncDataExtPt;
import com.das.province.service.extension.ProvinceSyncDataScenario;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.TransactionStatus;

import javax.annotation.Resource;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/7/15
 */
@Slf4j
@Component
@Extension(bizId = ProvinceSyncDataScenario.BIZ_ID, useCase = ProvinceSyncDataScenario.USE_CASE_SAVE,
        scenario = ProvinceSyncDataScenario.DEVICE_SAVE_SCENARIO)
public class SyncSafeDeviceInfoSaveExt implements ProvinceSyncDataExtPt {

    @Resource
    private SyncSafeTemperatureHumidityDeviceMapper syncSafeTemperatureHumidityDeviceMapper;

    @Autowired
    private PlatformTransactionManager platformTransactionManager;

    @Autowired
    private TransactionDefinition transactionDefinition;

    @Override
    public void saveData(String uniqueCode, String data) {
        TransactionStatus transactionStatus = platformTransactionManager.getTransaction(transactionDefinition);
        try {
            if (StringUtils.isBlank(data)) {
                return;
            }
            List<SyncSafeTemperatureHumidityDeviceDO> humidityDeviceDOList = JSON.parseArray(data, SyncSafeTemperatureHumidityDeviceDO.class);
            if (CollectionUtils.isEmpty(humidityDeviceDOList)) {
                return;
            }
            List<SyncSafeTemperatureHumidityDeviceDO> dataList = this.syncSafeTemperatureHumidityDeviceMapper.selectByUniqueCode(uniqueCode);
            Set<String> deviceSet = dataList.stream().map(SyncSafeTemperatureHumidityDeviceDO::getDeviceCode).collect(Collectors.toSet());
            humidityDeviceDOList.forEach(u -> {
                if ("-1000L".equals(u.getDeviceCode())) {
                    this.syncSafeTemperatureHumidityDeviceMapper.deleteByUniqueCode(uniqueCode);
                    return;
                }
                u.setUniqueCode(uniqueCode);
                if (deviceSet.contains(u.getDeviceCode())) {
                    this.syncSafeTemperatureHumidityDeviceMapper.updateByDeviceCode(u);
                } else {
                    u.setCreator(-1L);
                    u.setModifier(-1L);
                    this.syncSafeTemperatureHumidityDeviceMapper.insertSelective(u);
                }
            });
            platformTransactionManager.commit(transactionStatus);
        } catch (Exception e) {
            platformTransactionManager.rollback(transactionStatus);
            log.error("SyncSafeDeviceInfoSaveExt uniqueCode:[{}] saveData: [{}] Exception:", uniqueCode, data, e);
        }
    }
}
