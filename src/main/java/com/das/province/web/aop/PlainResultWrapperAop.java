package com.das.province.web.aop;

import com.das.province.common.bo.PlainResult;
import com.das.province.common.exception.CommonErrorCodeEnum;
import com.das.province.common.exception.CommonException;
import com.das.province.common.utils.UUIDUtils;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.slf4j.MDC;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;


@Aspect
@Component
@Order(-20)
@Slf4j
public class PlainResultWrapperAop {

    public static final String TRACE_ID = "TraceId";

    @Around("@within(com.das.province.web.aop.annoation.PlainResultWrapper) || @annotation(com.das.province.web.aop.annoation.PlainResultWrapper) " +
            "|| execution(com.das.province.common.bo.PlainResult com.das.province.web.controller..*.*"
            + "(..))")
    public Object process(ProceedingJoinPoint joinPoint) {
        Object obj;
        String requestId = null;
        try {
            requestId = UUIDUtils.generateUUID(16);
            MDC.put(TRACE_ID, requestId);
            obj = joinPoint.proceed();
            if(obj == null){
                return null;
            }
            PlainResult plainResult = (PlainResult) obj;
            plainResult.setRequestId(requestId);
        } catch (CommonException ex) {
            log.error("PlainResult Exception: ", ex);
            obj = getFailedResult(ex.getCode(), ex.getMessage(), requestId);
        } catch (IllegalArgumentException ex) {
            log.error("PlainResult Exception: ", ex);
            obj = getFailedResult(new CommonException(CommonErrorCodeEnum.VALIDATE_ERROR.getCode(), ex.getMessage()), requestId);
        } catch (Throwable ex) {
            log.error("PlainResult Exception: ", ex);
            obj = getFailedResult(new CommonException(CommonErrorCodeEnum.UNKNOWN_ERROR.getCode(), CommonErrorCodeEnum.UNKNOWN_ERROR.getMessage()), requestId);
        }
        finally {
            MDC.remove(TRACE_ID);
        }
        return obj;
    }

    private PlainResult getFailedResult(CommonException ex, String requestId) {
        return getFailedResult(ex.getCode(), ex.getMessage(), requestId);
    }

    private PlainResult getFailedResult(int code, String message, String requestId) {
        PlainResult<Boolean> result = new PlainResult<>();
        result.setSuccess(false);
        result.setData(null);
        result.setCode(code);
        result.setErrorMessage(message);
        result.setResultMsg(null);
        result.setRequestId(requestId);
        return result;
    }
}
