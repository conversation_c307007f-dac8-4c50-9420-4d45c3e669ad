package com.das.province.web.controller.comprehensive.response;

import lombok.Data;

import java.util.List;

@Data
public class ProvinceVisitorStatisticsVO {

    /**
     * 今日全省观众总量
     */
    private Long todayTotal;

    /**
     * 今日全省线下入馆观众
     */
    private Long todayOffline;

    /**
     * 今日全省线上访问观众
     */
    private Long todayOnline;

    /**
     * 近一年全省观众总量
     */
    private Long yearTotal;

    /**
     * 近一年全省线下入馆观众
     */
    private Long yearOffline;

    /**
     * 近一年全省线上访问观众
     */
    private Long yearOnline;

    /**
     * 近12个月观众统计
     */
    private List<ProvinceMonthVisitorVO> provinceMonthVisitorVOList;

    /**
     * 近一年观众地区占比
     */
    private List<CityVisitorNumVO> cityVisitorNum;

}
