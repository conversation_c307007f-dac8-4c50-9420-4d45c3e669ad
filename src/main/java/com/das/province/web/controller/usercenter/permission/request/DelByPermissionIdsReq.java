package com.das.province.web.controller.usercenter.permission.request;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

@Data
public class DelByPermissionIdsReq implements Serializable {
    private static final long serialVersionUID = 4482407157316892523L;
    @NotNull(message = "功能id不能为空")
    private List<Long> permissionIds;
}
