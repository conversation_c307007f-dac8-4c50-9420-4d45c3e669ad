package com.das.province.web.controller.auth;

import com.das.province.common.bo.PlainResult;
import com.das.province.common.utils.Md5DigesterUtil;
import com.das.province.service.biz.data.ProvinceSyncDataService;
import com.das.province.web.aop.annoation.PlainResultWrapper;
import com.das.province.web.controller.auth.request.SendKeyReq;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 * @date 2023/3/20
 */
@Slf4j
@PlainResultWrapper
@RestController
@RequestMapping("/province/data")
public class ProvinceSyncDataController {

    @Value("${md5.digester.salt}")
    private String salt;

    private static final String X_UNIQUE_CODE_HEADER = "x-unique-code";

    private static final String X_UNIQUE_MUSEUM_HEADER = "x-unique-museum";

    @Resource
    private ProvinceSyncDataService provinceSyncDataService;

    /**
     * 获取sessionKey
     * @param req req
     * @return key
     */
    @PostMapping(value = "/receive")
    public PlainResult<Boolean> receiveData(@RequestBody SendKeyReq req, HttpServletRequest request) {
        if (StringUtils.isBlank(req.getKeyBody())) {
            return PlainResult.success(false, "同步失败");
        }
        String uniqueCode = request.getHeader(X_UNIQUE_CODE_HEADER);
        String md5Code = Md5DigesterUtil.digestHex(req.getKeyBody()+salt, salt);
        if (StringUtils.isBlank(uniqueCode) || !uniqueCode.equals(md5Code)) {
            log.error("ProvinceSyncDataController receiveData uniqueCode: [{}] md5Code: [{}] no the same", uniqueCode, md5Code);
            return PlainResult.success(false, "同步失败");
        }
        String museumBaseId = request.getHeader(X_UNIQUE_MUSEUM_HEADER);
        if (StringUtils.isBlank(museumBaseId)) {
            log.error("ProvinceSyncDataController receiveData museumBaseId: [{}] is not exist", museumBaseId);
            return PlainResult.success(false, "同步失败");
        }
        this.provinceSyncDataService.provinceSyncDataReceive(Long.valueOf(museumBaseId), req.getKeyBody());
        return PlainResult.success(true, "同步成功");
    }
}
