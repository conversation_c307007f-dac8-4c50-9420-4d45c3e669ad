package com.das.province.web.controller.usercenter.permission.response;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class PermissionPageVO implements Serializable {
    private static final long serialVersionUID = -1950205877426230309L;
    private Long permissionId;
    private Long companyId;
    private String permissionName;
    private String permissionCode;
    private String permissionType;
    private Long parentId;
    private String parentName;
    private Byte level;
    private String levelName;
    private Byte sortIndex;
    private Byte functionStatus;
    private String functionStatusDesc;
    private Date gmtCreate;
}