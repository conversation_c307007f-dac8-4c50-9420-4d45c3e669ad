package com.das.province.web.controller.usercenter.user.request;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

@Data
public class BatchEditAccountStatusReq implements Serializable {
    private static final long serialVersionUID = 6228537807384955246L;
    @NotNull(message = "用户ids不能为空")
    private List<Long> userIds;
    @NotNull(message = "账号状态不能为空")
    private Byte accountStatus;
}
