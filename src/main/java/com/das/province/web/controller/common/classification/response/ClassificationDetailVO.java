package com.das.province.web.controller.common.classification.response;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class ClassificationDetailVO implements Serializable {
    private static final long serialVersionUID = -755559550979830828L;
    private Long classificationId;
    private Long companyId;
    private Long bizId;
    private String bizCode;
    private Long bizNumber;
    private Byte bizType;
    private String name;
    private Long parentId;
    private Byte level;
    private Byte sortIndex;
    private String permission;
    private Byte enabledStatus;
    private Byte leafNode;
    private String remark;
    private Long creator;
    private Long modifier;
    private Date gmtCreate;
    private Date gmtModified;
}