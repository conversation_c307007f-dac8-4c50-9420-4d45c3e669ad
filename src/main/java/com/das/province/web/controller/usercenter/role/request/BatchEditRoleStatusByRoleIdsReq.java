package com.das.province.web.controller.usercenter.role.request;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

@Data
public class BatchEditRoleStatusByRoleIdsReq implements Serializable {
    private static final long serialVersionUID = -4413709960191455975L;
    @NotNull(message = "角色ids不能为空")
    private List<Long> roleIds;
    @NotNull(message = "状态不能为空")
    private Byte status;
}
