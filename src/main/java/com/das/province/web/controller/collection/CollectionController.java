package com.das.province.web.controller.collection;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.das.province.common.bo.PlainResult;
import com.das.province.common.bo.SimplePageInfo;
import com.das.province.common.exception.CommonErrorCodeEnum;
import com.das.province.common.exception.CommonException;
import com.das.province.common.utils.DateUtils;
import com.das.province.service.biz.collection.CollectionService;
import com.das.province.service.biz.collection.action.QueryCulturalPageByConditionAction;
import com.das.province.service.biz.collection.dto.*;
import com.das.province.web.aop.annoation.PlainResultWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.ClassPathResource;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.BufferedOutputStream;
import java.io.File;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.Date;
import java.util.List;

/**
 * 数字藏品
 *
 * <AUTHOR>
 * @date 2023/03/28
 */
@Slf4j
@PlainResultWrapper
@RestController
@RequestMapping("/province/collection")
public class CollectionController {

    @Resource
    private CollectionService collectionService;

    /**
     * 分页查询数据藏品
     * @param collectionName         藏品名称
     * @param categoryName         文物类别名称
     * @param museumCode         博物馆唯一编码
     * @param regionCode         区域编码
     * @param levelName          级别名称
     * @param textureName        质地名称
     * @param sourceName         来源名称
     * @param age                年代名称
     * @param completeDegreeName 完残程度名称
     * @param sortBy             排序字段
     * @param pageNum            页数
     * @param pageSize           页大小
     * @return info
     */
    @GetMapping("/digitPageList")
    public PlainResult<SimplePageInfo<CollectionInfoListDTO>> getPageByConditionQuery(@RequestParam(value = "collectionName", required = false) String collectionName,
                                                                                      @RequestParam(value = "categoryName", required = false) String categoryName,
                                                                                      @RequestParam(value = "museumCode", required = false) String museumCode,
                                                                                      @RequestParam(value = "regionCode", required = false) String regionCode,
                                                                                      @RequestParam(value = "levelName", required = false) String levelName,
                                                                                      @RequestParam(value = "textureName", required = false) String textureName,
                                                                                      @RequestParam(value = "sourceName", required = false) String sourceName,
                                                                                      @RequestParam(value = "age", required = false) String age,
                                                                                      @RequestParam(value = "completeDegreeName", required = false) String completeDegreeName,
                                                                                      @RequestParam(value = "sortBy", defaultValue = "gmt_create desc") String sortBy,
                                                                                      @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
                                                                                      @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize) {
        QueryCulturalPageByConditionAction action = new QueryCulturalPageByConditionAction();
        action.setCollectionName(collectionName);
        action.setCategoryName(categoryName);
        action.setMuseumCode(museumCode);
        action.setRegionCode(regionCode);
        action.setLevelName(levelName);
        action.setTextureName(textureName);
        action.setSourceName(sourceName);
        action.setAge(age);
        action.setCompleteDegreeName(completeDegreeName);
        action.setSortBy(sortBy);
        action.setPageNum(pageNum);
        action.setPageSize(pageSize);
        SimplePageInfo<CollectionInfoListDTO> pageInfo = this.collectionService.queryCollectionPageByCondition(action);
        return PlainResult.success(pageInfo, "获取成功");
    }

    /**
     * 导出藏品列表
     * @param collectionName     藏品名称
     * @param categoryName       文物类别名称
     * @param museumCode         博物馆唯一编码
     * @param regionCode         区域编码
     * @param levelName          级别名称
     * @param textureName        质地名称
     * @param sourceName         来源名称
     * @param age                年代名称
     * @param completeDegreeName 完残程度名称
     * @param sortBy             排序字段
     * @param pageNum            页数
     * @param pageSize           页大小
     * @return info
     */
    @GetMapping("/exportCollectionList")
    public void exportCollectionList(@RequestParam(value = "collectionName", required = false) String collectionName,
                                      @RequestParam(value = "categoryName", required = false) String categoryName,
                                      @RequestParam(value = "museumCode", required = false) String museumCode,
                                      @RequestParam(value = "regionCode", required = false) String regionCode,
                                      @RequestParam(value = "levelName", required = false) String levelName,
                                      @RequestParam(value = "textureName", required = false) String textureName,
                                      @RequestParam(value = "sourceName", required = false) String sourceName,
                                      @RequestParam(value = "age", required = false) String age,
                                      @RequestParam(value = "completeDegreeName", required = false) String completeDegreeName,
                                      @RequestParam(value = "sortBy", defaultValue = "gmt_create desc") String sortBy,
                                      @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
                                      @RequestParam(value = "pageSize", defaultValue = "1000000") Integer pageSize,
                                      HttpServletRequest request, HttpServletResponse response) {
        QueryCulturalPageByConditionAction action = new QueryCulturalPageByConditionAction();
        action.setCollectionName(collectionName);
        action.setCategoryName(categoryName);
        action.setMuseumCode(museumCode);
        action.setRegionCode(regionCode);
        action.setLevelName(levelName);
        action.setTextureName(textureName);
        action.setSourceName(sourceName);
        action.setAge(age);
        action.setCompleteDegreeName(completeDegreeName);
        action.setSortBy(sortBy);
        action.setPageNum(pageNum);
        action.setPageSize(1000000);
        SimplePageInfo<CollectionInfoListDTO> pageInfo = this.collectionService.queryCollectionPageByCondition(action);
        this.writerExcelFormTemplate(response,"collection_general.xlsx", "藏品明细报表.xlsx",pageInfo.getList());
    }


    private void writerExcelFormTemplate(HttpServletResponse response,String templateName, String exportName, List dataList) {
        OutputStream out;
        BufferedOutputStream bos;
        try {
            org.springframework.core.io.Resource resource = new ClassPathResource("templates" + File.separator + templateName);
            InputStream inputStream = resource.getInputStream();
            response.setContentType("application/vnd.ms-excel");
            response.setCharacterEncoding("utf-8");
            String fileName = URLEncoder.encode(exportName, "utf-8");
            response.setHeader("Content-disposition", "attachment; filename=" +
                    new String(fileName.getBytes(StandardCharsets.UTF_8), StandardCharsets.ISO_8859_1));
            out = response.getOutputStream();
            bos = new BufferedOutputStream(out);
            ExcelWriter excelWriter = EasyExcel.write(bos).withTemplate(inputStream).build();
            WriteSheet writeSheet = EasyExcel.writerSheet().build();
            excelWriter.fill(dataList, writeSheet);
            excelWriter.finish();
            bos.flush();
        } catch (Exception e) {
            log.error("writerExcelFormTemplate Exception:", e);
            throw new CommonException(CommonErrorCodeEnum.UNKNOWN_ERROR, "系统错误, 导出失败!");
        }
    }


    /**
     * 节假日入馆情况图表
     *
     * @return
     */
    @GetMapping("/holidays")
    public PlainResult<List<HolidayDTO>> holidays(@RequestParam(value = "year") Integer year,
                                                  @RequestParam(value = "museumCode", required = false) String museumCode,
                                                  @RequestParam(value = "regionCode", required = false) String regionCode) {
        List<HolidayDTO> holidayDTOList = this.collectionService.holidayList(year, museumCode, regionCode);
        return PlainResult.success(holidayDTOList, "获取成功");
    }

    /**
     * 观众画像-地域结构
     *
     * @param statisticsWay 0:今日,3:本年,5:自定义,6:昨日
     * @param startTime
     * @param endTime
     * @return
     */
    @GetMapping("/audiencePortraitArea")
    public PlainResult<AudiencePortraitAreaDTO> audiencePortraitArea(@RequestParam(value = "statisticsWay") Integer statisticsWay,
                                                                     @RequestParam(value = "startTime", required = false) String startTime,
                                                                     @RequestParam(value = "endTime", required = false) String endTime,
                                                                     @RequestParam(value = "museumCode", required = false) String museumCode,
                                                                     @RequestParam(value = "regionCode", required = false) String regionCode) {
        // 此处默认只计算本月
        String start = DateUtils.formatDateYYYMMDDHHmmss(DateUtils.getTimesDayMorning());
        String end = DateUtils.formatDateYYYMMDDHHmmss(new Date());
        AudiencePortraitAreaDTO audiencePortraitAreaDTO = this.collectionService.audiencePortraitArea(5, start, end, museumCode, regionCode);
        return PlainResult.success(audiencePortraitAreaDTO, "获取成功");
    }

    /**
     * 观众画像-年龄性别结构
     *
     * @param statisticsWay 0:今日,3:本年,5:自定义,6:昨日
     * @param startTime
     * @param endTime
     * @return
     */
    @GetMapping("/audiencePortraitAge")
    public PlainResult<AudiencePortraitAgeDTO> audiencePortraitAge(@RequestParam(value = "statisticsWay") Integer statisticsWay,
                                                                   @RequestParam(value = "startTime", required = false) String startTime,
                                                                   @RequestParam(value = "endTime", required = false) String endTime,
                                                                   @RequestParam(value = "museumCode", required = false) String museumCode,
                                                                   @RequestParam(value = "regionCode", required = false) String regionCode) {
        // 此处默认只计算本日
        String start = DateUtils.formatDateYYYMMDDHHmmss(DateUtils.getTimesDayMorning());
        String end = DateUtils.formatDateYYYMMDDHHmmss(new Date());
        AudiencePortraitAgeDTO audiencePortraitAgeDTO = this.collectionService.audiencePortraitAge(5, start, end, museumCode, regionCode);
        return PlainResult.success(audiencePortraitAgeDTO, "获取成功");
    }

    /**
     * 观众画像-按省份统计观众人数
     *
     * @param statisticsWay 0:今日,3:本年,5:自定义,6:昨日
     * @param startTime
     * @param endTime
     * @return
     */
    @GetMapping("/statisticsByProvince")
    public PlainResult<List<StatisticsByProvinceDTO>> statisticsByProvince(@RequestParam(value = "statisticsWay") Integer statisticsWay,
                                                                           @RequestParam(value = "startTime", required = false) String startTime,
                                                                           @RequestParam(value = "endTime", required = false) String endTime,
                                                                           @RequestParam(value = "museumCode", required = false) String museumCode,
                                                                           @RequestParam(value = "regionCode", required = false) String regionCode) {
        List<StatisticsByProvinceDTO> statisticsByProvinceDTOList = this.collectionService.statisticsByProvince(statisticsWay, startTime, endTime, museumCode, regionCode);
        return PlainResult.success(statisticsByProvinceDTOList, "获取成功");
    }

    /**
     * 观众入馆情况
     *
     * @param statisticsWay 0:今日,3:本年,5:自定义,6:昨日
     * @param startTime
     * @param endTime
     * @return
     */
    @GetMapping("/visitorList")
    public PlainResult<VisitorStatisticsDTO> visitorList(@RequestParam(value = "statisticsWay") Integer statisticsWay,
                                                         @RequestParam(value = "startTime", required = false) String startTime,
                                                         @RequestParam(value = "endTime", required = false) String endTime,
                                                         @RequestParam(value = "museumCode", required = false) String museumCode,
                                                         @RequestParam(value = "regionCode", required = false) String regionCode) {
        VisitorStatisticsDTO visitorStatisticsDTO = this.collectionService.visitorList(statisticsWay, startTime, endTime, museumCode, regionCode);
        return PlainResult.success(visitorStatisticsDTO, "获取成功");
    }

    /**
     * 观众入馆-按城市排名
     *
     * @param statisticsWay 0:今日,3:本年,5:自定义,6:昨日
     * @param startTime
     * @param endTime
     * @return
     */
    @GetMapping("/visitorCityRank")
    public PlainResult<List<VisitorCityRankDTO>> visitorCityRank(@RequestParam(value = "statisticsWay") Integer statisticsWay,
                                                         @RequestParam(value = "startTime", required = false) String startTime,
                                                         @RequestParam(value = "endTime", required = false) String endTime) {
        List<VisitorCityRankDTO> visitorCityRankDTOList = this.collectionService.visitorCityRank(statisticsWay, startTime, endTime);
        return PlainResult.success(visitorCityRankDTOList, "获取成功");
    }

    /**
     * 观众入馆-按博物馆排名
     *
     * @param statisticsWay 0:今日,3:本年,5:自定义,6:昨日
     * @param startTime   自定义开始时间
     * @param endTime     自定义结束时间
     * @param regionCode  行政区划编码
     * @param keyword     博物馆名称关键字
     * @param orderBy     0 降序 1 升序
     * @return
     */
    @GetMapping("/visitorMuseumRank")
    public PlainResult<List<VisitorMuseumRankDTO>> visitorMuseumRank(@RequestParam(value = "statisticsWay") Integer statisticsWay,
                                                                     @RequestParam(value = "startTime", required = false) String startTime,
                                                                     @RequestParam(value = "endTime", required = false) String endTime,
                                                                     @RequestParam(value = "regionCode", required = false) String regionCode,
                                                                     @RequestParam(value = "keyword", required = false) String keyword,
                                                                     @RequestParam(value = "museumCode", required = false) String museumCode,
                                                                     @RequestParam(value = "orderBy") Byte orderBy) {
        List<VisitorMuseumRankDTO> visitorMuseumRankDTOList = this.collectionService.visitorMuseumRank(statisticsWay, startTime, endTime, regionCode, keyword, museumCode, orderBy);
        return PlainResult.success(visitorMuseumRankDTOList, "获取成功");
    }

    /**
     * 时间段入馆情况图表
     *
     * @param statisticsWay 0:今日,3:本年,5:自定义,6:昨日
     * @param startTime
     * @param endTime
     * @return
     */
    @GetMapping("/timePeriodList")
    public PlainResult<List<TimePeriodStatisticsDTO>> timePeriodList(@RequestParam(value = "statisticsWay") Integer statisticsWay,
                                                               @RequestParam(value = "startTime", required = false) String startTime,
                                                               @RequestParam(value = "endTime", required = false) String endTime,
                                                               @RequestParam(value = "museumCode", required = false) String museumCode,
                                                               @RequestParam(value = "regionCode", required = false) String regionCode) {
        List<TimePeriodStatisticsDTO> timePeriodStatisticsDTOList = this.collectionService.timePeriodStatistics(statisticsWay, startTime, endTime, museumCode, regionCode);
        return PlainResult.success(timePeriodStatisticsDTOList, "获取成功");
    }


    /**
     * 观众入馆-数据趋势
     *
     * @param statisticsWay 1 近一周 2 近一月 3 近一年 4 自定义
     * @param startTime   自定义开始时间
     * @param endTime     自定义结束时间
     * @param regionCode  行政区划编码
     * @param museumCode  博物馆编码
     * @return
     */
    @GetMapping("/visitorTendency")
    public PlainResult<List<VisitorTendencyDTO>> visitorTendency(@RequestParam(value = "statisticsWay") Integer statisticsWay,
                                                                 @RequestParam(value = "startTime", required = false) String startTime,
                                                                 @RequestParam(value = "endTime", required = false) String endTime,
                                                                 @RequestParam(value = "regionCode", required = false) String regionCode,
                                                                 @RequestParam(value = "museumCode", required = false) String museumCode) {
        List<VisitorTendencyDTO> visitorTendencyDTOList = this.collectionService.visitorTendency(statisticsWay, startTime, endTime, regionCode, museumCode);
        return PlainResult.success(visitorTendencyDTOList, "获取成功");
    }

    /**
     * 观众接待场馆分布
     *
     * @param statisticsWay 0:今日,3:本年,5:自定义,6:昨日
     * @param startTime   自定义开始时间
     * @param endTime     自定义结束时间
     * @param regionCode  行政区划编码
     * @param museumCode  博物馆编码
     * @param sortField   排序字段 1接待观众 2接待观众全市占比 3接待观众全省占比 4青少年观众占比 5本地观众占比 6国内异地观众占比 7境外观众占比 8男性观众占比 9女性观众占比
     * @param orderBy     0 正序 1 反序
     * @return
     */
    @GetMapping("/visitorVenueDistribute")
    public PlainResult<SimplePageInfo<VisitorVenueDistributeDTO>> visitorVenueDistribute(@RequestParam(value = "statisticsWay") Integer statisticsWay,
                                                                                         @RequestParam(value = "startTime", required = false) String startTime,
                                                                                         @RequestParam(value = "endTime", required = false) String endTime,
                                                                                         @RequestParam(value = "regionCode", required = false) String regionCode,
                                                                                         @RequestParam(value = "museumCode", required = false) String museumCode,
                                                                                         @RequestParam(value = "sortField") Integer sortField,
                                                                                         @RequestParam(value = "orderBy") Integer orderBy,
                                                                                         @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
                                                                                         @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize) {
        SimplePageInfo<VisitorVenueDistributeDTO> visitorVenueDistributeDTOList = this.collectionService.visitorVenueDistribute(statisticsWay, startTime,
                endTime, regionCode, museumCode, sortField, orderBy, pageNum, pageSize);
        return PlainResult.success(visitorVenueDistributeDTOList, "获取成功");
    }

    /**
     * 导出观众接待场馆分布列表
     *
     * @param statisticsWay 0:今日,3:本年,5:自定义,6:昨日
     * @param startTime   自定义开始时间
     * @param endTime     自定义结束时间
     * @param regionCode  行政区划编码
     * @param museumCode  博物馆编码
     * @param sortField   排序字段 1接待观众 2接待观众全市占比 3接待观众全省占比 4青少年观众占比 5本地观众占比 6国内异地观众占比 7境外观众占比 8男性观众占比 9女性观众占比
     * @param orderBy     0 正序 1 反序
     * @return
     */
    @GetMapping("/exportVisitorDistribute")
    public void exportVisitorDistribute(@RequestParam(value = "statisticsWay") Integer statisticsWay,
                                        @RequestParam(value = "startTime", required = false) String startTime,
                                        @RequestParam(value = "endTime", required = false) String endTime,
                                        @RequestParam(value = "regionCode", required = false) String regionCode,
                                        @RequestParam(value = "museumCode", required = false) String museumCode,
                                        @RequestParam(value = "sortField") Integer sortField,
                                        @RequestParam(value = "orderBy") Integer orderBy,
                                        @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
                                        @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize,
                                        HttpServletRequest request, HttpServletResponse response) {
        SimplePageInfo<VisitorVenueDistributeDTO> visitorVenueDistributeDTOList = this.collectionService.visitorVenueDistribute(statisticsWay, startTime,
                endTime, regionCode, museumCode, sortField, orderBy, pageNum, 1000);
        this.writerExcelFormTemplate(response,"visitor_museum.xlsx", "观众接待场馆分布.xlsx", visitorVenueDistributeDTOList.getList());
    }
}
