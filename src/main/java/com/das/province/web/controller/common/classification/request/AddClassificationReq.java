package com.das.province.web.controller.common.classification.request;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
public class AddClassificationReq implements Serializable {
    private static final long serialVersionUID = 2861805886714080029L;
    private Long bizId;
    private String bizCode;
    private Long bizNumber;
    @NotNull(message = "分类类型不能为空")
    private Byte bizType;
    @NotNull(message = "分类名称不能为空")
    private String name;
    private Long parentId;
    @NotNull(message = "分类层级不能为空")
    private Byte level;
    @NotNull(message = "分类排序不能为空")
    private Integer sortIndex;
    private String permission;
    private Byte enabledStatus;
    private Byte leafNode;
    private String remark;
}
