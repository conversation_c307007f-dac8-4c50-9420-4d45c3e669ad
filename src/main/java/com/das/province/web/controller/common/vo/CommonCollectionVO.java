package com.das.province.web.controller.common.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
public class CommonCollectionVO implements Serializable {
    private static final long serialVersionUID = 3808484095861441363L;
    private Long collectionId;
    private String registerNo;
    private Long classify;
    private String classifyName;
    private String collectionName;
    private Integer actualCount;
    private Long countUnit;
    private String countUnitName;
    private Integer collectionCount;
    private String era;
    private Long age;
    private String ageName;
    private Long textureCategory;
    private String textureCategoryName;
    private String textureName;
    private String size;
    private Long sizeUnit;
    private String sizeUnitName;
    private Long completeDegree;
    private String completeDegreeName;
    private String completeInfo;
    private Long initialLevel;
    private String initialLevelName;
    private String amount;
    private Long collectType;
    private Long collectTypeName;
    private Date collectDate;
    private String holder;
    private String payVoucherNo;
    private String remark;
    private String appendageDesc;
    private String sourceInfo;
    private String introduce;
    private String whereabouts;
    private String inputMode;
    private String inputType;
    private String collectionStatus;
    private String processStatus;
    private Date inMuseumDate;
    private String inMuseumStatus;
    private Date inTibetanDate;
    private String appraiserName;
    private Long warehouseId;
    private String warehouseStatus;
    private Date inWarehouseDate;
    private Date outWarehouseDate;
    private String documentId;
    private List<CommonDocumentVO> commonDocumentList;
}
