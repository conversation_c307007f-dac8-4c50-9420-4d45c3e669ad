package com.das.province.web.controller.config;

import com.das.province.common.builder.HttpClientCustomizeBuilder;
import com.das.province.common.http.HttpConfig;
import com.das.province.common.http.HttpMethods;
import com.das.province.common.http.HttpResult;
import com.das.province.common.utils.HttpClientUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.Header;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

/**
 * 数字藏品
 *
 * <AUTHOR>
 * @date 2023/03/28
 */
@Slf4j
@RestController
@RequestMapping("/province/proxy")
public class ProxyController {

    @Resource(name = "provinceHttpClientCustomizeBuilder")
    private HttpClientCustomizeBuilder httpClientCustomizeBuilder;

    /**
     * 代理url
     * @return
     */
    @GetMapping("/proxyUrl")
    public Object proxyUrl(@RequestParam(value = "proxyUrl") String proxyUrl, HttpServletResponse response) {
        try {
            HttpConfig config = HttpConfig.custom()
                    .timeout(3000)
                    //.headers(HttpHeader.custom().build())
                    .url(proxyUrl)
                    //.encoding("utf-8")
                    .method(HttpMethods.GET)
                    .client(httpClientCustomizeBuilder.build());
            HttpResult result = HttpClientUtil.sendAndGetResp(config);
            Header[] respHeaders = result.getRespHeaders();
            for (Header h : respHeaders) {
                response.addHeader(h.getName(), h.getValue());
            }
            return result.getResult();
        } catch (Exception e) {
            log.error("ProxyController syncUserInfo error: ", e);
        }
        return null;
    }

    public static void main(String[] args) {
        String s ="\"https://province-digital-museum-dev.daspatial.com/provinceDigitalMuseumApi/province/proxy/proxyUrl?proxyUrl=%22http://digitalmuseum-display-dev.daspatial.com/wbResource/model/93828722b44b47fdae3f67eebf6f38ea/gltf/model.get3d?1680603756267%22\"";
        System.out.println(s.substring(1, s.length()-1));
    }
}
