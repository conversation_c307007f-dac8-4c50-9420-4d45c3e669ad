package com.das.province.web.controller.comprehensive.response;

import lombok.Data;

import java.util.List;

@Data
public class MuseumGeneralVO {

    /**
     * 博物馆总数量
     */
    private Integer totalNum;

    /**
     * 入驻博物馆总数量
     */
    private Integer settleInNum;

    /**
     * 平台藏品总数
     */
    private Integer collectionNum;

    /**
     * 年报藏品总数
     */
    private Integer reportCollectionNum;

    /**
     * 视频监控个数
     */
    private Integer cameraNum;

    /**
     * 环境监测个数
     */
    private Integer monitorNum;

    /**
     * 今日观众总量
     */
    private Integer todayTotal;

    /**
     * 本年观众总量
     */
    private Integer yearTotal;

    /**
     * 今日开馆数量
     */
    private Integer openMuseumNum;

//    /**
//     * 在编人员总数量
//     */
//    private Integer underPreparationNum;
//
//    /**
//     * 编外人员总数量
//     */
//    private Integer notPreparationNum;
//
//    /**
//     * 分类统计列表
//     */
//    private List<MuseumClassifyVO> museumClassifyList;
}
