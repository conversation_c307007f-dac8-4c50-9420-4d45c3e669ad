package com.das.province.web.controller.comprehensive.response;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023/7/19
 */
@Data
public class ChangeOfflineVO implements Serializable {

    private static final long serialVersionUID = -1497119470162738214L;

    /**
     * 公司名称
     */
    private String museumName;

    /**
     * 公司编码
     */
    private String uniqueCode;

    /**
     * 所在区域(1展厅,2库房,3其他)
     */
    private Byte regionName;

    /**
     * 设备编码
     */
    private String deviceCode;

    /**
     * 设备位置
     */
    private String deviceAddress;
}
