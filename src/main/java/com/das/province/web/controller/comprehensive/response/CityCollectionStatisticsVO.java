package com.das.province.web.controller.comprehensive.response;

import lombok.Data;

@Data
public class CityCollectionStatisticsVO {

    /**
     * 行政区划编码
     */
    private String regionCode;

    /**
     * 场馆数
     */
    private Integer museumNum;

    /**
     * 藏品总数
     */
    private Long totalCollectionNum;

    /**
     * 定级藏品
     */
    private Long  valuableNum;

    /**
     * 未定级藏品
     */
    private Long exhibitionNum;

    /**
     * 近一年观众总量
     */
    private Long visitorNum;

    /**
     * 监控个数
     */
    private Integer mediaNum;
}
