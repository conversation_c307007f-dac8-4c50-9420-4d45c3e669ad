package com.das.province.web.controller.usercenter.user.request;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

@Data
public class BatchAssignRoleByUserIdsReq implements Serializable {
    private static final long serialVersionUID = -8069291553064495385L;
    @NotNull(message = "用户信息不能为空")
    private List<Long> userIds;
    @NotNull(message = "角色信息不能为空")
    private List<Long> roleIds;
}
