package com.das.province.web.controller.museum;

import com.das.province.common.bo.PlainResult;
import com.das.province.common.bo.SimplePageInfo;
import com.das.province.service.biz.museum.MuseumManageService;
import com.das.province.service.biz.museum.action.AddMuseumAction;
import com.das.province.service.biz.museum.action.EditMuseumAction;
import com.das.province.service.biz.museum.action.QueryMuseumPageAction;
import com.das.province.web.aop.annoation.PlainResultWrapper;
import com.das.province.web.controller.bo.LoginUserInfoBO;
import com.das.province.web.controller.museum.request.AddMuseumReq;
import com.das.province.web.controller.museum.request.EditMuseumReq;
import com.das.province.web.controller.museum.response.MuseumPageVO;
import com.das.province.web.controller.utils.LoginUserInfoUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import javax.validation.Valid;

/**
 * 博物馆管理
 */

@Slf4j
@PlainResultWrapper
@RestController
@RequestMapping("/museum")
public class MuseumManageController {

    @Resource
    private MuseumManageService museumManageService;

    /**
     *
     * @param museumName 博物馆名称
     * @param regionCode 行政区划编码
     * @param levelId    等级id
     * @param natureId   性质id
     * @param sortBy
     * @param pageNum
     * @param pageSize
     * @param request
     * @param session
     * @return
     */
    @GetMapping("/pageList")
    public PlainResult<SimplePageInfo<MuseumPageVO>> getPageByConditionQuery(@RequestParam(value = "museumName", required = false) String museumName,
                                                                             @RequestParam(value = "museumId", required = false) String museumId,
                                                                             @RequestParam(value = "settleIn", required = false) Byte settleIn,
                                                                             @RequestParam(value = "regionCode", required = false) String regionCode,
                                                                             @RequestParam(value = "levelId", required = false) Long levelId,
                                                                             @RequestParam(value = "natureId", required = false) Long natureId,
                                                                             @RequestParam(value = "typeId", required = false) Long typeId,
                                                                             @RequestParam(value = "sortBy", defaultValue = "sdx aes") String sortBy,
                                                                             @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
                                                                             @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize,
                                                                             HttpServletRequest request, HttpSession session) {
        LoginUserInfoBO userBO = LoginUserInfoUtils.getLoginUserInfo();
        QueryMuseumPageAction action = new QueryMuseumPageAction();
        action.setCompanyId(userBO.getCompanyId());
        action.setMuseumName(museumName);
        action.setRegionCode(regionCode);
        action.setMuseumId(museumId);
        action.setSettleIn(settleIn);
        action.setLevelId(levelId);
        action.setNatureId(natureId);
        action.setTypeId(typeId);
        action.setSortBy("sdx asc");
        action.setPageNum(pageNum);
        action.setPageSize(pageSize);
        SimplePageInfo<MuseumPageVO> pageInfo = this.museumManageService.queryPageByCondition(action);
        return PlainResult.success(pageInfo, "获取成功");
    }

    @PostMapping("/add")
    public PlainResult<String> addMuseum(@RequestBody @Valid AddMuseumReq addMuseumReq,
                                     HttpServletRequest request, HttpSession session) {
        LoginUserInfoBO userBO = LoginUserInfoUtils.getLoginUserInfo();
        AddMuseumAction addMuseumAction = new AddMuseumAction();
        BeanUtils.copyProperties(addMuseumReq,addMuseumAction);
        addMuseumAction.setUserId(userBO.getUserId());
        String museumId = this.museumManageService.addMuseum(addMuseumAction);
        return PlainResult.success(museumId, "新增成功");
    }

    @PostMapping("/edit")
    public PlainResult<String> editMuseum(@RequestBody @Valid EditMuseumReq editMuseumReq,
                                         HttpServletRequest request, HttpSession session) {
        LoginUserInfoBO userBO = LoginUserInfoUtils.getLoginUserInfo();
        EditMuseumAction editMuseumAction = new EditMuseumAction();
        BeanUtils.copyProperties(editMuseumReq,editMuseumAction);
        editMuseumAction.setUserId(userBO.getUserId());
        this.museumManageService.editMuseum(editMuseumAction);
        return PlainResult.success(editMuseumReq.getMuseumId(), "编辑成功");
    }

    @PostMapping("/del")
    public PlainResult<String> delMuseum(@RequestParam(value = "museumId") String museumId,
                                         HttpServletRequest request, HttpSession session) {

        this.museumManageService.delMuseumById(museumId);
        return PlainResult.success(museumId, "删除成功");
    }

}
