package com.das.province.web.controller.museum.request;

import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
public class AddMuseumReq {

    /**
     * 博物馆名称
     */
    @NotNull(message = "博物馆名称不能为空")
    private String museumName;

    /**
     * 所属地区编码
     */
    @NotNull(message = "所属地区编码不能为空")
    private String regionCode;

    /**
     * 所属省份名称
     */
    @NotNull(message = "所属省份名称不能为空")
    private String provinceName;

    /**
     * 所属城市名称
     */
    @NotNull(message = "所属城市名称不能为空")
    private String cityName;

    /**
     * 所属区县名称
     */
    @NotNull(message = "所属区县名称不能为空")
    private String countyName;

    /**
     * 等级id
     */
    @NotNull(message = "等级id不能为空")
    private Long levelId;

    /**
     * 博物馆性质id
     */
    @NotNull(message = "博物馆性质id不能为空")
    private Long natureId;

    /**
     * 博物馆类型id
     */
    @NotNull(message = "博物馆类型id不能为空")
    private Long typeId;

    /**
     * 藏品年报数据
     */
    @NotNull(message = "藏品年报数据不能为空")
    private Long reportCollectionNum;

    /**
     * 博物馆经度
     */
    @NotNull(message = "博物馆经度不能为空")
    private String lng;

    /**
     * 博物馆纬度
     */
    @NotNull(message = "博物馆纬度不能为空")
    private String lat;

    /**
     * 官方平台地址
     */
    private String platformAddress;

    /**
     * 馆长姓名
     */
    private String curatorName;

    /**
     * 馆长电话
     */
    private String curatorPhone;

    /**
     * 消防负责人姓名
     */
    private String firemenName;

    /**
     * 消防负责人电话
     */
    private String firemenPhone;

    /**
     * 安防负责人姓名
     */
    private String securityName;

    /**
     * 安防负责人电话
     */
    private String securityPhone;

    /**
     * 藏品负责人姓名
     */
    private String collectionName;

    /**
     * 藏品负责人电话
     */
    private String collectionPhone;

    /**
     * 巡查人员姓名
     */
    private String patrolName;

    /**
     * 巡查人员电话
     */
    private String patrolPhone;

    /**
     * 是否已入驻省平台 0否 1是
     */
    @NotNull(message = "是否已入驻省平台不能为空")
    private Byte settleIn;

    @NotNull(message = "博物馆排序")
    private Integer sdx;
}
