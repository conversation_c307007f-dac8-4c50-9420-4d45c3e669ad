package com.das.province.web.controller.usercenter.user;

import com.das.province.service.biz.common.dto.UserClassificationDTO;
import com.das.province.service.biz.user.UserService;
import com.das.province.service.biz.user.action.*;
import com.das.province.service.biz.user.action.AddUserAction;
import com.das.province.service.biz.user.action.DelByUserIdAction;
import com.das.province.service.biz.user.action.EditUserByUserIdAction;
import com.das.province.service.biz.user.action.QueryPageByConditionAction;
import com.das.province.service.biz.user.dto.*;
import com.das.province.service.enums.AccountStatusEnum;
import com.das.province.service.enums.LoginClientTypeEnum;
import com.das.province.service.enums.PostCodeEnum;
import com.das.province.common.bo.PlainResult;
import com.das.province.common.bo.SimplePageInfo;
import com.das.province.common.exception.CommonErrorCodeEnum;
import com.das.province.common.exception.CommonException;
import com.das.province.common.utils.BeanCopyUtils;
import com.das.province.web.aop.annoation.PlainResultWrapper;
import com.das.province.web.controller.bo.LoginUserInfoBO;
import com.das.province.web.controller.usercenter.user.request.*;
import com.das.province.web.controller.usercenter.user.response.UserRoleVO;
import com.das.province.web.controller.utils.LoginUserInfoUtils;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import javax.validation.Valid;
import java.util.List;
import java.util.Objects;

@Slf4j
@PlainResultWrapper
@RestController
@RequestMapping("/province/setting/user")
public class UserController {

    @Resource
    private UserService userService;

    @PostMapping("/add")
    public PlainResult<Long> addUser(@RequestBody AddUserReq addUserReq) {
        LoginUserInfoBO userBO = LoginUserInfoUtils.getLoginUserInfo();
        AddUserAction action = new AddUserAction();
        action.setCompanyId(userBO.getCompanyId());
        action.setUserName(addUserReq.getUserName());
        action.setRealName(addUserReq.getUserName());
        action.setLoginAccount(addUserReq.getLoginAccount());
        action.setPassword(addUserReq.getPassword());
        action.setPhone(addUserReq.getPhone());
        action.setDepartmentId(addUserReq.getDepartmentId());
        action.setPositionId(addUserReq.getPositionId());
        if (Objects.nonNull(addUserReq.getPostCode())) {
            action.setPostCode(PostCodeEnum.fromCode(addUserReq.getPostCode()));
        }
        action.setRoleIds(addUserReq.getRoleIds());
        action.setPositionName(addUserReq.getPositionName());
        action.setLeaderId(addUserReq.getLeaderId());
        action.setCreator(userBO.getUserId());
        action.setModifier(userBO.getUserId());
        Long retUserId = this.userService.addUser(action);
        return PlainResult.success(retUserId, "新增成功");
    }

    @PostMapping("/edit")
    public PlainResult<Long> editUserById(@RequestBody EditUserReq editUserReq) {
        LoginUserInfoBO userBO = LoginUserInfoUtils.getLoginUserInfo();
        EditUserByUserIdAction action = new EditUserByUserIdAction();
        action.setCompanyId(userBO.getCompanyId());
        action.setUserId(editUserReq.getUserId());
        action.setUserName(editUserReq.getUserName());
        action.setRealName(editUserReq.getUserName());
        action.setPassword(editUserReq.getPassword());
        action.setPhone(editUserReq.getPhone());
        action.setDepartmentId(editUserReq.getDepartmentId());
        action.setPositionId(editUserReq.getPositionId());
        if (Objects.nonNull(editUserReq.getPostCode())) {
            action.setPostCode(PostCodeEnum.fromCode(editUserReq.getPostCode()));
        }
        action.setRoleIds(editUserReq.getRoleIds());
        action.setPositionName(editUserReq.getPositionName());
        action.setLeaderId(editUserReq.getLeaderId());
        action.setModifier(userBO.getUserId());
        this.userService.editUserByUserId(action);
        return PlainResult.success(editUserReq.getUserId(), "编辑成功");
    }

    /**
     * 修改密码
     * @param req
     * @return
     */
    @PostMapping("/updatePassword")
    public PlainResult<Long> updatePassword(@RequestBody @Valid UpdatePasswordReq req) {
        LoginUserInfoBO userBO = LoginUserInfoUtils.getLoginUserInfo();

        this.userService.updatePassword(userBO.getCompanyId(),userBO.getUserId(),req.getOldPassword(),
                req.getNewPassword(),req.getConfirmNewPassword());
        return PlainResult.success(userBO.getUserId(), "密码修改成功");
    }

    /**
     * 修改头像
     * @param req
     * @return
     */
    @PostMapping("/updateAvatar")
    public PlainResult<Long> updateAvatar(@RequestBody @Valid UpdateAvatarReq req) {
        LoginUserInfoBO userBO = LoginUserInfoUtils.getLoginUserInfo();
        this.userService.updateAvatar(userBO.getCompanyId(),userBO.getUserId(),req.getDocumentId());
        return PlainResult.success(userBO.getUserId(), "修改头像成功");
    }

    @PostMapping("/del")
    public PlainResult<Boolean> delByUserId(@RequestParam(value = "userId") Long userId) {
        LoginUserInfoBO userBO = LoginUserInfoUtils.getLoginUserInfo();
        DelByUserIdAction action = new DelByUserIdAction();
        action.setUserId(userId);
        action.setCompanyId(userBO.getCompanyId());
        action.setModifier(userBO.getUserId());
        this.userService.delByUserId(action);
        return PlainResult.success(true, "删除成功");
    }

    @PostMapping("/batchDel")
    public PlainResult<Boolean> delByUserIds(@RequestBody DelByUserIdsReq req) {
        LoginUserInfoBO userBO = LoginUserInfoUtils.getLoginUserInfo();
        BatchDelByUserIdsAction action = new BatchDelByUserIdsAction();
        action.setUserIds(req.getUserIds());
        action.setCompanyId(userBO.getCompanyId());
        action.setModifier(userBO.getUserId());
        this.userService.batchDelByUserIds(action);
        return PlainResult.success(true, "删除成功");
    }

    @PostMapping("/editAccountStatus")
    public PlainResult<Boolean> editAccountStatusByUserId(@RequestBody EditAccountStatusByUserIdReq req) {
        LoginUserInfoBO userBO = LoginUserInfoUtils.getLoginUserInfo();
        if (AccountStatusEnum.启用.getCode() == req.getAccountStatus().intValue()) {
            this.userService.enableLoginAccount(userBO.getCompanyId(), req.getUserId(), userBO.getUserId());
        } else if (AccountStatusEnum.禁用.getCode() == req.getAccountStatus().intValue()) {
            this.userService.disabledLoginAccount(userBO.getCompanyId(), req.getUserId(), userBO.getUserId());
        } else {
            throw new CommonException(CommonErrorCodeEnum.VALIDATE_ERROR, "不存在用户状态操作类型");
        }
        return PlainResult.success(true, "修改成功");
    }

    @PostMapping("/batchEditAccountStatus")
    public PlainResult<Boolean> batchEditAccountStatusByUserId(@RequestBody BatchEditAccountStatusReq req) {
        LoginUserInfoBO userBO = LoginUserInfoUtils.getLoginUserInfo();
        if (AccountStatusEnum.启用.getCode() == req.getAccountStatus().intValue()) {
            this.userService.batchEnableLoginAccount(userBO.getCompanyId(), req.getUserIds(), userBO.getUserId());
        } else if (AccountStatusEnum.禁用.getCode() == req.getAccountStatus().intValue()) {
            this.userService.batchDisabledLoginAccount(userBO.getCompanyId(), req.getUserIds(), userBO.getUserId());
        } else {
            throw new CommonException(CommonErrorCodeEnum.VALIDATE_ERROR, "不存在用户状态操作类型");
        }
        return PlainResult.success(true, "修改成功");
    }

    @GetMapping("/pageList")
    public PlainResult<SimplePageInfo<UserPageDTO>> getPageByConditionQuery(@RequestParam(value = "departmentId", required = false) Long departmentId,
                                                                            @RequestParam(value = "queryContent", required = false) String queryContent,
                                                                            @RequestParam(value = "accountStatus", required = false) Byte accountStatus,
                                                                            @RequestParam(value = "sortBy", defaultValue = "gmt_create desc") String sortBy,
                                                                            @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
                                                                            @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize) {
        LoginUserInfoBO userBO = LoginUserInfoUtils.getLoginUserInfo();
        QueryPageByConditionAction action = new QueryPageByConditionAction();
        action.setCompanyId(userBO.getCompanyId());
        action.setDepartmentId(departmentId);
        action.setQueryContent(queryContent);
        action.setAccountStatus(accountStatus);
        action.setSortBy(sortBy);
        action.setPageNum(pageNum);
        action.setPageSize(pageSize);
        SimplePageInfo<UserPageDTO> pageInfo = this.userService.queryPageByCondition(action);
        return PlainResult.success(pageInfo, "获取成功");
    }

    @GetMapping("/userList")
    public PlainResult<List<UserListDTO>> queryUserList(@RequestParam(value = "userName", required = false) String userName) {
        LoginUserInfoBO userBO = LoginUserInfoUtils.getLoginUserInfo();
        List<UserListDTO> userList = this.userService.queryUserList(userBO.getCompanyId(),userName);
        return PlainResult.success(userList, "获取成功");
    }

    @GetMapping("/info")
    public PlainResult<UserDTO> getDetailByUserId(@RequestParam(value = "userId") Long userId) {
        LoginUserInfoBO userBO = LoginUserInfoUtils.getLoginUserInfo();
        UserDTO userDTO = this.userService.queryByLoginAccount(userBO.getCompanyId(), userId);
        return PlainResult.success(userDTO, "获取成功");
    }

    @GetMapping("/loginUserInfo")
    public PlainResult<UserInfoDTO> getDetailByUserId() {
        LoginUserInfoBO userBO = LoginUserInfoUtils.getLoginUserInfo();
        UserInfoDTO userInfoDTO = this.userService.queryLoginUserInfo(userBO.getCompanyId(), userBO.getUserId());
        return PlainResult.success(userInfoDTO, "获取成功");
    }

    @GetMapping("/loginAccountVerify")
    public PlainResult<Boolean> loginAccountVerify(@RequestParam(value = "loginAccount") String loginAccount) {
        LoginUserInfoBO userBO = LoginUserInfoUtils.getLoginUserInfo();
        Boolean verifyResult = this.userService.verifyByLoginAccount(userBO.getCompanyId(), loginAccount, LoginClientTypeEnum.PROVINCEPC);
        return PlainResult.success(verifyResult, "验证结果");
    }

    @GetMapping("/checkUsersRole")
    public PlainResult<Boolean> checkUsersRole(@RequestParam(value = "userIds") List<Long> userIds) {
        LoginUserInfoBO userBO = LoginUserInfoUtils.getLoginUserInfo();
        Boolean checkResult = this.userService.checkUsersRole(userBO.getCompanyId(), userIds);
        return PlainResult.success(checkResult, "验证结果");
    }

    @GetMapping("/unassignedRole")
    public PlainResult<List<UserRoleVO>> unassignedRole(@RequestParam(value = "copyUserId", required = false) Long copyUserId,
                                                        @RequestParam(value = "userId") Long userId) {
        LoginUserInfoBO userBO = LoginUserInfoUtils.getLoginUserInfo();
        List<RoleDTO> roleDTOList = this.userService.queryUserNoHaveRoleList(userBO.getCompanyId(),copyUserId,userId);

        if (CollectionUtils.isEmpty(roleDTOList)) {
            return PlainResult.success(Lists.newArrayList(), "获取成功");
        }
        List<UserRoleVO> userRoleVOList = BeanCopyUtils.copyArrayByJSON(roleDTOList, UserRoleVO.class);
        return PlainResult.success(userRoleVOList, "获取成功");
    }

    @GetMapping("/assignedRole")
    public PlainResult<List<UserRoleVO>> assignedRole(@RequestParam(value = "userId", required = false) Long userId) {
        LoginUserInfoBO userBO = LoginUserInfoUtils.getLoginUserInfo();
        List<RoleDTO> roleDTOList = this.userService.queryUserHaveRoleList(userBO.getCompanyId(),userId);
        if (CollectionUtils.isEmpty(roleDTOList)) {
            return PlainResult.success(Lists.newArrayList(), "获取成功");
        }
        List<UserRoleVO> userRoleVOList = BeanCopyUtils.copyArrayByJSON(roleDTOList, UserRoleVO.class);
        return PlainResult.success(userRoleVOList, "获取成功");
    }

    @PostMapping("/assignRole")
    public PlainResult<Boolean> assignRoleByUserId(@RequestBody AssignRoleByUserIdReq assignRoleByUserIdReq) {
        LoginUserInfoBO userBO = LoginUserInfoUtils.getLoginUserInfo();
        this.userService.assignRoleByUserId(userBO.getCompanyId(),assignRoleByUserIdReq.getUserId(),assignRoleByUserIdReq.getRoleIds(),userBO.getUserId());
        return PlainResult.success(true, "分配成功");
    }

    @PostMapping("/batchAssignRole")
    public PlainResult<Boolean> batchAssignRoleByUserId(@RequestBody BatchAssignRoleByUserIdsReq batchAssignRoleByUserIdsReq) {
        LoginUserInfoBO userBO = LoginUserInfoUtils.getLoginUserInfo();
        this.userService.batchAssignRoleByUserId(userBO.getCompanyId(),batchAssignRoleByUserIdsReq.getUserIds(),batchAssignRoleByUserIdsReq.getRoleIds(),userBO.getUserId());
        return PlainResult.success(true, "分配成功");
    }

    @GetMapping("/jobCount")
    public PlainResult<UserJobCountDTO> jobCount() {
        LoginUserInfoBO userBO = LoginUserInfoUtils.getLoginUserInfo();
        UserJobCountDTO userJobCountDTO = this.userService.userJobCount(userBO.getCompanyId());
        return PlainResult.success(userJobCountDTO, "获取成功");
    }

    @GetMapping("/statusCount")
    public PlainResult<List<UserStatusCountDTO>> statusCount() {
        LoginUserInfoBO userBO = LoginUserInfoUtils.getLoginUserInfo();
        List<UserStatusCountDTO> userStatusCountDTOList = this.userService.userStatusCount(userBO.getCompanyId());
        return PlainResult.success(userStatusCountDTOList, "获取成功");
    }

    @GetMapping("/deptCount")
    public PlainResult<List<UserDeptCountDTO>> deptCount() {
        LoginUserInfoBO userBO = LoginUserInfoUtils.getLoginUserInfo();
        List<UserDeptCountDTO> userDeptCountDTOList = this.userService.userDeptCount(userBO.getCompanyId());
        return PlainResult.success(userDeptCountDTOList, "获取成功");
    }

    @GetMapping("/userPermissionList")
    public PlainResult<List<UserPermissionDTO>> queryUserPermissionList() {
        LoginUserInfoBO userBO = LoginUserInfoUtils.getLoginUserInfo();
        List<UserPermissionDTO> userPermissionDTOList = this.userService.queryUserPermissionList(userBO.getCompanyId(),userBO.getUserId());
        return PlainResult.success(userPermissionDTOList, "获取成功");
    }

    @GetMapping("/userPermissionButtonList")
    public PlainResult<List<UserPermissionButtonDTO>> queryUserPermissionButtonList() {
        LoginUserInfoBO userBO = LoginUserInfoUtils.getLoginUserInfo();
        List<UserPermissionButtonDTO> userPermissionDTOList = this.userService.queryUserPermissionButtonList(userBO.getCompanyId(),userBO.getUserId());
        return PlainResult.success(userPermissionDTOList, "获取成功");
    }

    @GetMapping("/queryUserTree")
    public PlainResult<UserClassificationDTO> queryUserTree(@RequestParam(value = "userName", required = false) String userName) {
        UserClassificationDTO userClassificationDTO = this.userService.queryUserTree(userName);
        return PlainResult.success(userClassificationDTO, "获取成功");
    }
}
