package com.das.province.web.controller.usercenter.permission.request;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
public class SaveFunctionPermissionVO implements Serializable {
    private static final long serialVersionUID = -5352310030759125205L;
    private Long permissionId;
    @NotNull(message = "权限名称不能为空")
    private String permissionName;
    @NotNull(message = "权限编码不能为空")
    private String permissionCode;
    private String permissionType;
    private Long parentId;
    @NotNull(message = "分类层级不能为空")
    private Byte level;
    private Byte sortIndex;
}
