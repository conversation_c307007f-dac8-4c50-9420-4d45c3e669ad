package com.das.province.web.controller.common.user;

import cn.dev33.satoken.session.SaSession;
import cn.dev33.satoken.stp.StpUtil;
import com.das.province.service.biz.common.dto.AccUserInfoDTO;
import com.das.province.service.biz.user.UserService;
import com.das.province.service.biz.user.action.QueryUserByLoginAccountAction;
import com.das.province.service.biz.user.dto.UserDTO;
import com.das.province.service.enums.LoginClientTypeEnum;
import com.das.province.common.bo.PlainResult;
import com.das.province.common.exception.CommonErrorCodeEnum;
import com.das.province.common.exception.CommonException;
import com.das.province.common.utils.BeanCopyUtils;
import com.das.province.web.aop.annoation.PlainResultWrapper;
import com.das.province.web.controller.bo.LoginUserInfoBO;
import com.das.province.web.controller.bo.UserBO;
import com.das.province.web.controller.common.user.request.LoginUserReq;
import com.das.province.web.controller.utils.LoginUserInfoUtils;
import com.google.code.kaptcha.Constants;
import com.google.code.kaptcha.Producer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.imageio.ImageIO;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.awt.image.BufferedImage;
import java.io.IOException;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

@Slf4j
@PlainResultWrapper
@RestController
@RequestMapping("/province")
public class LoginController {

    @Resource
    private UserService userService;

    @Resource
    private RedisTemplate redisTemplate;

    @Autowired
    private Producer captchaProducer;

    private final static String USER_LOGIN_ERROR_COUNT = "province_user_login_error_count_";

    private final static String USER_LOCK_TIME = "province_user_lock_time_";

    //private final static String USER_LOGIN_CODE = "province_user_login_code_";

    @GetMapping("/captcha")
    public void getCaptcha(HttpServletRequest request,
                           HttpServletResponse response) throws IOException {
        response.setContentType("image/jpeg");
        String capText = captchaProducer.createText();
        // 缓存验证码，并设置过期时间为3分钟
        request.getSession().setAttribute(Constants.KAPTCHA_SESSION_KEY, capText);
        String sessionId = request.getSession().getId();
        redisTemplate.opsForValue().set(sessionId, capText, 60*3, TimeUnit.SECONDS);
        BufferedImage bi = captchaProducer.createImage(capText);
        ServletOutputStream out = response.getOutputStream();
        ImageIO.write(bi, "jpg", out);
        try {
            out.flush();
        } finally {
            out.close();
        }
    }

    @PostMapping(value = "/pc/login")
    public PlainResult<Long> pcLogin(@RequestBody LoginUserReq req, HttpServletRequest request) {
        // 判断账号是否被锁定
        Object lockTime = redisTemplate.opsForValue().get(USER_LOCK_TIME + req.getUserName());
        if(Objects.nonNull(lockTime)){
            throw new CommonException(CommonErrorCodeEnum.VALIDATE_ERROR, "用户名密码输入错误次数超过限制，账号已被锁定10分钟!");
        }

        Object code = redisTemplate.opsForValue().get(request.getSession().getId());
        //String code = (String) request.getSession().getAttribute(Constants.KAPTCHA_SESSION_KEY);
        if(Objects.isNull(code)){
            throw new CommonException(CommonErrorCodeEnum.VALIDATE_ERROR, "请先获取验证码!");
        }
        if (!req.getCaptcha().equals(code.toString())) {
            throw new CommonException(CommonErrorCodeEnum.VALIDATE_ERROR, "验证码不正确, 请重新输入再试!");
        }
        QueryUserByLoginAccountAction action = new QueryUserByLoginAccountAction();
        action.setLoginAccount(req.getUserName());
        action.setPassword(req.getPassword());
        action.setLoginClientType(LoginClientTypeEnum.PROVINCEPC);
        UserDTO userDTO = this.userService.queryUserByLoginAccount(action);
        if (Objects.isNull(userDTO)) {
            // 记录用户密码输入错误次数，1分钟内连续三次输入错误，则锁定账号10分钟
            Object errorCount = redisTemplate.opsForValue().get(USER_LOGIN_ERROR_COUNT + req.getUserName());
            if(Objects.isNull(errorCount)){
                redisTemplate.opsForValue().set(USER_LOGIN_ERROR_COUNT + req.getUserName(), 1);
            }else{
                int count = Integer.parseInt(errorCount.toString());
                int errorNum = count + 1;
                if(errorNum >= 3){
                    redisTemplate.opsForValue().set(USER_LOCK_TIME + req.getUserName(), 0, 10, TimeUnit.MINUTES);
                    throw new CommonException(CommonErrorCodeEnum.VALIDATE_ERROR, "用户名密码输入错误次数超过限制，账号将被锁定10分钟!");
                }else{
                    redisTemplate.opsForValue().set(USER_LOGIN_ERROR_COUNT + req.getUserName(), errorNum);
                }
            }
            throw new CommonException(CommonErrorCodeEnum.VALIDATE_ERROR, "用户名密码不正确, 请重新输入再试!");
        }
        Long currUserId = userDTO.getUserId();
        StpUtil.login(currUserId, LoginClientTypeEnum.PROVINCEPC.getCode());
        SaSession session = StpUtil.getSession();
        LoginUserInfoBO loginUserInfoBO = BeanCopyUtils.copyByJSON(userDTO, LoginUserInfoBO.class);
        session.set("loginUser", loginUserInfoBO);
        // 登录成功，清除登录错误缓存
        redisTemplate.delete(USER_LOGIN_ERROR_COUNT + req.getUserName());
        redisTemplate.delete(request.getSession().getId());
        return PlainResult.success(currUserId, "客户端登录成功");
    }

    @PostMapping(value = "/pc/logout")
    public PlainResult<Boolean> pcLogout() {
        SaSession session = StpUtil.getSession();
        LoginUserInfoBO loginUserInfoBO = (LoginUserInfoBO) session.get("loginUser");
        if(Objects.isNull(loginUserInfoBO)){
            return PlainResult.success(true, "退出登录成功");
        }
        StpUtil.logout(loginUserInfoBO.getUserId(), LoginClientTypeEnum.PROVINCEPC.getCode());
        session.logoutByTokenSignCountToZero();
        return PlainResult.success(true, "退出登录成功");
    }

    @PostMapping(value = "/logout")
    public PlainResult<Boolean> logout(HttpServletRequest request, HttpSession session) {
        LoginUserInfoBO userBO = LoginUserInfoUtils.getLoginUserInfo();
        session.removeAttribute("user");
        session.invalidate();
        return PlainResult.success(true, "退出登录成功");
    }
}
