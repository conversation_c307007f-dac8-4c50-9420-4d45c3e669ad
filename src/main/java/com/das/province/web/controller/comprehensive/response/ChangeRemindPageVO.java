package com.das.province.web.controller.comprehensive.response;

import lombok.Data;


@Data
public class ChangeRemindPageVO {

    /**
     * 异动id
     */
    private Long changeId;

    /**
     * 异动类型,1:藏品异动,2:观众异动,3:温度异常,4:湿度异常,5:设备离线异常
     */
    private Byte changeType;

    /**
     * 异动描述
     */
    private String changeDescription;

    /**
     * 异动发生时间
     */
    private String happenTime;

}
