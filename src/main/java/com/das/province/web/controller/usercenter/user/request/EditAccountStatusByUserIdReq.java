package com.das.province.web.controller.usercenter.user.request;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
public class EditAccountStatusByUserIdReq implements Serializable {
    private static final long serialVersionUID = 6437653995303117196L;
    @NotNull(message = "用户id不能为空")
    private Long userId;
    @NotNull(message = "账号状态不能为空")
    private Byte accountStatus;
}
