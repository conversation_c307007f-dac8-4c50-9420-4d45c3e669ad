package com.das.province.web.controller.comprehensive.response;

import lombok.Data;

import java.util.List;

@Data
public class CityVisitorStatisticsVO {

    /**
     * 今日全市观众总量
     */
    private Long todayTotal;

    /**
     * 今日全市线下入馆观众
     */
    private Long todayOffline;

    /**
     * 今日全市线上访问观众
     */
    private Long todayOnline;

    /**
     * 近一年全市观众总量
     */
    private Long yearTotal;

    /**
     * 近一年全市线下入馆观众
     */
    private Long yearOffline;

    /**
     * 近一年全市线上访问观众
     */
    private Long yearOnline;

    /**
     * 近12个月观众统计
     */
    private List<CityMonthVisitorVO> cityMonthVisitorVOList;
}
