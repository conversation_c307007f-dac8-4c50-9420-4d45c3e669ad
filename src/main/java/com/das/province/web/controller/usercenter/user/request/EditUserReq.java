package com.das.province.web.controller.usercenter.user.request;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

@Data
public class EditUserReq implements Serializable {
    private static final long serialVersionUID = -8815541679319285748L;
    @NotNull(message = "用户id不能为空")
    private Long userId;
    @NotNull(message = "用户名称不能为空")
    private String userName;
    @NotNull(message = "用户密码不能为空")
    private String password;
    // @NotNull(message = "用户手机号不能为空")
    private String phone;
    @NotNull(message = "部门不能为空")
    private Long departmentId;
    // @NotNull(message = "职位不能为空")
    private Long positionId;
    // @NotNull(message = "岗位编码不能为空")
    private String postCode;

    /**
     * 角色ids
     */
    @NotNull(message = "角色不能为空")
    private List<Long> roleIds;

    /**
     * 职位名称
     */
    private String positionName;

    /**
     * 领导id
     */
    private Long leaderId;
}
