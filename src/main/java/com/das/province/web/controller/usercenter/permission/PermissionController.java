package com.das.province.web.controller.usercenter.permission;

import com.das.province.service.biz.user.PermissionService;
import com.das.province.service.biz.user.action.*;
import com.das.province.service.biz.user.dto.PermissionDTO;
import com.das.province.service.biz.user.dto.PermissionPageDTO;
import com.das.province.service.biz.user.dto.PermissionTreeDTO;
import com.das.province.service.enums.FunctionStatusEnum;
import com.das.province.service.enums.PermissionTypeEnum;
import com.das.province.common.bo.PlainResult;
import com.das.province.common.bo.SimplePageInfo;
import com.das.province.web.aop.annoation.PlainResultWrapper;
import com.das.province.web.controller.bo.LoginUserInfoBO;
import com.das.province.web.controller.usercenter.permission.request.*;
import com.das.province.web.controller.utils.LoginUserInfoUtils;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

@Slf4j
@PlainResultWrapper
@RestController
@RequestMapping("/province/setting/permission")
public class PermissionController {

    @Resource
    private PermissionService permissionService;

    @PostMapping("/add")
    public PlainResult<Long> addPermission(@RequestBody AddPermissionReq req,
                                           HttpServletRequest request, HttpSession session) {
        LoginUserInfoBO userBO = LoginUserInfoUtils.getLoginUserInfo();
        AddPermissionAction action = new AddPermissionAction();
        action.setCompanyId(userBO.getCompanyId());
        action.setPermissionCode(req.getPermissionCode());
        action.setPermissionName(req.getPermissionName());
        action.setPermissionType(PermissionTypeEnum.fromCode(req.getPermissionType()));
        action.setParentId(req.getParentId());
        action.setLevel(req.getLevel());
        action.setSortIndex(req.getSortIndex());
        action.setFunctionStatus(FunctionStatusEnum.fromCode(req.getFunctionStatus()));
        action.setResourceUrl(req.getResourceUrl());
        action.setResourceIcon(req.getResourceIcon());
        action.setCreator(userBO.getUserId());
        action.setModifier(userBO.getUserId());
        action.setFunctionPermissionList(this.convertFunctionPermission(null, req.getLevel(), req.getFunctionPermissionList()));
        Long permissionId = this.permissionService.addPermission(action);
        return PlainResult.success(permissionId, "新增成功");
    }

    @PostMapping("/edit")
    public PlainResult<Long> editPermissionById(@RequestBody EditPermissionByIdReq req,
                                                HttpServletRequest request, HttpSession session) {
        LoginUserInfoBO userBO = LoginUserInfoUtils.getLoginUserInfo();
        EditPermissionByIdAction action = new EditPermissionByIdAction();
        action.setCompanyId(userBO.getCompanyId());
        action.setPermissionId(req.getPermissionId());
        action.setPermissionCode(req.getPermissionCode());
        action.setPermissionName(req.getPermissionName());
        action.setSortIndex(req.getSortIndex());
        action.setFunctionStatus(FunctionStatusEnum.fromCode(req.getFunctionStatus()));
        action.setResourceUrl(req.getResourceUrl());
        action.setResourceIcon(req.getResourceIcon());
        action.setModifier(userBO.getUserId());
        action.setFunctionPermissionList(this.convertFunctionPermission(req.getPermissionId(), req.getLevel(), req.getFunctionPermissionList()));
        this.permissionService.editPermissionById(action);
        return PlainResult.success(req.getPermissionId(), "修改成功");
    }

    @PostMapping("/saveFunction")
    public PlainResult<Boolean> saveFunctionPermission(@RequestBody SaveFunctionPermissionReq req,
                                                       HttpServletRequest request, HttpSession session) {
        LoginUserInfoBO userBO = LoginUserInfoUtils.getLoginUserInfo();
        AddFunctionPermissionAction action = new AddFunctionPermissionAction();
        action.setCompanyId(userBO.getCompanyId());
        action.setModifier(userBO.getUserId());
        action.setParentId(req.getParentId());
        action.setFunctionPermissionList(this.convertFunctionPermission(req.getParentId(), req.getParentLevel(), req.getFunctionPermissionList()));
        this.permissionService.saveFunctionPermission(action);
        return PlainResult.success(true, "保存成功");
    }

    @PostMapping("/del")
    public PlainResult<Boolean> delByPermissionId(@RequestParam(value = "permissionId") Long permissionId,
                                                  HttpServletRequest request, HttpSession session) {
        LoginUserInfoBO userBO = LoginUserInfoUtils.getLoginUserInfo();
        DelPermissionByIdAction action = new DelPermissionByIdAction();
        action.setPermissionId(permissionId);
        action.setCompanyId(userBO.getCompanyId());
        action.setModifier(userBO.getUserId());
        this.permissionService.delPermissionById(action);
        return PlainResult.success(true, "删除成功");
    }

    @PostMapping("/batchDel")
    public PlainResult<Boolean> delByPermissionIds(@RequestBody DelByPermissionIdsReq req,
                                                   HttpServletRequest request, HttpSession session) {
        LoginUserInfoBO userBO = LoginUserInfoUtils.getLoginUserInfo();
        BatchDelPermissionByIdAction action = new BatchDelPermissionByIdAction();
        action.setPermissionIds(req.getPermissionIds());
        action.setCompanyId(userBO.getCompanyId());
        action.setModifier(userBO.getUserId());
        this.permissionService.batchDelPermissionById(action);
        return PlainResult.success(true, "删除成功");
    }

    @GetMapping("/pageList")
    public PlainResult<SimplePageInfo<PermissionPageDTO>> getPageByConditionQuery(@RequestParam(value = "permissionId", required = false) Long permissionId,
                                                                                  @RequestParam(value = "permissionName", required = false) String permissionName,
                                                                                  @RequestParam(value = "level", required = false) Byte level,
                                                                                  @RequestParam(value = "sortBy", defaultValue = "level asc, c.sort_index asc") String sortBy,
                                                                                  @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
                                                                                  @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize,
                                                                                  HttpServletRequest request, HttpSession session) {
        LoginUserInfoBO userBO = LoginUserInfoUtils.getLoginUserInfo();
        QueryPagePermissionByCondition action = new QueryPagePermissionByCondition();
        action.setCompanyId(userBO.getCompanyId());
        action.setPermissionId(permissionId);
        action.setPermissionName(permissionName);
        action.setLevel(level);
        action.setSortBy(sortBy);
        action.setPageNum(pageNum);
        action.setPageSize(pageSize);
        SimplePageInfo<PermissionPageDTO> pageInfo = this.permissionService.queryPageByCondition(action);
        return PlainResult.success(pageInfo, "获取成功");
    }

    @GetMapping("/info")
    public PlainResult<PermissionDTO> getDetailByPermissionId(@RequestParam(value = "permissionId") Long permissionId,
                                                              HttpServletRequest request, HttpSession session) {
        LoginUserInfoBO userBO = LoginUserInfoUtils.getLoginUserInfo();
        PermissionDTO permissionDTO = this.permissionService.queryByPermissionId(userBO.getCompanyId(), permissionId);
        if (Objects.isNull(permissionDTO)) {
            return PlainResult.success(null, "获取成功");
        }
        return PlainResult.success(permissionDTO, "获取成功");
    }

    @GetMapping("/directChild")
    public PlainResult<List<PermissionDTO>> getDirectChildByPermissionId(@RequestParam(value = "permissionId") Long permissionId,
                                                                   HttpServletRequest request, HttpSession session) {
        LoginUserInfoBO userBO = LoginUserInfoUtils.getLoginUserInfo();
        List<PermissionDTO> permissionDTOList = this.permissionService.queryDirectChildByPermissionId(userBO.getCompanyId(), permissionId);
        return PlainResult.success(permissionDTOList, "获取成功");
    }

    @GetMapping("/menuTree")
    public PlainResult<PermissionTreeDTO> getMenuTree(HttpServletRequest request, HttpSession session) {
        LoginUserInfoBO userBO = LoginUserInfoUtils.getLoginUserInfo();
        PermissionTreeDTO permissionTreeDTO = this.permissionService.queryTreeByPermissionType(userBO.getCompanyId(), PermissionTypeEnum.MENU);
        return PlainResult.success(permissionTreeDTO, "获取成功");
    }

    @GetMapping("/permissionTree")
    public PlainResult<PermissionTreeDTO> getAllPermissionTree(HttpServletRequest request, HttpSession session) {
        LoginUserInfoBO userBO = LoginUserInfoUtils.getLoginUserInfo();
        PermissionTreeDTO permissionTreeDTO = this.permissionService.queryTreeByPermissionType(userBO.getCompanyId(), null);
        return PlainResult.success(permissionTreeDTO, "获取成功");
    }

    private List<SaveFunctionPermissionAction> convertFunctionPermission(Long permissionId, Byte parentLevel, List<SaveFunctionPermissionVO> functionPermissionList) {
        if (CollectionUtils.isEmpty(functionPermissionList)) {
            return Lists.newArrayList();
        }
        AtomicInteger count= new AtomicInteger(1);
        return functionPermissionList.stream().map(functionPermission -> {
            SaveFunctionPermissionAction action = new SaveFunctionPermissionAction();
            action.setPermissionId(functionPermission.getPermissionId());
            action.setPermissionName(functionPermission.getPermissionName());
            action.setPermissionCode(functionPermission.getPermissionCode());
            if (Objects.isNull(functionPermission.getPermissionType())) {
                action.setPermissionType(PermissionTypeEnum.FCN);
            } else {
                action.setPermissionType(PermissionTypeEnum.fromCode(functionPermission.getPermissionType()));
            }
            action.setParentId(permissionId);
            action.setLevel(functionPermission.getLevel());
            action.setSortIndex((byte) count.getAndIncrement());
            return action;
        }).collect(Collectors.toList());
    }
}
