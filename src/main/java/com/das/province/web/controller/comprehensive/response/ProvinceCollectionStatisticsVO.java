package com.das.province.web.controller.comprehensive.response;

import lombok.Data;

import java.util.List;

@Data
public class ProvinceCollectionStatisticsVO {

    /**
     * 藏品总数
     */
    private Long totalCollectionNum;

    /**
     * 定级藏品数
     */
    private Long valuableNum;

    /**
     * 未定级藏品数
     */
    private Long exhibitionNum;

    /**
     * 文物全息展示数量
     */
    private Long holographicNum;

    /**
     * 地区藏品占比
     */
    private List<CityCollectionNumVO> cityCollectionNumVOList;
}
