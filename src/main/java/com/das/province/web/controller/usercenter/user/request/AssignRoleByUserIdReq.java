package com.das.province.web.controller.usercenter.user.request;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

@Data
public class AssignRoleByUserIdReq implements Serializable {
    private static final long serialVersionUID = -4717251717815655079L;
    @NotNull(message = "用户信息不能为空")
    private Long userId;
    @NotNull(message = "角色信息不能为空")
    private List<Long> roleIds;
}
