package com.das.province.web.controller.usercenter.position.request;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
public class EditPositionReq implements Serializable {
    private static final long serialVersionUID = -6005492647488992136L;
    @NotNull(message = "职位id不能为空")
    private Long positionId;
    @NotNull(message = "职位名称不能为空")
    private String positionName;
}