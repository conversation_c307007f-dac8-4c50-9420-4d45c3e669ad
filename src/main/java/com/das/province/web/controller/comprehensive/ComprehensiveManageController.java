package com.das.province.web.controller.comprehensive;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.das.province.common.bo.PlainResult;
import com.das.province.common.bo.SimplePageInfo;
import com.das.province.common.exception.CommonErrorCodeEnum;
import com.das.province.common.exception.CommonException;
import com.das.province.service.biz.comprehensive.ComprehensiveManageService;
import com.das.province.service.biz.comprehensive.action.ChangeRemindPageAction;
import com.das.province.service.biz.comprehensive.action.GeneralStatisticMuseumAction;
import com.das.province.service.biz.comprehensive.action.GeneralStatisticRegionAction;
import com.das.province.service.biz.comprehensive.action.MuseumInfoListAction;
import com.das.province.service.task.MuseumDataGatherTask;
import com.das.province.web.aop.annoation.PlainResultWrapper;
import com.das.province.web.controller.bo.LoginUserInfoBO;
import com.das.province.web.controller.comprehensive.response.*;
import com.das.province.web.controller.utils.LoginUserInfoUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.core.io.ClassPathResource;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.io.BufferedOutputStream;
import java.io.File;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.List;

/**
 * 综合管理
 */

@Slf4j
@PlainResultWrapper
@RestController
@RequestMapping("/comprehensive")
public class ComprehensiveManageController {

    @Resource
    private ComprehensiveManageService comprehensiveManageService;

    @Resource
    private MuseumDataGatherTask museumDataGatherTask;

    /**
     * 手动触发定时任务
     * @return
     */
    @GetMapping("/teachTask")
    public PlainResult<String> teachTask() {
        museumDataGatherTask.calculateMuseumCollection();
        museumDataGatherTask.calculateMuseumGeneral();
        museumDataGatherTask.calculateMuseumClassify();
        museumDataGatherTask.calculateMuseumVisitor();
        return PlainResult.success("true", "获取成功");
    }

    /**
     * 全省博物馆概况
     * @return
     */
    @GetMapping("/queryMuseumGeneral")
    public PlainResult<MuseumGeneralVO> queryMuseumGeneral() {
        MuseumGeneralVO museumGeneralVO = this.comprehensiveManageService.queryMuseumGeneral();
        return PlainResult.success(museumGeneralVO, "获取成功");
    }

    /**
     * 博物馆各市情况
     * @return
     */
    @GetMapping("/cityMuseumGeneral")
    public PlainResult<List<CityMuseumNumVO>> cityMuseumGeneral() {
        List<CityMuseumNumVO> cityMuseumNumVOList = this.comprehensiveManageService.queryCityMuseumNum();
        return PlainResult.success(cityMuseumNumVOList, "获取成功");
    }

    /**
     * 获取博物馆列表
     * @param regionCode 行政区划编码
     * @param museumCode 博物馆编码
     * @param levelId    质量等级id
     * @param natureId   性质Id
     * @param settleIn   是否入驻 0否，1是
     * @param isClose    今日是否开放   0否，1是
     * @param sortBy     排序字段   1 年报数量正序 2 年报数量反序  3 平台藏品数量正序 4 平台藏品数量反序 5 本年观众正序  6 本年观众反序
     * @param pageNum
     * @param pageSize
     * @return
     */
    @GetMapping("/museumInfoList")
    public PlainResult<SimplePageInfo<MuseumInfoListVO>> museumInfoList(@RequestParam(value = "regionCode", required = false) String regionCode,
                                                                        @RequestParam(value = "museumCode", required = false) String museumCode,
                                                                        @RequestParam(value = "museumName", required = false) String museumName,
                                                                        @RequestParam(value = "levelId", required = false) Long levelId,
                                                                        @RequestParam(value = "natureId", required = false) Long natureId,
                                                                        @RequestParam(value = "settleIn", required = false) Byte settleIn,
                                                                        @RequestParam(value = "isClose", required = false) Byte isClose,
                                                                        @RequestParam(value = "sortBy", required = false) Byte sortBy,
                                                                        @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
                                                                        @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize) {
        LoginUserInfoBO userBO = LoginUserInfoUtils.getLoginUserInfo();
        MuseumInfoListAction action = new MuseumInfoListAction();
        action.setCompanyId(userBO.getCompanyId());
        action.setRegionCode(regionCode);
        action.setMuseumCode(museumCode);
        action.setMuseumName(museumName);
        action.setLevelId(levelId);
        action.setIsClose(isClose);
        action.setNatureId(natureId);
        action.setSettleIn(settleIn);
        action.setSortBy(sortBy);
        action.setPageNum(pageNum);
        action.setPageSize(pageSize);
        SimplePageInfo<MuseumInfoListVO> museumInfoList = this.comprehensiveManageService.museumInfoList(action);
        return PlainResult.success(museumInfoList, "获取成功");
    }

    /**
     * 异动提醒
     * @return
     */
    @GetMapping("/changeRemindPageList")
    public PlainResult<SimplePageInfo<ChangeRemindPageVO>> changeRemindPageList(@RequestParam(value = "changeType", required = false) Byte changeType,
                                                                                @RequestParam(value = "startTime", required = false) String startTime,
                                                                                @RequestParam(value = "endTime", required = false) String endTime,
                                                                                @RequestParam(value = "sortBy", defaultValue = "gmt_create desc") String sortBy,
                                                                                @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
                                                                                @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize,
                                                                                HttpServletRequest request, HttpSession session) {

        ChangeRemindPageAction action = new ChangeRemindPageAction();
        action.setChangeType(changeType);
        action.setStartTime(startTime);
        action.setEndTime(endTime);
        action.setSortBy(sortBy);
        action.setPageNum(pageNum);
        action.setPageSize(pageSize);
        SimplePageInfo<ChangeRemindPageVO> pageInfo = this.comprehensiveManageService.queryChangeRemindPageList(action);
        return PlainResult.success(pageInfo, "获取成功");
    }

    /**
     * 异动提醒详情
     * @return
     */
    @GetMapping("/changeRemindDetailList")
    public PlainResult<List<ChangeDetailInfoVO>> changeRemindDetailList(@RequestParam(value = "changeId") Long changeId) {

        List<ChangeDetailInfoVO> changeDetailInfoList = this.comprehensiveManageService.queryChangeRemindDetailList(changeId);
        return PlainResult.success(changeDetailInfoList, "获取成功");
    }

    /**
     * 异动提醒详情(温湿度)
     * @return
     */
    @GetMapping("/changeDeviceThInfo")
    public PlainResult<ChangeThInfoVO> changeDeviceThInfo(@RequestParam(value = "changeId") Long changeId) {
        ChangeThInfoVO changeThInfoVO = this.comprehensiveManageService.getChangeDeviceThInfo(changeId);
        return PlainResult.success(changeThInfoVO, "获取成功");
    }

    /**
     * 异动提醒详情(离线)
     * @return
     */
    @GetMapping("/changeDeviceOfflineList")
    public PlainResult<List<ChangeOfflineVO>> changeDeviceOfflineList(@RequestParam(value = "changeId") Long changeId) {
        List<ChangeOfflineVO> changeOfflineVOList = this.comprehensiveManageService.getChangeDeviceOfflineList(changeId);
        return PlainResult.success(changeOfflineVOList, "获取成功");
    }

    /**
     * 全省观众统计
     * @return
     */
    @GetMapping("/provinceVisitorStatistics")
    public PlainResult<ProvinceVisitorStatisticsVO> provinceVisitorStatistics() {
        ProvinceVisitorStatisticsVO provinceVisitorStatisticsVO = this.comprehensiveManageService.provinceVisitorStatistics();
        return PlainResult.success(provinceVisitorStatisticsVO, "获取成功");
    }

    /**
     * 全省藏品统计
     * @return
     */
    @GetMapping("/provinceCollectionStatistics")
    public PlainResult<ProvinceCollectionStatisticsVO> provinceCollectionStatistics() {
        ProvinceCollectionStatisticsVO provinceCollectionStatisticsVO = this.comprehensiveManageService.provinceCollectionStatistics();
        return PlainResult.success(provinceCollectionStatisticsVO, "获取成功");
    }


    /**
     * 全省热门top5
     * @return
     */
    @GetMapping("/hotVisitorTop")
    public PlainResult<List<HotVisitorTopVO>> hotVisitorTop() {
        List<HotVisitorTopVO> hotVisitorTopVOList = this.comprehensiveManageService.hotVisitorTop();
        return PlainResult.success(hotVisitorTopVOList, "获取成功");
    }

    /**
     * 市观众统计
     * @return
     */
    @GetMapping("/cityVisitorStatistics")
    public PlainResult<CityVisitorStatisticsVO> cityVisitorStatistics(@RequestParam(value = "regionCode") String regionCode) {
        if(StringUtils.isBlank(regionCode)){
            throw new CommonException(CommonErrorCodeEnum.VALIDATE_ERROR, "regionCode不能为空！");

        }
        CityVisitorStatisticsVO cityVisitorStatisticsVO = this.comprehensiveManageService.cityVisitorStatistics(regionCode);
        return PlainResult.success(cityVisitorStatisticsVO, "获取成功");
    }

    /**
     * 市各博物馆藏品、观众统计
     * sortBy 排序字段，1 藏品总数，2 今日观众总量，3 近一年观众总量
     * orderBy 0正序，1反序
     * @return
     */
    @GetMapping("/museumDataStatistics")
    public PlainResult<List<MuseumDataStatisticsVO>> museumDataStatistics(@RequestParam(value = "regionCode") String regionCode,
                                                                          @RequestParam(value = "sortBy",defaultValue = "1") Byte sortBy,
                                                                          @RequestParam(value = "orderBy",defaultValue = "1") Byte orderBy) {
        if(StringUtils.isBlank(regionCode)){
            throw new CommonException(CommonErrorCodeEnum.VALIDATE_ERROR, "regionCode不能为空！");

        }
        List<MuseumDataStatisticsVO> museumDataStatisticsVOList = this.comprehensiveManageService.museumDataStatistics(regionCode,sortBy,orderBy);
        return PlainResult.success(museumDataStatisticsVOList, "获取成功");
    }

    /**
     * 市概况、藏品动态
     * @return
     */
    @GetMapping("/cityCollectionStatistics")
    public PlainResult<CityCollectionStatisticsVO> cityCollectionStatistics(@RequestParam(value = "regionCode") String regionCode) {
        if(StringUtils.isBlank(regionCode)){
            throw new CommonException(CommonErrorCodeEnum.VALIDATE_ERROR, "regionCode不能为空！");

        }
        CityCollectionStatisticsVO cityCollectionStatisticsVO = this.comprehensiveManageService.cityCollectionStatistics(regionCode);
        return PlainResult.success(cityCollectionStatisticsVO, "获取成功");
    }

    /**
     * 概况统计报表-地区情况
     * dateType 日期类型：0 全部，1 近一月，2 近半年，3 近一年 4 自定义
     * startTime  开始时间
     * endTime    结束时间
     * @return
     */
    @GetMapping("/generalStatisticRegionList")
    public PlainResult<SimplePageInfo<GeneralStatisticRegionVO>> generalStatisticRegionList(@RequestParam(value = "dateType") Byte dateType,
                                                                                            @RequestParam(value = "startTime", required = false) String startTime,
                                                                                            @RequestParam(value = "endTime", required = false) String endTime,
                                                                                            @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
                                                                                            @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize) {

        GeneralStatisticRegionAction action = new GeneralStatisticRegionAction();
        action.setDateType(dateType);
        action.setStartTime(startTime);
        action.setEndTime(endTime);
        action.setPageNum(pageNum);
        action.setPageSize(pageSize);
        SimplePageInfo<GeneralStatisticRegionVO> pageInfo = this.comprehensiveManageService.generalStatisticRegionList(action);
        return PlainResult.success(pageInfo, "获取成功");
    }

    /**
     * 导出概况统计报表-地区情况excel
     * dateType 日期类型：0 全部，1 近一月，2 近半年，3 近一年 4 自定义
     * startTime  开始时间
     * endTime    结束时间
     * @return
     */
    @GetMapping("/exportRegionList")
    public void exportRegionList(@RequestParam(value = "dateType") Byte dateType,
                                    @RequestParam(value = "startTime", required = false) String startTime,
                                    @RequestParam(value = "endTime", required = false) String endTime,
                                    @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
                                    @RequestParam(value = "pageSize", defaultValue = "10000") Integer pageSize,
                                    HttpServletRequest request,HttpServletResponse response) {
        GeneralStatisticRegionAction action = new GeneralStatisticRegionAction();
        action.setDateType(dateType);
        action.setStartTime(startTime);
        action.setEndTime(endTime);
        action.setPageNum(pageNum);
        action.setPageSize(pageSize);
        SimplePageInfo<GeneralStatisticRegionVO> pageInfo = this.comprehensiveManageService.generalStatisticRegionList(action);
        this.writerExcelFormTemplate(response,"region_general.xlsx",pageInfo.getList());
    }

    /**
     * 概况统计报表-博物馆情况
     * @return
     */
    @GetMapping("/generalStatisticMuseumList")
    public PlainResult<SimplePageInfo<GeneralStatisticMuseumVO>> generalStatisticMuseumList(@RequestParam(value = "regionCode", required = false) String regionCode,
                                                                                            @RequestParam(value = "museumId", required = false) String museumId,
                                                                                            @RequestParam(value = "dateType") Byte dateType,
                                                                                            @RequestParam(value = "startTime", required = false) String startTime,
                                                                                            @RequestParam(value = "endTime", required = false) String endTime,
                                                                                            @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
                                                                                            @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize) {

        GeneralStatisticMuseumAction action = new GeneralStatisticMuseumAction();
        action.setRegionCode(regionCode);
        action.setMuseumId(museumId);
        action.setDateType(dateType);
        action.setStartTime(startTime);
        action.setEndTime(endTime);
        action.setPageNum(pageNum);
        action.setPageSize(pageSize);
        SimplePageInfo<GeneralStatisticMuseumVO> pageInfo = this.comprehensiveManageService.generalStatisticMuseumList(action);
        return PlainResult.success(pageInfo, "获取成功");
    }

    /**
     * 按等级统计全省藏品
     * @return
     */
    @GetMapping("/collectionGroupByLevel")
    public PlainResult<List<CollectionGroupByLevelVO>> collectionGroupByLevel() {
        List<CollectionGroupByLevelVO> collectionGroupByLevelVOList = this.comprehensiveManageService.collectionGroupByLevel();
        return PlainResult.success(collectionGroupByLevelVOList, "获取成功");
    }

    /**
     * 导出概况统计报表-博物馆情况excel
     * @return
     */
    @GetMapping("/exportMuseumList")
    public void exportMuseumList(@RequestParam(value = "regionCode", required = false) String regionCode,
                                 @RequestParam(value = "museumId", required = false) String museumId,
                                 @RequestParam(value = "dateType") Byte dateType,
                                 @RequestParam(value = "startTime", required = false) String startTime,
                                 @RequestParam(value = "endTime", required = false) String endTime,
                                 @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
                                 @RequestParam(value = "pageSize", defaultValue = "10000") Integer pageSize,
                                 HttpServletRequest request,HttpServletResponse response) {

        GeneralStatisticMuseumAction action = new GeneralStatisticMuseumAction();
        action.setRegionCode(regionCode);
        action.setMuseumId(museumId);
        action.setDateType(dateType);
        action.setStartTime(startTime);
        action.setEndTime(endTime);
        action.setPageNum(pageNum);
        action.setPageSize(pageSize);
        SimplePageInfo<GeneralStatisticMuseumVO> pageInfo = this.comprehensiveManageService.generalStatisticMuseumList(action);
        this.writerExcelFormTemplate(response,"museum_general.xlsx",pageInfo.getList());
    }


    private void writerExcelFormTemplate(HttpServletResponse response,String templateName, List dataList) {
        OutputStream out;
        BufferedOutputStream bos;
        try {
            org.springframework.core.io.Resource resource = new ClassPathResource("templates" + File.separator + templateName);
            InputStream inputStream = resource.getInputStream();
            response.setContentType("application/vnd.ms-excel");
            response.setCharacterEncoding("utf-8");
            String fileName = URLEncoder.encode("概况统计报表.xlsx", "utf-8");
            response.setHeader("Content-disposition", "attachment; filename=" +
                    new String(fileName.getBytes(StandardCharsets.UTF_8), StandardCharsets.ISO_8859_1));
            out = response.getOutputStream();
            bos = new BufferedOutputStream(out);
            ExcelWriter excelWriter = EasyExcel.write(bos).withTemplate(inputStream).build();
            WriteSheet writeSheet = EasyExcel.writerSheet().build();
            excelWriter.fill(dataList, writeSheet);
            excelWriter.finish();
            bos.flush();
        } catch (Exception e) {
            log.error("writerExcelFormTemplate Exception:", e);
            throw new CommonException(CommonErrorCodeEnum.UNKNOWN_ERROR, "系统错误, 导出失败!");
        }
    }

}
