package com.das.province.web.controller.usercenter.user.request;

import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @Author: shaochengwei
 * @Date: 2023-02-07
 */
@Data
public class UpdatePasswordReq {

    /**
     * 旧密码
     */
    @NotNull(message = "旧密码不能为空")
    private String oldPassword;

    /**
     * 新密码
     */
    @NotNull(message = "新密码不能为空")
    private String newPassword;

    /**
     * 确认新密码
     */
    @NotNull(message = "确认新密码不能为空")
    private String confirmNewPassword;
}
