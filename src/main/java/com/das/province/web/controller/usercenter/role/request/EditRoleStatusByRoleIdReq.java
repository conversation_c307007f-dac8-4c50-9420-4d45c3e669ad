package com.das.province.web.controller.usercenter.role.request;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
public class EditRoleStatusByRoleIdReq implements Serializable {
    private static final long serialVersionUID = 281341892377419599L;
    @NotNull(message = "角色id不能为空")
    private Long roleId;
    @NotNull(message = "状态不能为空")
    private Byte status;
}
