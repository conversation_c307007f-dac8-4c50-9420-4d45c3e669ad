package com.das.province.web.controller.comprehensive.response;

import lombok.Data;

@Data
public class MuseumClassifyVO {

    /**
     * 分类类型，1:质量级别,2:性质,3:类别
     */
    private Byte classifyType;

    /**
     * 统计类型id
     */
    private Long statisticId;

    /**
     * 统计类型名称
     */
    private String statisticName;

    /**
     * 博物馆数量
     */
    private Integer museumNum;

    /**
     * 占比
     */
    private String museumProportion;
}
