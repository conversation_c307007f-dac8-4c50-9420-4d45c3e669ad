package com.das.province.web.controller.safe;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.das.province.common.bo.PlainResult;
import com.das.province.common.bo.SimplePageInfo;
import com.das.province.common.exception.CommonErrorCodeEnum;
import com.das.province.common.exception.CommonException;
import com.das.province.service.biz.safe.DeviceMonitorService;
import com.das.province.service.biz.safe.dto.*;
import com.das.province.web.aop.annoation.PlainResultWrapper;
import com.das.province.web.controller.bo.LoginUserInfoBO;
import com.das.province.web.controller.utils.LoginUserInfoUtils;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.ClassPathResource;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.io.*;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.List;

/**
 * 实时监控
 *
 * <AUTHOR>
 * @date 2023/7/11
 */
@Slf4j
@PlainResultWrapper
@RestController
@RequestMapping("/device/monitor")
public class DeviceMonitorController {

    @Resource
    private DeviceMonitorService deviceMonitorService;


    /**
     * 查询监控设备详情
     *
     * @param museumId        博物馆code
     * @param regionCode      地区code
     * @param exceptionDegree 异常程度 1:轻度, 2:中度, 3:严重
     * @param regionName     设备区域 1展厅,2库房,3其他
     * @return
     */
    @GetMapping("/info")
    public PlainResult<DeviceMonitorInfoDTO> getDeviceMonitorInfo(@RequestParam(value = "museumId", required = false) String museumId,
                                                                  @RequestParam(value = "regionCode", required = false) String regionCode,
                                                                  @RequestParam(value = "exceptionDegree", required = false) String exceptionDegree,
                                                                  @RequestParam(value = "regionName", required = false) String regionName,
                                                                  HttpServletRequest request, HttpSession session) {
        try {
            LoginUserInfoBO userBO = LoginUserInfoUtils.getLoginUserInfo();
            DeviceMonitorInfoDTO deviceMonitorInfoDTO = this.deviceMonitorService.getDeviceMonitorInfo(museumId, regionCode, exceptionDegree, regionName);
            return PlainResult.success(deviceMonitorInfoDTO, "获取成功");
        } catch (Exception e) {
            log.error("DeviceMonitorController getDeviceMonitorInfo Exception:", e);
            return PlainResult.success(null, "获取成功");
        }
    }

    /**
     * 查询监控设备分页
     *
     * @param museumId        博物馆code
     * @param regionCode      地区code
     * @param exceptionDegree 异常程度 1:轻度, 2:中度, 3:严重
     * @param regionName     设备区域 1展厅,2库房,3其他
     * @param type     类型筛选 1 监测设备 2 温度正常  3 湿度正常  4 监测异常 5 温度异常  6 湿度异常  7 离线设备
     * @param pageNum
     * @param pageSize
     * @param request
     * @param session
     * @return
     */
    @GetMapping("/pageList")
    public PlainResult<SimplePageInfo<SafeDeviceInfoDTO>> getPageByConditionQuery(@RequestParam(value = "museumId", required = false) String museumId,
                                                                                  @RequestParam(value = "regionCode", required = false) String regionCode,
                                                                                  @RequestParam(value = "exceptionDegree", required = false) String exceptionDegree,
                                                                                  @RequestParam(value = "regionName", required = false) String regionName,
                                                                                  @RequestParam(value = "type", required = false) Integer type,
                                                                                  @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
                                                                                  @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize,
                                                                                  HttpServletRequest request, HttpSession session) {
        try {
            LoginUserInfoBO userBO = LoginUserInfoUtils.getLoginUserInfo();
            SimplePageInfo<SafeDeviceInfoDTO> pageInfo = this.deviceMonitorService.getPageByConditionQuery(museumId, regionCode, exceptionDegree, regionName, type, pageNum, pageSize);
            return PlainResult.success(pageInfo, "获取成功");
        } catch (Exception e) {
            log.error("DeviceMonitorController getPageByConditionQuery Exception:", e);
            return PlainResult.success(new SimplePageInfo<>(), "获取成功");
        }
    }

    /**
     * 查询单个监控设备分页
     *
     * @param deviceCode    设备编码
     * @param isException   是否只看异常1:是, 0:否
     * @param statisticsWay 0:今日,1:近一周,2:近一月,3:近一年,5:自定义
     * @param pageNum
     * @param pageSize
     * @param request
     * @param session
     * @return
     */
    @GetMapping("/pageByDeviceCode")
    public PlainResult<SimplePageInfo<SafeDeviceInfoDTO>> pageByDeviceCode(@RequestParam(value = "deviceCode") String deviceCode,
                                                                          @RequestParam(value = "isException", required = false) Integer isException,
                                                                          @RequestParam(value = "statisticsWay") Integer statisticsWay,
                                                                          @RequestParam(value = "startTime", required = false) String startTime,
                                                                          @RequestParam(value = "endTime", required = false) String endTime,
                                                                          @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
                                                                          @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize,
                                                                          HttpServletRequest request, HttpSession session) {
        try {
            LoginUserInfoBO userBO = LoginUserInfoUtils.getLoginUserInfo();
            SimplePageInfo<SafeDeviceInfoDTO> pageInfo = this.deviceMonitorService.pageByDeviceCode(deviceCode, isException, statisticsWay, startTime, endTime, pageNum, pageSize);
            return PlainResult.success(pageInfo, "获取成功");
        } catch (Exception e) {
            log.error("DeviceMonitorController pageByDeviceCode Exception:", e);
            return PlainResult.success(new SimplePageInfo<>(), "获取成功");
        }
    }

    /**
     * 查询单个监控设备信息
     *
     * @param deviceCode    设备编码
     * @param isException   是否只看异常1:是, 0:否
     * @param statisticsWay 0:今日,1:近一周,2:近一月,3:近一年,5:自定义
     * @param request
     * @param session
     * @return
     */
    @GetMapping("/getByDeviceCode")
    public PlainResult<SingleDeviceMonitorInfoDTO> getByDeviceCode(@RequestParam(value = "deviceCode") String deviceCode,
                                                                   @RequestParam(value = "isException", required = false) Integer isException,
                                                                   @RequestParam(value = "statisticsWay") Integer statisticsWay,
                                                                   @RequestParam(value = "startTime", required = false) String startTime,
                                                                   @RequestParam(value = "endTime", required = false) String endTime,
                                                                   HttpServletRequest request, HttpSession session) {
        try {
            LoginUserInfoBO userBO = LoginUserInfoUtils.getLoginUserInfo();
            SingleDeviceMonitorInfoDTO singleDeviceMonitorInfoDTO = this.deviceMonitorService.getByDeviceCode(deviceCode, isException, statisticsWay, startTime, endTime);
            return PlainResult.success(singleDeviceMonitorInfoDTO, "获取成功");
        } catch (Exception e) {
            log.error("DeviceMonitorController getByDeviceCode Exception:", e);
            return PlainResult.success(null, "获取成功");
        }
    }

    /**
     * 查询单个监控设备温度趋势
     *
     * @param deviceCode    设备编码
     * @param statisticsWay 0:今日,1:近一周,2:近一月,3:近一年,5:自定义
     * @param request
     * @param session
     * @return
     */
    @GetMapping("/getTemperatureByDeviceCode")
    public PlainResult<TemperatureTrendDTO> getTemperatureByDeviceCode(@RequestParam(value = "deviceCode") String deviceCode,
                                                                       @RequestParam(value = "statisticsWay") Integer statisticsWay,
                                                                       HttpServletRequest request, HttpSession session) {
        try {
            LoginUserInfoBO userBO = LoginUserInfoUtils.getLoginUserInfo();
            TemperatureTrendDTO temperatureTrendDTO = this.deviceMonitorService.getTemperatureByDeviceCode(deviceCode, statisticsWay);
            return PlainResult.success(temperatureTrendDTO, "获取成功");
        } catch (Exception e) {
            log.error("DeviceMonitorController getTemperatureByDeviceCode Exception:", e);
            return PlainResult.success(null, "获取成功");
        }
    }

    /**
     * 查询单个监控设备湿度趋势
     *
     * @param deviceCode    设备编码
     * @param statisticsWay 0:今日,1:近一周,2:近一月,3:近一年,5:自定义
     * @param request
     * @param session
     * @return
     */
    @GetMapping("/getHumidityByDeviceCode")
    public PlainResult<HumidityTrendDTO> getHumidityByDeviceCode(@RequestParam(value = "deviceCode") String deviceCode,
                                                                 @RequestParam(value = "statisticsWay") Integer statisticsWay,
                                                                 HttpServletRequest request, HttpSession session) {
        try {
            LoginUserInfoBO userBO = LoginUserInfoUtils.getLoginUserInfo();
            HumidityTrendDTO humidityTrendDTO = this.deviceMonitorService.getHumidityByDeviceCode(deviceCode, statisticsWay);
            return PlainResult.success(humidityTrendDTO, "获取成功");
        } catch (Exception e) {
            log.error("DeviceMonitorController getHumidityByDeviceCode Exception:", e);
            return PlainResult.success(null, "获取成功");
        }
    }

    /**
     * 导出查询单个监控设备信息
     * @param deviceCode 设备编码
     * @param isException 是否只看异常1:是, 0:否
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param statisticsWay 0:今日,1:近一周,2:近一月,3:近一年,5:自定义
     * @param response
     */
    @GetMapping("/deviceInfoExport")
    public void deviceInfoExport(@RequestParam(value = "deviceCode") String deviceCode,
                                @RequestParam(value = "isException", required = false) Integer isException,
                                @RequestParam(value = "startTime", required = false) String startTime,
                                @RequestParam(value = "endTime", required = false) String endTime,
                                @RequestParam(value = "statisticsWay") Integer statisticsWay, HttpServletResponse response) {
        OutputStream out;
        BufferedOutputStream bos;
        try {
            List<SafeDeviceInfoDTO> dataList = this.deviceMonitorService.deviceInfoExport(deviceCode, isException, statisticsWay, startTime, endTime);
            org.springframework.core.io.Resource resource = new ClassPathResource("templates" + File.separator + "device_ino_template.xlsx");
            InputStream inputStream = resource.getInputStream();
            response.setContentType("application/vnd.ms-excel");
            response.setCharacterEncoding("utf-8");
            String fileName = URLEncoder.encode("device_ino_template.xlsx", "utf-8");
            response.setHeader("Content-disposition", "attachment; filename=" +
                    new String(fileName.getBytes(StandardCharsets.UTF_8), StandardCharsets.ISO_8859_1));
            out = response.getOutputStream();
            bos = new BufferedOutputStream(out);
            ExcelWriter excelWriter = EasyExcel.write(bos).withTemplate(inputStream).build();
            WriteSheet writeSheet = EasyExcel.writerSheet().build();
            excelWriter.fill(dataList, writeSheet);
            excelWriter.finish();
            bos.flush();
        } catch (Exception e) {
            log.error("DeviceMonitorController writerExcelFormTemplate Exception:", e);
            try {
                org.springframework.core.io.Resource resource = new ClassPathResource("templates" + File.separator + "device_ino_template.xlsx");
                InputStream inputStream = resource.getInputStream();
                response.setContentType("application/vnd.ms-excel");
                response.setCharacterEncoding("utf-8");
                String fileName = URLEncoder.encode("device_ino_template.xlsx", "utf-8");
                response.setHeader("Content-disposition", "attachment; filename=" +
                        new String(fileName.getBytes(StandardCharsets.UTF_8), StandardCharsets.ISO_8859_1));
                out = response.getOutputStream();
                bos = new BufferedOutputStream(out);
                ExcelWriter excelWriter = EasyExcel.write(bos).withTemplate(inputStream).build();
                WriteSheet writeSheet = EasyExcel.writerSheet().build();
                excelWriter.fill(Lists.newArrayList(), writeSheet);
                excelWriter.finish();
                bos.flush();
            } catch (IOException ex) {
                log.error("DeviceMonitorController writerExcelFormTemplate catch Exception:", e);
                throw new CommonException(CommonErrorCodeEnum.UNKNOWN_ERROR, "系统错误, 导出失败!");
            }
        }
    }
}
