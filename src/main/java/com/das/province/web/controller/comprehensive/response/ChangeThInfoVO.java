package com.das.province.web.controller.comprehensive.response;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2023/7/19
 */
@Data
public class ChangeThInfoVO implements Serializable {

    private static final long serialVersionUID = 3430624266015619771L;

    /**
     * 公司名称
     */
    private String museumName;

    /**
     * 公司编码
     */
    private String uniqueCode;

    /**
     * 所在区域(1展厅,2库房,3其他)
     */
    private Byte regionName;

    /**
     * 设备编码
     */
    private String deviceCode;

    /**
     * 设备位置
     */
    private String deviceAddress;

    /**
     * 最低温湿度阈值
     */
    private String minTh;

    /**
     * 最高温湿度阈值
     */
    private String maxTh;

    /**
     * 温湿度值
     */
    private String thValue;

    /**
     * 温度异常程度: 1:轻度, 2:中度, 3:严重
     */
    private String thExceptionDegree;

    /**
     * 上报时间
     */
    private Date reportTime;
}
