package com.das.province.web.controller.usercenter.role.request;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

@Data
public class EditRoleReq implements Serializable {
    private static final long serialVersionUID = -1589319947389100036L;
    @NotNull(message = "角色id不能为空")
    private Long roleId;
    @NotNull(message = "角色名称不能为空")
    private String roleName;

    @NotNull(message = "权限编码不能为空")
    private List<String> permissionCodeList;
}