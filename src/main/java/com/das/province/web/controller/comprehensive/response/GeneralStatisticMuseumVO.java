package com.das.province.web.controller.comprehensive.response;

import lombok.Data;


@Data
public class GeneralStatisticMuseumVO {

    /**
     * 博物馆id
     */
    private String museumId;

    /**
     * 博物馆名称
     */
    private String museumName;

    /**
     * 藏品总数
     */
    private Integer collectionNum;

    /**
     * 定级藏品
     */
    private Integer scoreNum;

    /**
     * 未定级藏品
     */
    private Integer unclassifiedNum;

    /**
     * 数据库公开比例
     */
    private String displayRatio;

    /**
     * 二维数字化占比
     */
    private String twoModelRatio;

    /**
     * 三维数字化占比
     */
    private String threeModelRatio;

    /**
     * 观众总量
     */
    private Integer totalVisitorNum;

    /**
     * 线上访问总量
     */
    private Integer onlineNum;

    /**
     * 线下入馆观众
     */
    private Integer offlineNum;

    /**
     * 入馆个人
     */
    private Integer personalNum;

    /**
     * 入馆团队个数
     */
    private Integer teamNum;

    /**
     * 入馆团队人数
     */
    private Integer teamPeopleNum;

    /**
     * 预约观众
     */
    private Integer reserveNum;

    /**
     * 成年观众
     */
    private Integer adultNum;

    /**
     * 未成年观众
     */
    private Integer childrenNum;

    /**
     * 省内观众
     */
    private Integer provinceNum;

    /**
     * 省外观众
     */
    private Integer outsideNum;

    /**
     * 境外观众
     */
    private Integer abroadNum;

    /**
     * 男性观众
     */
    private Integer manNum;

    /**
     * 女性观众
     */
    private Integer womanNum;

}
