package com.das.province.web.controller.bo;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class LoginUserInfoBO implements Serializable {
    private static final long serialVersionUID = 69691779260725977L;
    private Long userId;
    private Long companyId;
    private String userName;
    private String loginAccount;
    private String password;
    private String phone;
    private Long departmentId;
    private Long positionId;
    private String postCode;
    private Byte accountStatus;
    private Byte isDelete;
    private Long creator;
    private Long modifier;
    private Date gmtCreate;
    private Date gmtModified;
}