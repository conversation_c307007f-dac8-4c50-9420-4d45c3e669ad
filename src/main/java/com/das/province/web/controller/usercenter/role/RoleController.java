package com.das.province.web.controller.usercenter.role;

import com.das.province.service.biz.user.RoleService;
import com.das.province.service.biz.user.action.*;
import com.das.province.service.biz.user.dto.RoleDTO;
import com.das.province.service.biz.user.dto.RolePageDTO;
import com.das.province.service.biz.user.dto.RolePermissionDTO;
import com.das.province.service.enums.AccountStatusEnum;
import com.das.province.common.bo.PlainResult;
import com.das.province.common.bo.SimplePageInfo;
import com.das.province.common.exception.CommonErrorCodeEnum;
import com.das.province.common.exception.CommonException;
import com.das.province.common.utils.BeanCopyUtils;
import com.das.province.web.aop.annoation.PlainResultWrapper;
import com.das.province.web.controller.bo.LoginUserInfoBO;
import com.das.province.web.controller.usercenter.role.request.*;
import com.das.province.web.controller.usercenter.role.response.RolePermissionVO;
import com.das.province.web.controller.usercenter.role.response.RoleVO;
import com.das.province.web.controller.utils.LoginUserInfoUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import java.util.List;
import java.util.Objects;

@Slf4j
@PlainResultWrapper
@RestController
@RequestMapping("/province/setting/role")
public class RoleController {

    @Resource
    private RoleService roleService;

    @PostMapping("/add")
    public PlainResult<Long> addRole(@RequestBody AddRoleReq addRoleReq) {
        LoginUserInfoBO userBO = LoginUserInfoUtils.getLoginUserInfo();
        AddRoleAction action = new AddRoleAction();
        action.setCompanyId(userBO.getCompanyId());
        action.setRoleName(addRoleReq.getRoleName());
        action.setCreator(userBO.getUserId());
        action.setModifier(userBO.getUserId());
        action.setPermissionCodeList(addRoleReq.getPermissionCodeList());
        Long roleId = this.roleService.addRole(action);
        return PlainResult.success(roleId, "新增成功");
    }

    @PostMapping("/edit")
    public PlainResult<Long> editRoleById(@RequestBody EditRoleReq editRoleReq) {
        LoginUserInfoBO userBO = LoginUserInfoUtils.getLoginUserInfo();
        EditRoleByIdAction action = new EditRoleByIdAction();
        action.setCompanyId(userBO.getCompanyId());
        action.setRoleId(editRoleReq.getRoleId());
        action.setRoleName(editRoleReq.getRoleName());
        action.setPermissionCodeList(editRoleReq.getPermissionCodeList());
        action.setModifier(userBO.getModifier());
        this.roleService.editRoleById(action);
        return PlainResult.success(editRoleReq.getRoleId(), "编辑成功");
    }

    @PostMapping("/del")
    public PlainResult<Boolean> delByRoleId(@RequestParam(value = "roleId") Long roleId) {
        LoginUserInfoBO userBO = LoginUserInfoUtils.getLoginUserInfo();
        DelRoleByIdAction action = new DelRoleByIdAction();
        action.setRoleId(roleId);
        action.setCompanyId(userBO.getCompanyId());
        action.setModifier(userBO.getUserId());
        this.roleService.delRoleById(action);
        return PlainResult.success(true, "删除成功");
    }

    @PostMapping("/batchDel")
    public PlainResult<Boolean> delByRoleIds(@RequestParam(value = "roleIds") List<Long> roleIds) {
        LoginUserInfoBO userBO = LoginUserInfoUtils.getLoginUserInfo();
        BatchDelRoleByIdsAction action = new BatchDelRoleByIdsAction();
        action.setRoleIds(roleIds);
        action.setCompanyId(userBO.getCompanyId());
        action.setModifier(userBO.getUserId());
        this.roleService.batchDelRoleByIds(action);
        return PlainResult.success(true, "删除成功");
    }

    @PostMapping("/editStatus")
    public PlainResult<Boolean> editRoleStatusByRoleId(@RequestBody EditRoleStatusByRoleIdReq req) {
        LoginUserInfoBO userBO = LoginUserInfoUtils.getLoginUserInfo();
        if (AccountStatusEnum.禁用.getCode() == req.getStatus().intValue()) {
            DisabledRoleAction action = new DisabledRoleAction();
            action.setCompanyId(userBO.getCompanyId());
            action.setRoleId(req.getRoleId());
            action.setModifier(userBO.getUserId());
            this.roleService.disabledRole(action);
        } else if (AccountStatusEnum.启用.getCode() == req.getStatus().intValue()) {
            EnableRoleAction action = new EnableRoleAction();
            action.setCompanyId(userBO.getCompanyId());
            action.setRoleId(req.getRoleId());
            action.setModifier(userBO.getUserId());
            this.roleService.enableRole(action);
        } else {
            throw new CommonException(CommonErrorCodeEnum.VALIDATE_ERROR, "不存在角色状态操作类型");
        }
        return PlainResult.success(true, "修改成功");
    }

    @PostMapping("/batchEditStatus")
    public PlainResult<Boolean> batchEditRoleStatusByRoleIds(@RequestBody BatchEditRoleStatusByRoleIdsReq req) {
        LoginUserInfoBO userBO = LoginUserInfoUtils.getLoginUserInfo();
        if (AccountStatusEnum.禁用.getCode() == req.getStatus().intValue()) {
            BatchDisabledRoleAction action = new BatchDisabledRoleAction();
            action.setCompanyId(userBO.getCompanyId());
            action.setRoleIds(req.getRoleIds());
            action.setModifier(userBO.getUserId());
            this.roleService.batchDisabledRole(action);
        } else if (AccountStatusEnum.启用.getCode() == req.getStatus().intValue()) {
            BatchEnableRoleAction action = new BatchEnableRoleAction();
            action.setCompanyId(userBO.getCompanyId());
            action.setRoleIds(req.getRoleIds());
            action.setModifier(userBO.getUserId());
            this.roleService.batchEnableRole(action);
        } else {
            throw new CommonException(CommonErrorCodeEnum.VALIDATE_ERROR, "不存在角色状态操作类型");
        }
        return PlainResult.success(true, "修改成功");
    }

    @GetMapping("/pageList")
    public PlainResult<SimplePageInfo<RolePageDTO>> getPageByConditionQuery(@RequestParam(value = "roleName", required = false) String roleName,
                                                                            @RequestParam(value = "sortBy", defaultValue = "gmt_create desc") String sortBy,
                                                                            @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
                                                                            @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize) {
        LoginUserInfoBO userBO = LoginUserInfoUtils.getLoginUserInfo();
        QueryRolePageByCondition action = new QueryRolePageByCondition();
        action.setCompanyId(userBO.getCompanyId());
        action.setRoleName(roleName);
        action.setSortBy(sortBy);
        action.setPageNum(pageNum);
        action.setPageSize(pageSize);
        SimplePageInfo<RolePageDTO> pageInfo = this.roleService.queryPageByCondition(action);
        return PlainResult.success(pageInfo, "获取成功");
    }

    @GetMapping("/info")
    public PlainResult<RoleVO> getInfoByRoleIdId(@RequestParam(value = "roleId") Long roleId) {
        LoginUserInfoBO userBO = LoginUserInfoUtils.getLoginUserInfo();
        RoleDTO roleDTO = this.roleService.queryByRoleId(userBO.getCompanyId(), roleId);
        if (Objects.isNull(roleDTO)) {
            return PlainResult.success(null, "获取成功");
        }
        RoleVO roleVO = BeanCopyUtils.copyByJSON(roleDTO, RoleVO.class);
        return PlainResult.success(roleVO, "获取成功");
    }

    @GetMapping("/permission")
    public PlainResult<RolePermissionVO> getPermissionByRoleIdId(@RequestParam(value = "roleId") Long roleId) {
        LoginUserInfoBO userBO = LoginUserInfoUtils.getLoginUserInfo();
        RolePermissionDTO rolePermissionDTO = this.roleService.queryPermissionByRoleId(userBO.getCompanyId(), roleId);
        if (Objects.isNull(rolePermissionDTO)) {
            return PlainResult.success(null, "获取成功");
        }
        RolePermissionVO rolePermissionVO = BeanCopyUtils.copyByJSON(rolePermissionDTO, RolePermissionVO.class);
        return PlainResult.success(rolePermissionVO, "获取成功");
    }

    @PostMapping("/roleAuth")
    public PlainResult<Boolean> roleAuthByRoleId(@RequestBody RoleAuthByRoleIdReq req) {
        LoginUserInfoBO userBO = LoginUserInfoUtils.getLoginUserInfo();
        RoleAuthByRoleIdAction action = new RoleAuthByRoleIdAction();
        action.setCompanyId(userBO.getCompanyId());
        action.setRoleId(req.getRoleId());
        action.setModifier(userBO.getUserId());
        action.setPermissionCodeList(req.getPermissionCodeList());
        this.roleService.roleAuthByRoleId(action);
        return PlainResult.success(true, "授权成功");
    }

    @GetMapping("/listRole")
    public PlainResult<List<RoleDTO>> listRole() {
        LoginUserInfoBO userBO = LoginUserInfoUtils.getLoginUserInfo();
        List<RoleDTO> roleDTOList = this.roleService.listRole(userBO.getCompanyId());
        return PlainResult.success(roleDTOList, "获取成功");
    }
}
