package com.das.province.web.controller.common.classification.request;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
public class EditClassificationReq implements Serializable {
    private static final long serialVersionUID = 2861805886714080029L;
    @NotNull(message = "分类id不能为空")
    private Long classificationId;
    private String bizCode;
    private Long bizNumber;
    private String name;
    @NotNull(message = "分类父节点不能为空")
    private Long parentId;
    private Integer sortIndex;
    private String permission;
    private Byte enabledStatus;
    private Byte leafNode;
    private String remark;
    private Byte bizType;
}
