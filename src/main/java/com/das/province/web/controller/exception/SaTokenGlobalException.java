package com.das.province.web.controller.exception;


import cn.dev33.satoken.exception.DisableServiceException;
import cn.dev33.satoken.exception.NotLoginException;
import cn.dev33.satoken.exception.NotPermissionException;
import cn.dev33.satoken.exception.NotRoleException;
import com.das.province.web.controller.constant.BizConstant;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

import static org.springframework.http.MediaType.APPLICATION_JSON_UTF8_VALUE;

@ControllerAdvice
@Slf4j
public class SaTokenGlobalException {

    @ExceptionHandler
    public void handlerException(Exception e, HttpServletRequest request, HttpServletResponse response)
            throws Exception {
        log.error("全局异常---------------", e);
        if (e instanceof NotLoginException) {
            NotLoginException ex = (NotLoginException) e;
            writeOutJson(response, BizConstant.USER_NOT_EXIST_JSON_MESSAGE);
        } else if (e instanceof NotRoleException) {
            NotRoleException ex = (NotRoleException) e;
            writeOutJson(response, BizConstant.USER_ROLE_NOT_EXIST_MESSAGE);
        } else if (e instanceof NotPermissionException) {
            NotPermissionException ex = (NotPermissionException) e;
            writeOutJson(response, BizConstant.USER_PERMISSION_NOT_EXIST_MESSAGE);
        } else if (e instanceof DisableServiceException) {
            DisableServiceException ee = (DisableServiceException) e;
            writeOutJson(response, BizConstant.USER_ACCOUNT_DISABLED_MESSAGE);
        } else {
            writeOutJson(response, BizConstant.UNKNOWN_ERROR_JSON_MESSAGE);
        }
    }

    private void writeOutJson(HttpServletResponse response, String jsonMsg) throws IOException {
        response.setStatus(200);
        response.setContentType(APPLICATION_JSON_UTF8_VALUE);
        response.getWriter().append(jsonMsg);
    }
}
