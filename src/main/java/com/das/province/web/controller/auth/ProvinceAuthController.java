package com.das.province.web.controller.auth;

import com.alibaba.fastjson.JSON;
import com.das.province.common.bo.PlainResult;
import com.das.province.common.utils.RasSecureUtil;
import com.das.province.service.biz.authentication.ProvinceAuthService;
import com.das.province.service.biz.authentication.action.GenerateSessionKeyAction;
import com.das.province.web.aop.annoation.PlainResultWrapper;
import com.das.province.web.controller.auth.request.GetSessionKeyReq;
import com.das.province.web.controller.auth.request.SendKeyReq;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2023/3/17
 */
@Slf4j
@PlainResultWrapper
@RestController
@RequestMapping("/province/auth")
public class ProvinceAuthController {

    @Resource
    private ProvinceAuthService provinceAuthService;

    /**
     * 获取sessionKey
     * @param req req
     * @return key
     */
    @PostMapping(value = "/sessionKey")
    public PlainResult<String> getSessionKey(@RequestBody SendKeyReq req) {
        if (StringUtils.isBlank(req.getKeyBody())) {
            return PlainResult.success(null, "获取成功");
        }
        String jsonBody = RasSecureUtil.privateDecrypt(req.getKeyBody());
        GenerateSessionKeyAction action = JSON.parseObject(jsonBody, GenerateSessionKeyAction.class);
        Long serverPerMaster = this.provinceAuthService.generateSessionKey(action);
        return PlainResult.success(RasSecureUtil.privateEncrypt(String.valueOf(serverPerMaster)), "获取成功");
    }
}
