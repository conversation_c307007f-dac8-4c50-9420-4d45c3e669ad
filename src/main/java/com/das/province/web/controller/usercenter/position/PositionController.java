package com.das.province.web.controller.usercenter.position;

import com.das.province.service.biz.user.PositionService;
import com.das.province.service.biz.user.action.*;
import com.das.province.service.biz.user.dto.PositionDTO;
import com.das.province.service.biz.user.dto.PositionPageDTO;
import com.das.province.common.bo.PlainResult;
import com.das.province.common.bo.SimplePageInfo;
import com.das.province.common.utils.BeanCopyUtils;
import com.das.province.web.aop.annoation.PlainResultWrapper;
import com.das.province.web.controller.bo.LoginUserInfoBO;
import com.das.province.web.controller.usercenter.position.request.AddPositionReq;
import com.das.province.web.controller.usercenter.position.request.EditPositionReq;
import com.das.province.web.controller.usercenter.position.response.PositionVO;
import com.das.province.web.controller.usercenter.position.response.UserPositionVO;
import com.das.province.web.controller.utils.LoginUserInfoUtils;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import java.util.List;
import java.util.Objects;

@Slf4j
@PlainResultWrapper
@RestController
@RequestMapping("/province/setting/position")
public class PositionController {

    @Resource
    private PositionService positionService;

    @PostMapping("/add")
    public PlainResult<Long> addPosition(@RequestBody AddPositionReq addPositionReq,
                                         HttpServletRequest request, HttpSession session) {
        LoginUserInfoBO userBO = LoginUserInfoUtils.getLoginUserInfo();
        AddPositionAction action = new AddPositionAction();
        action.setCompanyId(userBO.getCompanyId());
        action.setPositionName(addPositionReq.getPositionName());
        action.setDepartmentId(addPositionReq.getDepartmentId());
        action.setCreator(userBO.getUserId());
        action.setModifier(userBO.getUserId());
        Long positionId = this.positionService.addPosition(action);
        return PlainResult.success(positionId, "新增成功");
    }

    @PostMapping("/edit")
    public PlainResult<Long> editPositionById(@RequestBody EditPositionReq editPositionReq,
                                              HttpServletRequest request, HttpSession session) {
        LoginUserInfoBO userBO = LoginUserInfoUtils.getLoginUserInfo();
        EditPositionByIdAction action = new EditPositionByIdAction();
        action.setCompanyId(userBO.getCompanyId());
        action.setPositionId(editPositionReq.getPositionId());
        action.setPositionName(editPositionReq.getPositionName());
        action.setModifier(userBO.getUserId());
        this.positionService.editPositionById(action);
        return PlainResult.success(editPositionReq.getPositionId(), "编辑成功");
    }

    @PostMapping("/del")
    public PlainResult<Boolean> delByPositionId(@RequestParam(value = "positionId") Long positionId,
                                                HttpServletRequest request, HttpSession session) {
        LoginUserInfoBO userBO = LoginUserInfoUtils.getLoginUserInfo();
        DelPositionByIdAction action = new DelPositionByIdAction();
        action.setPositionId(positionId);
        action.setCompanyId(userBO.getCompanyId());
        action.setModifier(userBO.getUserId());
        this.positionService.delPositionById(action);
        return PlainResult.success(true, "删除成功");
    }

    @PostMapping("/batchDel")
    public PlainResult<Boolean> delByPositionIds(@RequestParam(value = "positionIds") List<Long> positionIds,
                                                 HttpServletRequest request, HttpSession session) {
        LoginUserInfoBO userBO = LoginUserInfoUtils.getLoginUserInfo();
        DelPositionByIdsAction action = new DelPositionByIdsAction();
        action.setPositionIds(positionIds);
        action.setCompanyId(userBO.getCompanyId());
        action.setModifier(userBO.getUserId());
        this.positionService.batchDelPositionByIds(action);
        return PlainResult.success(true, "删除成功");
    }

    @GetMapping("/pageList")
    public PlainResult<SimplePageInfo<PositionPageDTO>> getPageByConditionQuery(@RequestParam(value = "departmentId",required = false) Long departmentId,
                                                                                @RequestParam(value = "sortBy", defaultValue = "gmt_create desc") String sortBy,
                                                                                @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
                                                                                @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize,
                                                                                HttpServletRequest request, HttpSession session) {
        LoginUserInfoBO userBO = LoginUserInfoUtils.getLoginUserInfo();
        QueryPositionPageByConditionAction action = new QueryPositionPageByConditionAction();
        action.setDepartmentId(departmentId);
        action.setSortBy(sortBy);
        action.setCompanyId(userBO.getCompanyId());
        action.setPageSize(pageSize);
        action.setPageNum(pageNum);
        SimplePageInfo<PositionPageDTO> pageInfo = this.positionService.queryPageByCondition(action);
        return PlainResult.success(pageInfo, "获取成功");
    }

    @GetMapping("/info")
    public PlainResult<PositionVO> getDetailByCollectionAncientBookId(@RequestParam(value = "positionId") Long positionId,
                                                                      HttpServletRequest request, HttpSession session) {
        LoginUserInfoBO userBO = LoginUserInfoUtils.getLoginUserInfo();
        PositionDTO positionDTO = this.positionService.queryByPositionId(userBO.getCompanyId(), positionId);
        if (Objects.isNull(positionDTO)) {
            return PlainResult.success(null, "获取成功");
        }
        PositionVO positionVO = BeanCopyUtils.copyByJSON(positionDTO, PositionVO.class);
        positionVO.setPositionId(positionId);
        return PlainResult.success(positionVO, "获取成功");
    }

    @GetMapping("/departmentPosition")
    public PlainResult<List<UserPositionVO>> departmentPosition(@RequestParam(value = "departmentId") Long departmentId,
                                                                HttpServletRequest request, HttpSession session) {
        LoginUserInfoBO userBO = LoginUserInfoUtils.getLoginUserInfo();
        List<PositionDTO> positionDTOList = this.positionService.queryByDepartmentId(userBO.getCompanyId(), departmentId);
        if (CollectionUtils.isEmpty(positionDTOList)) {
            return PlainResult.success(Lists.newArrayList(), "获取成功");
        }
        List<UserPositionVO> userPositionVOList = BeanCopyUtils.copyArrayByJSON(positionDTOList, UserPositionVO.class);
        return PlainResult.success(userPositionVOList, "获取成功");
    }
}
