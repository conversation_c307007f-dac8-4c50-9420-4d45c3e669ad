package com.das.province.web.controller.safe;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.das.province.common.bo.PlainResult;
import com.das.province.common.bo.SimplePageInfo;
import com.das.province.common.exception.CommonErrorCodeEnum;
import com.das.province.common.exception.CommonException;
import com.das.province.service.biz.safe.DeviceMonitorExceptionService;
import com.das.province.service.biz.safe.dto.*;
import com.das.province.web.aop.annoation.PlainResultWrapper;
import com.das.province.web.controller.bo.LoginUserInfoBO;
import com.das.province.web.controller.utils.LoginUserInfoUtils;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.ClassPathResource;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.io.BufferedOutputStream;
import java.io.File;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.List;

/**
 * 异常统计
 * <AUTHOR>
 * @date 2023/7/11
 */
@Slf4j
@PlainResultWrapper
@RestController
@RequestMapping("/device/monitor/exception")
public class DeviceMonitorExceptionController {

    @Resource
    private DeviceMonitorExceptionService deviceMonitorExceptionService;

    /**
     * 查询监控设备异常详情
     *
     * @param museumId        博物馆code
     * @param regionCode      地区code
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param statisticsWay  0:今日,1:近一周,2:近一月,3:近一年,5:自定义
     * @param request
     * @param session
     * @return
     */
    @GetMapping("/info")
    public PlainResult<DeviceMonitorExceptionInfoDTO> getDeviceMonitorExceptionInfo(@RequestParam(value = "museumId", required = false) String museumId,
                                                                                    @RequestParam(value = "regionCode", required = false) String regionCode,
                                                                                    @RequestParam(value = "startTime", required = false) String startTime,
                                                                                    @RequestParam(value = "endTime", required = false) String endTime,
                                                                                    @RequestParam(value = "statisticsWay") Integer statisticsWay,
                                                                                    HttpServletRequest request, HttpSession session) {
        try {
            LoginUserInfoBO userBO = LoginUserInfoUtils.getLoginUserInfo();
            DeviceMonitorExceptionInfoDTO result = this.deviceMonitorExceptionService.getDeviceMonitorExceptionInfo(museumId, regionCode, startTime, endTime, statisticsWay);
            return PlainResult.success(result, "获取成功");
        } catch (Exception e) {
            log.error("DeviceMonitorExceptionController getDeviceMonitorExceptionInfo Exception:", e);
            return PlainResult.success(null, "获取成功");
        }
    }

    /**
     * 查询监控设备异常趋势
     *
     * @param museumId        博物馆code
     * @param regionCode      地区code
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param statisticsWay  0:今日,1:近一周,2:近一月,3:近一年,5:自定义
     * @param request
     * @param session
     * @return
     */
    @GetMapping("/trend")
    public PlainResult<List<DeviceMonitorExceptionTrend>> getDeviceMonitorExceptionTrend(@RequestParam(value = "museumId", required = false) String museumId,
                                                            @RequestParam(value = "regionCode", required = false) String regionCode,
                                                            @RequestParam(value = "startTime", required = false) String startTime,
                                                            @RequestParam(value = "endTime", required = false) String endTime,
                                                            @RequestParam(value = "statisticsWay") Integer statisticsWay,
                                                            HttpServletRequest request, HttpSession session) {
        try {
            LoginUserInfoBO userBO = LoginUserInfoUtils.getLoginUserInfo();
            List<DeviceMonitorExceptionTrend> result = this.deviceMonitorExceptionService.getDeviceMonitorExceptionTrend(museumId, regionCode, startTime, endTime, statisticsWay);
            return PlainResult.success(result, "获取成功");
        } catch (Exception e) {
            log.error("DeviceMonitorExceptionController getDeviceMonitorExceptionTrend Exception:", e);
            return PlainResult.success(Lists.newArrayList(), "获取成功");
        }
    }

    /**
     * 查询监控设备温湿度异常分页
     * @param museumId 博物馆code
     * @param regionCode 地区code
     * @param regionName 所在区域
     * @param deviceAddress 设备位置
     * @param exceptionType 异常类型 1:温度异常, 2:湿度异常, 3:离线异常
     * @param exceptionDegree 异常程度 1:轻度, 2:中度, 3:严重
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param statisticsWay 0:今日,1:近一周,2:近一月,3:近一年,5:自定义
     * @param pageNum
     * @param pageSize
     * @param request
     * @param session
     * @return
     */
    @GetMapping("/pageMonitorThExceptionList")
    public PlainResult<SimplePageInfo<DeviceMonitorThExceptionDTO>> pageDeviceMonitorThException(@RequestParam(value = "museumId", required = false) String museumId,
                                                                                                 @RequestParam(value = "regionCode", required = false) String regionCode,
                                                                                                 @RequestParam(value = "regionName", required = false) String regionName,
                                                                                                 @RequestParam(value = "deviceAddress", required = false) String deviceAddress,
                                                                                                 @RequestParam(value = "exceptionType", required = false) String exceptionType,
                                                                                                 @RequestParam(value = "exceptionDegree", required = false) String exceptionDegree,
                                                                                                 @RequestParam(value = "startTime", required = false) String startTime,
                                                                                                 @RequestParam(value = "endTime", required = false) String endTime,
                                                                                                 @RequestParam(value = "statisticsWay") Integer statisticsWay,
                                                                                                 @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
                                                                                                 @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize,
                                                                                                 HttpServletRequest request, HttpSession session) {
        try {
            LoginUserInfoBO userBO = LoginUserInfoUtils.getLoginUserInfo();
            SimplePageInfo<DeviceMonitorThExceptionDTO> pageInfo = this.deviceMonitorExceptionService.pageDeviceMonitorThException(museumId, regionCode, regionName, deviceAddress, exceptionType,
                    exceptionDegree, startTime, endTime, statisticsWay, pageNum, pageSize);
            return PlainResult.success(pageInfo, "获取成功");
        } catch (Exception e) {
            log.error("DeviceMonitorExceptionController pageDeviceMonitorThException Exception:", e);
            return PlainResult.success(new SimplePageInfo<>(), "获取成功");
        }
    }

    /**
     * 查询监控设备温湿度异常详情
     * @param museumId 博物馆code
     * @param regionCode 地区code
     * @param regionName 所在区域
     * @param deviceAddress 设备位置
     * @param exceptionType 异常类型 1:温度异常, 2:湿度异常, 3:离线异常
     * @param exceptionDegree 异常程度 1:轻度, 2:中度, 3:严重
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param statisticsWay 0:今日,1:近一周,2:近一月,3:近一年,5:自定义
     * @param request
     * @param session
     * @return
     */
    @GetMapping("/getMonitorThExceptionInfo")
    public PlainResult<MonitorThExceptionInfoDTO> getMonitorThExceptionInfo(@RequestParam(value = "museumId", required = false) String museumId,
                                                                            @RequestParam(value = "regionCode", required = false) String regionCode,
                                                                            @RequestParam(value = "regionName", required = false) String regionName,
                                                                            @RequestParam(value = "deviceAddress", required = false) String deviceAddress,
                                                                            @RequestParam(value = "exceptionType", required = false) String exceptionType,
                                                                            @RequestParam(value = "exceptionDegree", required = false) String exceptionDegree,
                                                                            @RequestParam(value = "startTime", required = false) String startTime,
                                                                            @RequestParam(value = "endTime", required = false) String endTime,
                                                                            @RequestParam(value = "statisticsWay") Integer statisticsWay,
                                                                            HttpServletRequest request, HttpSession session) {
        try {
            LoginUserInfoBO userBO = LoginUserInfoUtils.getLoginUserInfo();
            MonitorThExceptionInfoDTO result = this.deviceMonitorExceptionService.getMonitorThExceptionInfo(museumId, regionCode, regionName, deviceAddress,
                    exceptionType, exceptionDegree, startTime, endTime, statisticsWay);
            return PlainResult.success(result, "获取成功");
        } catch (Exception e) {
            log.error("DeviceMonitorExceptionController getMonitorThExceptionInfo Exception:", e);
            return PlainResult.success(null, "获取成功");
        }
    }


    /**
     * 查询监控设备离线异常分页
     * @param museumId 博物馆code
     * @param regionCode 地区code
     * @param regionName 所在区域
     * @param deviceAddress 设备位置
     * @param exceptionDegree 异常程度 1:轻度, 2:中度, 3:严重
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param statisticsWay 0:今日,1:近一周,2:近一月,3:近一年,5:自定义
     * @param pageNum
     * @param pageSize
     * @param request
     * @param session
     * @return
     */
    @GetMapping("/pageMonitorOfflineExceptionList")
    public PlainResult<SimplePageInfo<MonitorOfflineExceptionDTO>> pageMonitorOfflineExceptionList(@RequestParam(value = "museumId", required = false) String museumId,
                                                                                                   @RequestParam(value = "regionCode", required = false) String regionCode,
                                                                                                   @RequestParam(value = "regionName", required = false) String regionName,
                                                                                                   @RequestParam(value = "deviceAddress", required = false) String deviceAddress,
                                                                                                   @RequestParam(value = "exceptionDegree", required = false) String exceptionDegree,
                                                                                                   @RequestParam(value = "startTime", required = false) String startTime,
                                                                                                   @RequestParam(value = "endTime", required = false) String endTime,
                                                                                                   @RequestParam(value = "statisticsWay") Integer statisticsWay,
                                                                                                   @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
                                                                                                   @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize,
                                                                                                   HttpServletRequest request, HttpSession session) {
        try {
            LoginUserInfoBO userBO = LoginUserInfoUtils.getLoginUserInfo();
            SimplePageInfo<MonitorOfflineExceptionDTO> pageInfo = this.deviceMonitorExceptionService.pageMonitorOfflineExceptionList(museumId, regionCode, regionName, deviceAddress,
                    exceptionDegree, startTime, endTime, statisticsWay, pageNum, pageSize);
            return PlainResult.success(pageInfo, "获取成功");
        } catch (Exception e) {
            log.error("DeviceMonitorExceptionController pageMonitorOfflineExceptionList Exception:", e);
            return PlainResult.success(new SimplePageInfo<>(), "获取成功");
        }
    }

    /**
     * 查询监控设备离线异常详情
     * @param museumId 博物馆code
     * @param regionCode 地区code
     * @param regionName 所在区域
     * @param deviceAddress 设备位置
     * @param exceptionDegree 异常程度 1:轻度, 2:中度, 3:严重
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param statisticsWay 0:今日,1:近一周,2:近一月,3:近一年,5:自定义
     * @param request
     * @param session
     * @return
     */
    @GetMapping("/getMonitorOfflineException")
    public PlainResult<MonitorOfflineExceptionInfoDTO> getMonitorOfflineExceptionInfo(@RequestParam(value = "museumId", required = false) String museumId,
                                                                                      @RequestParam(value = "regionCode", required = false) String regionCode,
                                                                                      @RequestParam(value = "regionName", required = false) String regionName,
                                                                                      @RequestParam(value = "deviceAddress", required = false) String deviceAddress,
                                                                                      @RequestParam(value = "exceptionDegree", required = false) String exceptionDegree,
                                                                                      @RequestParam(value = "startTime", required = false) String startTime,
                                                                                      @RequestParam(value = "endTime", required = false) String endTime,
                                                                                      @RequestParam(value = "statisticsWay") Integer statisticsWay,
                                                                                      HttpServletRequest request, HttpSession session) {
        try {
            LoginUserInfoBO userBO = LoginUserInfoUtils.getLoginUserInfo();
            MonitorOfflineExceptionInfoDTO result = this.deviceMonitorExceptionService.getMonitorOfflineExceptionInfo(museumId, regionCode, regionName, deviceAddress,
                    exceptionDegree, startTime, endTime, statisticsWay);
            return PlainResult.success(result, "获取成功");
        } catch (Exception e) {
            log.error("DeviceMonitorExceptionController getMonitorOfflineExceptionInfo Exception:", e);
            return PlainResult.success(null, "获取成功");
        }
    }

    /**
     * 导出温湿度异常信息
     * @param museumId 博物馆code
     * @param regionCode 地区code
     * @param regionName 所在区域
     * @param deviceAddress 设备位置
     * @param exceptionType 异常类型 1:温度异常, 2:湿度异常, 3:离线异常
     * @param exceptionDegree 异常程度 1:轻度, 2:中度, 3:严重
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param statisticsWay 0:今日,1:近一周,2:近一月,3:近一年,5:自定义
     * @param response
     */
    @GetMapping("/monitorThExceptionExport")
    public void monitorThExceptionExport(@RequestParam(value = "museumId", required = false) String museumId,
                                         @RequestParam(value = "regionCode", required = false) String regionCode,
                                         @RequestParam(value = "regionName", required = false) String regionName,
                                         @RequestParam(value = "deviceAddress", required = false) String deviceAddress,
                                         @RequestParam(value = "exceptionType", required = false) String exceptionType,
                                         @RequestParam(value = "exceptionDegree", required = false) String exceptionDegree,
                                         @RequestParam(value = "startTime", required = false) String startTime,
                                         @RequestParam(value = "endTime", required = false) String endTime,
                                         @RequestParam(value = "statisticsWay") Integer statisticsWay, HttpServletResponse response) {
        OutputStream out;
        BufferedOutputStream bos;
        try {
            List<DeviceMonitorThExceptionDTO> dataList = this.deviceMonitorExceptionService.monitorThExceptionExport(museumId, regionCode, regionName, deviceAddress,
                    exceptionType, exceptionDegree, startTime, endTime, statisticsWay);
            org.springframework.core.io.Resource resource = new ClassPathResource("templates" + File.separator + "device_th_ex_template.xlsx");
            InputStream inputStream = resource.getInputStream();
            response.setContentType("application/vnd.ms-excel");
            response.setCharacterEncoding("utf-8");
            String fileName = URLEncoder.encode("device_th_ex_template.xlsx", "utf-8");
            response.setHeader("Content-disposition", "attachment; filename=" +
                    new String(fileName.getBytes(StandardCharsets.UTF_8), StandardCharsets.ISO_8859_1));
            out = response.getOutputStream();
            bos = new BufferedOutputStream(out);
            ExcelWriter excelWriter = EasyExcel.write(bos).withTemplate(inputStream).build();
            WriteSheet writeSheet = EasyExcel.writerSheet().build();
            excelWriter.fill(dataList, writeSheet);
            excelWriter.finish();
            bos.flush();
        } catch (Exception e) {
            log.error("DeviceMonitorExceptionController writerExcelFormTemplate Exception:", e);
            throw new CommonException(CommonErrorCodeEnum.UNKNOWN_ERROR, "系统错误, 导出失败!");
        }
    }

    /**
     * 导出离线异常信息
     * @param museumId 博物馆code
     * @param regionCode 地区code
     * @param regionName 所在区域
     * @param deviceAddress 设备位置
     * @param exceptionDegree 异常程度 1:轻度, 2:中度, 3:严重
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param statisticsWay 0:今日,1:近一周,2:近一月,3:近一年,5:自定义
     * @param response
     */
    @GetMapping("/monitorOfflineExceptionExport")
    public void monitorOfflineExceptionExport(@RequestParam(value = "museumId", required = false) String museumId,
                                              @RequestParam(value = "regionCode", required = false) String regionCode,
                                              @RequestParam(value = "regionName", required = false) String regionName,
                                              @RequestParam(value = "deviceAddress", required = false) String deviceAddress,
                                              @RequestParam(value = "exceptionDegree", required = false) String exceptionDegree,
                                              @RequestParam(value = "startTime", required = false) String startTime,
                                              @RequestParam(value = "endTime", required = false) String endTime,
                                              @RequestParam(value = "statisticsWay") Integer statisticsWay, HttpServletResponse response) {
        OutputStream out;
        BufferedOutputStream bos;
        try {
            List<MonitorOfflineExceptionDTO> dataList = this.deviceMonitorExceptionService.monitorOfflineExceptionExport(museumId, regionCode, regionName, deviceAddress,
                    exceptionDegree, startTime, endTime, statisticsWay);
            org.springframework.core.io.Resource resource = new ClassPathResource("templates" + File.separator + "device_off_ex_template.xlsx");
            InputStream inputStream = resource.getInputStream();
            response.setContentType("application/vnd.ms-excel");
            response.setCharacterEncoding("utf-8");
            String fileName = URLEncoder.encode("device_off_ex_template.xlsx", "utf-8");
            response.setHeader("Content-disposition", "attachment; filename=" +
                    new String(fileName.getBytes(StandardCharsets.UTF_8), StandardCharsets.ISO_8859_1));
            out = response.getOutputStream();
            bos = new BufferedOutputStream(out);
            ExcelWriter excelWriter = EasyExcel.write(bos).withTemplate(inputStream).build();
            WriteSheet writeSheet = EasyExcel.writerSheet().build();
            excelWriter.fill(dataList, writeSheet);
            excelWriter.finish();
            bos.flush();
        } catch (Exception e) {
            log.error("DeviceMonitorExceptionController writerExcelFormTemplate Exception:", e);
            throw new CommonException(CommonErrorCodeEnum.UNKNOWN_ERROR, "系统错误, 导出失败!");
        }
    }
}
