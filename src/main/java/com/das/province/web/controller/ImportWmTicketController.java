package com.das.province.web.controller;

import com.das.province.common.bo.PlainResult;
import com.das.province.common.utils.DateUtils;
import com.das.province.infr.mapper.SyncOrderBaseMapper;
import com.das.province.service.task.GetTicketPersonTaskNew;
import com.das.province.web.aop.annoation.PlainResultWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Date;

/**
 * 重新导入伪满票务数据接口
 */

@Slf4j
@PlainResultWrapper
@RestController
@RequestMapping("/province/importWmTicket")
public class ImportWmTicketController {

    @Resource
    private GetTicketPersonTaskNew getTicketPersonTaskNew;

    @Resource
    private SyncOrderBaseMapper syncOrderBaseMapper;

    @GetMapping("/wmHistoryOrderImport")
    public PlainResult<Boolean> wmHistoryOrderImport(@RequestParam(value = "startTime") String startTime,
                                                     @RequestParam(value = "endTime") String endTime) {
        int total = 0;
        log.error("----开始导入历史数据时间段为："+startTime+"--"+endTime);
        Date startDate = DateUtils.parseDateYYYMMDDHHmmss(startTime);
        Date endDate = DateUtils.parseDateYYYMMDDHHmmss(endTime);
        // 删除时间段内历史数据
        syncOrderBaseMapper.deleteWmByTime(startDate,endDate);
        Date start = startDate;
        Date end = DateUtils.parseDateYYYMMDDHHmmss(DateUtils.getOrderDay(start, 0,2));
        while(true){
            if(endDate.before(start)){
                break;
            }
            if(start.before(endDate) && end.after(endDate)){
                end = endDate;
            }
            int num = getTicketPersonTaskNew.setWeiManTicketHistoryData(start.getTime()/1000,end.getTime()/1000);
            log.error("开始导入时间段：" + DateUtils.formatDateYYYMMDDHHmmss(start)+"--" + DateUtils.formatDateYYYMMDDHHmmss(end) + ",数据量："+ num);
            total += num;
            start = DateUtils.parseDateYYYMMDDHHmmss(DateUtils.getOrderDay(start, 1,1));
            end = DateUtils.parseDateYYYMMDDHHmmss(DateUtils.getOrderDay(end, 1,2));
        }
        return PlainResult.success(true, "导入成功:" + total);
    }

    @GetMapping("/lxHistoryOrderImport")
    public PlainResult<Boolean> lxHistoryOrderImport(@RequestParam(value = "startTime") String startTime,
                                                     @RequestParam(value = "endTime") String endTime) {
        int total = 0;
        log.error("----开始导入历史数据时间段为："+startTime+"--"+endTime);
        Date startDate = DateUtils.parseDateYYYMMDDHHmmss(startTime);
        Date endDate = DateUtils.parseDateYYYMMDDHHmmss(endTime);
        // 删除时间段内历史数据
        syncOrderBaseMapper.deleteLxByTime(startDate,endDate);
        Date start = startDate;
        Date end = DateUtils.parseDateYYYMMDDHHmmss(DateUtils.getOrderDay(start, 0,2));
        while(true){
            if(endDate.before(start)){
                break;
            }
            if(start.before(endDate) && end.after(endDate)){
                end = endDate;
            }
            int num = getTicketPersonTaskNew.setLunXianTicketRealData(start.getTime()/1000,end.getTime()/1000);
            log.error("开始导入时间段：" + DateUtils.formatDateYYYMMDDHHmmss(start)+"--" + DateUtils.formatDateYYYMMDDHHmmss(end) + ",数据量："+ num);
            total += num;
            start = DateUtils.parseDateYYYMMDDHHmmss(DateUtils.getOrderDay(start, 1,1));
            end = DateUtils.parseDateYYYMMDDHHmmss(DateUtils.getOrderDay(end, 1,2));
        }
        return PlainResult.success(true, "导入成功:" + total);
    }

    //@GetMapping("/wmRealOrderImport")
    public PlainResult<Boolean> wmRealOrderImport(@RequestParam(value = "startTime") String startTime,
                                                  @RequestParam(value = "endTime") String endTime) {

        log.info("----开始导入历史数据时间段为："+startTime+"--"+endTime);
        int num = getTicketPersonTaskNew.setWeiManTicketRealData(DateUtils.parseDateYYYMMDDHHmmss(startTime).getTime()/1000,DateUtils.parseDateYYYMMDDHHmmss(endTime).getTime()/1000);
        return PlainResult.success(true, "导入成功:" + num);
    }

}
