package com.das.province.web.controller.config;

import com.das.province.common.builder.HttpClientCustomizeBuilder;
import com.das.province.common.http.SslConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
@Slf4j
public class ProvinceHttpConfig {

    @Bean(name = "provinceHttpClientCustomizeBuilder")
    public HttpClientCustomizeBuilder creatHttpClientCustomizeBuilder() {
        return HttpClientCustomizeBuilder.custom()
                .pool(100, 5)
                .sslpv(SslConfig.SSLProtocolVersion.SSLv3)
                .ssl()
                .retry(3);
    }
}
