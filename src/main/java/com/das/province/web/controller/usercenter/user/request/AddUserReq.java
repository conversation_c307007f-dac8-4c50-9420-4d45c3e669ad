package com.das.province.web.controller.usercenter.user.request;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

@Data
public class AddUserReq implements Serializable {
    private static final long serialVersionUID = 1403402636962212551L;
    @NotNull(message = "用户名字不能为空")
    private String userName;
    @NotNull(message = "用户登录账号不能为空")
    private String loginAccount;
    @NotNull(message = "用户密码不能为空")
    private String password;
    // @NotNull(message = "用户手机号不能为空")
    private String phone;
    @NotNull(message = "部门不能为空")
    private Long departmentId;
    // @NotNull(message = "职位名称不能为空")
    private Long positionId;
    // @NotNull(message = "岗位编码不能为空")
    private String postCode;
    /**
     * 角色ids
     */
    @NotNull(message = "角色不能为空")
    private List<Long> roleIds;

    /**
     * 职位名称
     */
    private String positionName;

    /**
     * 领导id
     */
    private Long leaderId;
}
