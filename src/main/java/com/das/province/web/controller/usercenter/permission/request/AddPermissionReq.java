package com.das.province.web.controller.usercenter.permission.request;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

@Data
public class AddPermissionReq implements Serializable {
    private static final long serialVersionUID = 8655074364822577247L;
    @NotNull(message = "权限名称不能为空")
    private String permissionName;
    @NotNull(message = "权限编码不能为空")
    private String permissionCode;
    @NotNull(message = "权限类型不能为空")
    private String permissionType;
    private Long parentId;
    @NotNull(message = "权限层级不能为空")
    private Byte level;
    @NotNull(message = "排序不能为空")
    private Byte sortIndex;
    @NotNull(message = "功能权限启用状态不能为空")
    private Integer functionStatus;
    private String resourceUrl;
    private String resourceIcon;
    private List<SaveFunctionPermissionVO> functionPermissionList;
}