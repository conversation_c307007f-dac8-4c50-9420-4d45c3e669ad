package com.das.province.web.controller.common.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class CommonDocumentVO implements Serializable {
    private static final long serialVersionUID = -6178242375046144533L;
    private Long documentId;
    private String fileType;
    private String fileFormat;
    private String sourceFileName;
    private String saveFileName;
    private Long fileSize;
    private String url;
    private String description;
    private Long creator;
    private String creatorName;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date gmtCreate;
}
