package com.das.province.web.controller.usercenter.permission.request;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

@Data
public class SaveFunctionPermissionReq implements Serializable {
    private static final long serialVersionUID = -5352310030759125205L;
    @NotNull(message = "父权限不能为空")
    private Long parentId;
    @NotNull(message = "父权限层级不能为空")
    private Byte parentLevel;
    private List<SaveFunctionPermissionVO> functionPermissionList;
}
