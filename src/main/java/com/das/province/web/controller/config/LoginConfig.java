package com.das.province.web.controller.config;

import cn.dev33.satoken.interceptor.SaInterceptor;
import com.das.province.web.aop.interceptor.UserLoginInterceptor;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

@Configuration
public class LoginConfig implements WebMvcConfigurer {

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        InterceptorRegistration registration = registry.addInterceptor(new UserLoginInterceptor());
        registration.addPathPatterns("/**");
        registration.excludePathPatterns(
                "/province/pc/login",
                "/province/captcha",
                "/province/data/receive",
                "/province/auth/sessionKey",
                "/province/collection/digitPageList",
                "/province/collection/**",
                "/comprehensive/teachTask",
                "/province/importWmTicket/wmHistoryOrderImport",
                "/province/importWmTicket/lxHistoryOrderImport",
                "/province/proxy/proxyUrl"
        );
        registry.addInterceptor(new SaInterceptor()).addPathPatterns("/**").excludePathPatterns(
                "/province/pc/login",
                "/province/captcha",
                "/province/data/receive",
                "/province/auth/sessionKey",
                "/province/collection/digitPageList",
                "/province/collection/**",
                "/province/proxy/proxyUrl"
        );
    }
}
