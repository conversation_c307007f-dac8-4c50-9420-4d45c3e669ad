package com.das.province.web.controller.usercenter.role.request;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

@Data
public class AddRoleReq implements Serializable {
    private static final long serialVersionUID = -2019918120542363699L;
    @NotNull(message = "角色名称不能为空")
    private String roleName;

    @NotNull(message = "权限编码不能为空")
    private List<String> permissionCodeList;
}