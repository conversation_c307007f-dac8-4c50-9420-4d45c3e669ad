package com.das.province.web.controller.common.classification;

import com.das.province.common.bo.PlainResult;
import com.das.province.common.utils.BeanCopyUtils;
import com.das.province.service.biz.common.ClassificationService;
import com.das.province.service.biz.common.action.*;
import com.das.province.service.biz.common.dto.ClassificationDTO;
import com.das.province.service.enums.ClassificationBizTypeEnum;
import com.das.province.web.aop.annoation.PlainResultWrapper;
import com.das.province.web.controller.bo.LoginUserInfoBO;
import com.das.province.web.controller.common.classification.request.AddClassificationReq;
import com.das.province.web.controller.common.classification.request.EditClassificationReq;
import com.das.province.web.controller.common.classification.response.ClassificationDetailVO;
import com.das.province.web.controller.utils.LoginUserInfoUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

@Slf4j
@PlainResultWrapper
@RestController
@RequestMapping("/province/setting/classification")
public class ClassificationController {

    @Resource
    private ClassificationService classificationService;

    @PostMapping(value = "/add")
    public PlainResult<Long> addClassification(@RequestBody AddClassificationReq addClassificationReq) {
        LoginUserInfoBO userBO = LoginUserInfoUtils.getLoginUserInfo();
        ClassificationBizTypeEnum bizTypeEnum = ClassificationBizTypeEnum.fromCode(addClassificationReq.getBizType().intValue());
        AddClassificationAction action = BeanCopyUtils.copyByJSON(addClassificationReq, AddClassificationAction.class);
        action.setBizType(bizTypeEnum.getCode().byteValue());
        action.setCompanyId(userBO.getCompanyId());
        action.setCreator(userBO.getUserId());
        Long classificationId = this.classificationService.addClassification(action);
        return PlainResult.success(classificationId, "新增成功");
    }

    @PostMapping(value = "/edit")
    public PlainResult<Boolean> editClassification(@RequestBody EditClassificationReq editClassificationReq) {
        LoginUserInfoBO userBO = LoginUserInfoUtils.getLoginUserInfo();
        EditClassificationAction action = BeanCopyUtils.copyByJSON(editClassificationReq, EditClassificationAction.class);
        action.setCompanyId(userBO.getCompanyId());
        action.setModifier(userBO.getUserId());
        this.classificationService.editClassification(action);
        return PlainResult.success(true, "编辑成功");
    }

    @PostMapping("/del")
    public PlainResult<Boolean> delByClassificationId(@RequestParam(value = "classificationId") Long classificationId) {
        LoginUserInfoBO userBO = LoginUserInfoUtils.getLoginUserInfo();
        this.classificationService.delClassificationIncludeChild(userBO.getCompanyId(), classificationId);
        return PlainResult.success(true, "删除成功");
    }

    /**
     * 停用 启用
     * @param classificationId
     * @param enableStatus 启用状态 0 停用 1 启用
     * @return
     */
    @PostMapping("/updateEnableStatus")
    public PlainResult<Boolean> updateEnableStatus(@RequestParam(value = "classificationId") Long classificationId,@RequestParam(value = "enableStatus") Byte enableStatus) {
        LoginUserInfoBO userBO = LoginUserInfoUtils.getLoginUserInfo();
        this.classificationService.updateEnableStatus(userBO.getCompanyId(),userBO.getUserId(), classificationId,enableStatus);
        return PlainResult.success(true, "更新成功");
    }

    @GetMapping("/info")
    public PlainResult<ClassificationDetailVO> getDetailByClassificationId(@RequestParam(value = "classificationId") Long classificationId) {
        LoginUserInfoBO userBO = LoginUserInfoUtils.getLoginUserInfo();
        ClassificationDTO classificationDTO = this.classificationService.queryByClassificationId(userBO.getCompanyId(), classificationId);
        if (Objects.isNull(classificationDTO)) {
            return PlainResult.success(null, "获取成功");
        }
        ClassificationDetailVO classificationDetailVO = BeanCopyUtils.copyByJSON(classificationDTO, ClassificationDetailVO.class);
        return PlainResult.success(classificationDetailVO, "获取成功");
    }

    @GetMapping("/bizTypeTree")
    public PlainResult<ClassificationDTO> getTreeByBizType(@RequestParam(value = "bizId", required = false) Long bizId,
                                                           @RequestParam(value = "bizCode", required = false) String bizCode,
                                                           @RequestParam(value = "bizType") Integer bizType) {
        LoginUserInfoBO userBO = LoginUserInfoUtils.getLoginUserInfo();
        QueryClassificationTreeAction action = new QueryClassificationTreeAction();
        action.setCompanyId(userBO.getCompanyId());
        action.setBizId(bizId);
        action.setBizCode(bizCode);
        action.setBizType(ClassificationBizTypeEnum.fromCode(bizType));
        action.setCreator(userBO.getUserId());
        ClassificationDTO classificationDTO = this.classificationService.queryClassificationTree(action);
        if (Objects.isNull(classificationDTO)) {
            return PlainResult.success(null, "获取成功");
        }
        return PlainResult.success(classificationDTO, "获取成功");
    }

    @GetMapping("/classificationIdTree")
    public PlainResult<ClassificationDTO> getTreeByClassificationId(@RequestParam(value = "classificationId") Long classificationId) {
        LoginUserInfoBO userBO = LoginUserInfoUtils.getLoginUserInfo();
        QueryClassificationTreeByIdAction action = new QueryClassificationTreeByIdAction();
        action.setCompanyId(userBO.getCompanyId());
        action.setClassificationId(classificationId);
        ClassificationDTO classificationDTO = this.classificationService.queryClassificationTreeById(action);
        if (Objects.isNull(classificationDTO)) {
            return PlainResult.success(null, "获取成功");
        }
        return PlainResult.success(classificationDTO, "获取成功");
    }

    @GetMapping("/bizCodeAndTypeTree")
    public PlainResult<ClassificationDTO> getTreeByBizCodeAndBizType(@RequestParam(value = "bizCode") String bizCode,
                                                                     @RequestParam(value = "bizType") Integer bizType) {
        LoginUserInfoBO userBO = LoginUserInfoUtils.getLoginUserInfo();
        QueryTreeByBizCodeAndBizTypeAction action = new QueryTreeByBizCodeAndBizTypeAction();
        action.setCompanyId(userBO.getCompanyId());
        action.setBizCode(bizCode);
        action.setBizType(ClassificationBizTypeEnum.fromCode(bizType));
        ClassificationDTO classificationDTO = this.classificationService.queryTreeByBizCodeAndBizType(action);
        if (Objects.isNull(classificationDTO)) {
            return PlainResult.success(null, "获取成功");
        }
        return PlainResult.success(classificationDTO, "获取成功");
    }

    @GetMapping("/child")
    public PlainResult<List<ClassificationDTO>> getChildByClassificationId(@RequestParam(value = "classificationId") Long classificationId,
                                                                           @RequestParam(value = "name", required = false)String name) {
        LoginUserInfoBO userBO = LoginUserInfoUtils.getLoginUserInfo();
        if(StringUtils.isNotBlank(name)){
            List<ClassificationDTO> classificationDTOList = this.classificationService.queryByParentAndName(userBO.getCompanyId(), name, classificationId);
            return PlainResult.success(classificationDTOList, "获取成功");
        }

        List<ClassificationDTO> classificationDTOList = this.classificationService.queryChildByParentId(userBO.getCompanyId(), classificationId);
        return PlainResult.success(classificationDTOList, "获取成功");
    }

    @GetMapping("/byParentIdAndName")
    public PlainResult<ClassificationDTO> getByClassificationIdAndName(@RequestParam(value = "parentId") Long parentId,
                                                                       @RequestParam(value = "name") String name) {
        LoginUserInfoBO userBO = LoginUserInfoUtils.getLoginUserInfo();
        ClassificationDTO classificationDTO = this.classificationService.queryByParentIdAndName(userBO.getCompanyId(), parentId, name);
        return PlainResult.success(classificationDTO, "获取成功");
    }

    @GetMapping("/byTypeAndParentIdAndName")
    public PlainResult<ClassificationDTO> getByTypeAndParentIdAndName(@RequestParam(value = "parentId") Long parentId,
                                                                      @RequestParam(value = "bizType") Integer bizType,
                                                                      @RequestParam(value = "name") String name) {
        LoginUserInfoBO userBO = LoginUserInfoUtils.getLoginUserInfo();
        ClassificationDTO classificationDTO = this.classificationService
                .queryByTypeAndParentIdAndName(userBO.getCompanyId(), ClassificationBizTypeEnum.fromCode(bizType), parentId, name);
        return PlainResult.success(classificationDTO, "获取成功");
    }
}
