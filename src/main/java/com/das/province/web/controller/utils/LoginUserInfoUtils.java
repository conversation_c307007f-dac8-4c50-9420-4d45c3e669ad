package com.das.province.web.controller.utils;

import cn.dev33.satoken.session.SaSession;
import cn.dev33.satoken.stp.StpUtil;
import com.das.province.common.exception.CommonErrorCodeEnum;
import com.das.province.common.exception.CommonException;
import com.das.province.web.controller.bo.LoginUserInfoBO;

import java.util.Objects;

public class LoginUserInfoUtils {

    private LoginUserInfoUtils () {

    }

    public static LoginUserInfoBO getLoginUserInfo() {
        SaSession session = StpUtil.getSession();
        LoginUserInfoBO userInfoBO = (LoginUserInfoBO) session.get("loginUser");
        if (Objects.isNull(userInfoBO)) {
            throw new CommonException(CommonErrorCodeEnum.USER_NOT_EXIST_CODE, CommonErrorCodeEnum.USER_NOT_EXIST_CODE.getMessage());
        }
        return userInfoBO;
    }
}
