package com.das.province.web.controller.comprehensive.response;

import lombok.Data;

@Data
public class MuseumInfoListVO {

    /**
     * 博物馆id
     */
    private String museumId;

    /**
     * 博物馆名称
     */
    private String museumName;

    /**
     * 所在省
     */
    private String provinceName;

    /**
     * 所在市
     */
    private String cityName;

    /**
     * 所在区
     */
    private String countyName;

    /**
     * 等级id
     */
    private Long levelId;

    /**
     * 等级名称
     */
    private String levelName;

    /**
     * 博物馆性质id
     */
    private Long natureId;

    /**
     * 博物馆性质名称
     */
    private String natureName;

    /**
     * 是否已入驻省平台 0否 1是
     */
    private Byte settleIn;

    /**
     * 今日是否开放 0否 1是
     */
    private Byte isClose;

    /**
     * 藏品年报数据
     */
    private Long reportCollectionNum;

    /**
     * 平台藏品数据
     */
    private Integer collectionNum;

    /**
     * 今日观众数量
     */
    private Integer todayVisitorNum;

    /**
     * 本年观众数量
     */
    private Integer thisYearNum;

    /**
     * 官方平台地址
     */
    private String platformAddress;

    /**
     * 博物馆经度
     */
    private String lng;

    /**
     * 博物馆纬度
     */
    private String lat;


}
