package com.das.province.infr.dataobject;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR> 温湿度设备信息表
 */
@Data
public class SyncSafeDeviceInfoDO implements Serializable {

    private static final long serialVersionUID = 1370016024088596790L;

    /**
     * 自增id
     */
    private Long id;

    /**
     * 公司编码
     */
    private String uniqueCode;

    /**
     * 所属地区编码
     */
    private String regionCode;

    /**
     * 所属省份编码
     */
    private String provinceCode;

    /**
     * 所属城市编码
     */
    private String cityCode;

    /**
     * 所属区县编码
     */
    private String areaCode;

    /**
     * 设备编码
     */
    private String deviceCode;

    /**
     * 所在区域(1展厅,2库房,3其他)
     */
    private Byte regionName;

    /**
     * 设备位置
     */
    private String deviceAddress;

    /**
     * 最低温度阈值
     */
    private String minTemperature;

    /**
     * 最高温度阈值
     */
    private String maxTemperature;

    /**
     * 最低湿度阈值
     */
    private String minHumidity;

    /**
     * 最高湿度阈值
     */
    private String maxHumidity;

    /**
     * 温度
     */
    private String temperature;

    /**
     * 温度异常程度: 1:轻度, 2:中度, 3:严重
     */
    private String temperatureExceptionDegree;

    /**
     * 湿度
     */
    private String humidity;

    /**
     * 湿度异常程度: 1:轻度, 2:中度, 3:严重
     */
    private String humidityExceptionDegree;

    /**
     * 上报时间
     */
    private Date reportTime;

    /**
     * 设备状态：1:在线, 0:离线
     */
    private Byte deviceStatus;

    /**
     * 创建人
     */
    private Long creator;

    /**
     * 修改人
     */
    private Long modifier;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 修改时间
     */
    private Date gmtModified;
}