package com.das.province.infr.dataobject;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class AccUserInfoDO implements Serializable {
    private static final long serialVersionUID = -6162662400885699672L;
    private Long id;
    private Long companyId;
    private String userName;
    private String realName;
    private String password;
    private Long avatar;
    private String phone;
    private String email;
    private Long departmentId;
    private String address;
    private String remark;
    private Date gmtCreate;
    private Date gmtModified;
}