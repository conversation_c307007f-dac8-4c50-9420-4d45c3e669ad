package com.das.province.infr.dataobject;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class AccPositionDO implements Serializable {
    private static final long serialVersionUID = -2465904887838392234L;
    private Long id;
    private Long companyId;
    private String positionName;
    private Long departmentId;
    private String remark;
    private Byte isDelete;
    private Long creator;
    private Long modifier;
    private Date gmtCreate;
    private Date gmtModified;
}