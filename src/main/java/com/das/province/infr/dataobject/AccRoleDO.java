package com.das.province.infr.dataobject;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR> 角色信息表
 */
@Data
public class AccRoleDO implements Serializable {
    private static final long serialVersionUID = -6441602649394780402L;
    private Long id;
    private Long companyId;
    private String roleName;
    private String remark;
    private Byte status;
    private Byte isDelete;
    private Long creator;
    private Long modifier;
    private Date gmtCreate;
    private Date gmtModified;
}