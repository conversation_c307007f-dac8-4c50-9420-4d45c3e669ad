package com.das.province.infr.dataobject;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * <AUTHOR> 同步场馆信息表
 */
@Data
public class SyncVenueInfoDO implements Serializable {
    /**
     * 自增ID
     */
    private Long id;

    /**
     * 公司编码
     */
    private String uniqueCode;

    /**
     * 今日是否开馆,0否,1是
     */
    private Byte venueClose;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 修改时间
     */
    private Date gmtModified;

    private static final long serialVersionUID = 1L;
}