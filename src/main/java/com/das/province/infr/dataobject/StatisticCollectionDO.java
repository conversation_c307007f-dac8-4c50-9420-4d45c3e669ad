package com.das.province.infr.dataobject;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * <AUTHOR> 各博物馆藏品总量表
 */
@Data
public class StatisticCollectionDO implements Serializable {
    /**
     * 自增id
     */
    private Long id;

    /**
     * 博物馆id
     */
    private String museumId;

    /**
     * 博物馆名称
     */
    private String museumName;

    /**
     * 博物馆所属行政区编码
     */
    private String regionCode;

    /**
     * 博物馆所属城市
     */
    private String cityName;

    /**
     * 藏品总量
     */
    private Integer collectionNum;

    /**
     * 珍贵藏品总量
     */
    private Integer valuableNum;

    /**
     * 未定级藏品总量
     */
    private Integer exhibitionNum;

    /**
     * 全息展示总量
     */
    private Integer holographicNum;

    /**
     * 二维全息展示总量
     */
    private Integer twoModelNum;

    /**
     * 三维全息展示总量
     */
    private Integer threeModelNum;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 修改时间
     */
    private Date gmtModified;

    private static final long serialVersionUID = 1L;
}