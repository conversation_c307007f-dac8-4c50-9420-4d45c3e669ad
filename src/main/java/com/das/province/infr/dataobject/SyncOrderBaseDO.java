package com.das.province.infr.dataobject;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR> 同步订单信息基础表
 */
@Data
public class SyncOrderBaseDO implements Serializable {

    private static final long serialVersionUID = 1583819393491819962L;

    /**
     * 自增id
     */
    private Long id;

    /**
     * 公司编码
     */
    private String uniqueCode;

    /**
     * 场馆名称
     */
    private String venueName;

    /**
     * 订单编号
     */
    private String orderNo;

    /**
     * 联系方式
     */
    private String phone;

    /**
     * 订单总票数
     */
    private Long totalTicketCount;

    /**
     * 订单总人数
     */
    private Long totalPeopleCount;

    /**
     * 订单类型: 1:个人预约, 2:团队预约
     */
    private Byte orderType;

    /**
     * 预约类型: 1:提前预约, 2:免预约通行
     */
    private Byte reserveType;

    /**
     * 订单状态: 0:未使用, 1:未核销, 2:已核销, 3:已过期
     */
    private Byte status;

    /**
     * 预约日期
     */
    private Date reserveDate;

    /**
     * 分时场次开始时间
     */
    private Date timePeriodStart;

    /**
     * 分时场次结束时间
     */
    private Date timePeriodEnd;

    /**
     * 游客名称
     */
    private String touristName;

    /**
     * 证件类型: 1:身份证, 2:护照, 3:港澳通行证 4其他
     */
    private Byte touristCertificateType;

    /**
     * 证件号码
     */
    private String touristCertificateNo;

    /**
     * 核销日期
     */
    private Date writeOffDate;

    /**
     * 备注
     */
    private String remark;

    /**
     * 统计时间
     */
    private Date statisticsDate;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 修改时间
     */
    private Date gmtModified;
}