package com.das.province.infr.dataobject;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR> 数据异动详细信息表
 */
@Data
public class DataChangeDetailInfoDO implements Serializable {

    private static final long serialVersionUID = 8662508577642721420L;

    /**
     * 自增id
     */
    private Long id;

    /**
     * 异动id
     */
    private Long changeInfoId;

    /**
     * 博物馆id
     */
    private String museumId;

    /**
     * 异动数据
     */
    private String changeData;

    /**
     * 1：增加（新增），2：减少（注销）
     */
    private Byte upDown;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 修改时间
     */
    private Date gmtModified;
}