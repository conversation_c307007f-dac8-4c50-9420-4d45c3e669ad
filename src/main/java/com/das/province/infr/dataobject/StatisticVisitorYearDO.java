package com.das.province.infr.dataobject;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * <AUTHOR> 近一年观众统计表
 */
@Data
public class StatisticVisitorYearDO implements Serializable {
    /**
     * 自增id
     */
    private Long id;

    /**
     * 博物馆id
     */
    private String museumId;

    /**
     * 博物馆名称
     */
    private String museumName;

    /**
     * 博物馆所属行政区编码
     */
    private String regionCode;

    /**
     * 博物馆所属城市
     */
    private String cityName;

    /**
     * 观众总量
     */
    private Integer totalNum;

    /**
     * 线上观众总量
     */
    private Integer onlineNum;

    /**
     * 线下观众总量
     */
    private Integer offlineNum;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 修改时间
     */
    private Date gmtModified;

    private static final long serialVersionUID = 1L;
}