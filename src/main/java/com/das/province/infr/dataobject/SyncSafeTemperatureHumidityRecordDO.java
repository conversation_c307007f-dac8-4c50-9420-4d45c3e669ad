package com.das.province.infr.dataobject;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR> 温湿度上报记录表
 */
@Data
public class SyncSafeTemperatureHumidityRecordDO implements Serializable {

    private static final long serialVersionUID = -9202915484151604834L;

    /**
     * 自增id
     */
    private Long id;

    /**
     * 公司编码
     */
    private String uniqueCode;

    /**
     * 设备编码
     */
    private String deviceCode;

    /**
     * 温度
     */
    private String currentTemperature;

    /**
     * 湿度
     */
    private String currentHumidity;

    /**
     * 上报时间
     */
    private Date reportTime;
}