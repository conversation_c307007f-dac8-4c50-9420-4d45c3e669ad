package com.das.province.infr.dataobject;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR> 同步用户信息表
 */
@Data
public class SyncUserInfoDO implements Serializable {

    private static final long serialVersionUID = 5984942633771775169L;

    /**
     * 自增ID
     */
    private Long id;

    /**
     * 公司编码
     */
    private String uniqueCode;

    /**
     * 岗位编码
     */
    private String postCode;

    /**
     * 数量
     */
    private Long count;

    /**
     * 统计时间
     */
    private Date statisticsDate;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 修改时间
     */
    private Date gmtModified;
}