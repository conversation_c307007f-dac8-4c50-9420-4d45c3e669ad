package com.das.province.infr.dataobject;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * <AUTHOR> 博物馆概况统计表
 */
@Data
public class StatisticMuseumGeneralDO implements Serializable {
    /**
     * 自增id
     */
    private Long id;

    /**
     * 博物馆总数
     */
    private Integer totalNum;

    /**
     * 入驻博物馆总数
     */
    private Integer settleInNum;

    /**
     * 在编人员总数
     */
    private Integer underPreparationNum;

    /**
     * 编外人员总数
     */
    private Integer notPreparationNum;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 修改时间
     */
    private Date gmtModified;

    private static final long serialVersionUID = 1L;
}