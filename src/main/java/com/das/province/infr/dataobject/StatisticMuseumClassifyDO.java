package com.das.province.infr.dataobject;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * <AUTHOR> 博物馆分类统计表
 */
@Data
public class StatisticMuseumClassifyDO implements Serializable {
    /**
     * 自增id
     */
    private Long id;

    /**
     * 分类类型，1:质量级别,2:性质,3:类别
     */
    private Byte classifyType;

    /**
     * 统计id,对应字典id
     */
    private Long statisticId;

    /**
     * 统计名称
     */
    private String statisticName;

    /**
     * 博物馆数量
     */
    private Integer museumNum;

    /**
     * 占比
     */
    private String museumProportion;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 修改时间
     */
    private Date gmtModified;

    private static final long serialVersionUID = 1L;
}