package com.das.province.infr.dataobject;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class AccUserDO implements Serializable {
    private static final long serialVersionUID = -3447675581001476574L;
    private Long id;

    /**
     * 公司id
     */
    private Long companyId;

    /**
     * 用户名称
     */
    private String userName;

    /**
     * 用户真实名字
     */
    private String realName;

    /**
     * 用户登录账号
     */
    private String loginAccount;

    /**
     * 用户密码
     */
    private String password;

    /**
     * 源用户密码
     */
    private String sourcePassword;

    /**
     * 用户头像: document_id
     */
    private Long avatar;

    /**
     * 用户手机号
     */
    private String phone;

    /**
     * 用户邮箱
     */
    private String email;

    /**
     * 部门id
     */
    private Long departmentId;

    /**
     * 职位id
     */
    private Long positionId;

    /**
     * 职位名称
     */
    private String positionName;

    /**
     * 岗位编码:
     */
    private String postCode;

    /**
     * 领导id
     */
    private Long leaderId;

    /**
     * 领导级别
     */
    private Byte leaderLevel;

    /**
     * 用户类型:saaspc
     */
    private String userType;

    /**
     * 备注
     */
    private String remark;

    /**
     * 账号状态: 1:启用, 0:禁用
     */
    private Byte accountStatus;

    /**
     * 是否删除: 1:是, 0:否
     */
    private Byte isDelete;

    /**
     * 创建人
     */
    private Long creator;

    /**
     * 修改人
     */
    private Long modifier;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 修改时间
     */
    private Date gmtModified;
}