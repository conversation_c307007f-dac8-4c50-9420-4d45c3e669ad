package com.das.province.infr.dataobject;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * <AUTHOR> 观众日统计表
 */
@Data
public class StatisticVisitorDayDO implements Serializable {
    /**
     * 自增id
     */
    private Long id;

    /**
     * 统计日期
     */
    private String statisticDay;

    /**
     * 博物馆id
     */
    private String museumId;

    /**
     * 博物馆名称
     */
    private String museumName;

    /**
     * 博物馆所属行政区编码
     */
    private String regionCode;

    /**
     * 博物馆所属城市
     */
    private String cityName;

    /**
     * 观众总量
     */
    private Integer totalNum;

    /**
     * 线上观众总量
     */
    private Integer onlineNum;

    /**
     * 全息观众总量
     */
    private Integer holographicNum;

    /**
     * 全景观众总量
     */
    private Integer overallViewNum;

    /**
     * 预约观众总量
     */
    private Integer orderNum;

    /**
     * 线下（入馆）观众总量
     */
    private Integer offlineNum;

    /**
     * 个人观众总量
     */
    private Integer personalNum;

    /**
     * 团队观众总量
     */
    private Integer teamManNum;

    /**
     * 入馆团队个数
     */
    private Integer teamNum;

    /**
     * 成年观众
     */
    private Integer adultNum;

    /**
     * 未成年观众
     */
    private Integer childrenNum;

    /**
     * 省内观众
     */
    private Integer provinceNum;

    /**
     * 省外观众
     */
    private Integer outsideNum;

    /**
     * 境外观众
     */
    private Integer abroadNum;

    /**
     * 男性观众
     */
    private Integer manNum;

    /**
     * 女性观众
     */
    private Integer womanNum;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 修改时间
     */
    private Date gmtModified;

    private static final long serialVersionUID = 1L;
}