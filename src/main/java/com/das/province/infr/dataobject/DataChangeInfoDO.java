package com.das.province.infr.dataobject;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR> 数据异动信息表
 */
@Data
public class DataChangeInfoDO implements Serializable {

    private static final long serialVersionUID = -7381495630212775276L;

    /**
     * 自增id
     */
    private Long id;

    /**
     * 异动类型,1:藏品异动,2:观众异动
     */
    private Byte changeType;

    /**
     * 异动描述
     */
    private String changeDescription;

    /**
     * 异动发生时间
     */
    private Date happenTime;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 修改时间
     */
    private Date gmtModified;
}