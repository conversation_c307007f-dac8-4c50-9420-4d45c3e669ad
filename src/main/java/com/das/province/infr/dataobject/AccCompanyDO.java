package com.das.province.infr.dataobject;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class AccCompanyDO implements Serializable {
    private static final long serialVersionUID = 1748069989029600698L;
    private Long id;
    private String companyName;
    private String contactName;
    private String contactPhone;
    private Byte companyLevel;
    private Byte tryType;
    private Date tryDeadlineDate;
    private String address;
    private String remark;
    private Date gmtCreate;
    private Date gmtModified;
}