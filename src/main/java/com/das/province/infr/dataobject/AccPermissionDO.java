package com.das.province.infr.dataobject;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class AccPermissionDO implements Serializable {
    private static final long serialVersionUID = 2756587586224998287L;
    private Long id;
    private Long companyId;
    private String permissionName;
    private String permissionCode;
    private String permissionType;
    private Long parentId;
    private Byte level;
    private Byte sortIndex;
    private String permission;
    private Byte permissionStatus;
    private Byte functionStatus;
    private Byte isDelete;
    private String resourceUrl;
    private String resourceIcon;
    private String remark;
    private Long creator;
    private Long modifier;
    private Date gmtCreate;
    private Date gmtModified;
}