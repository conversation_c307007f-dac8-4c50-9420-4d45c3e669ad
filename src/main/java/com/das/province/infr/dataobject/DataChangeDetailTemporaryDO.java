package com.das.province.infr.dataobject;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * <AUTHOR> 数据异动详细信息临时表
 */
@Data
public class DataChangeDetailTemporaryDO implements Serializable {
    /**
     * 自增id
     */
    private Long id;

    /**
     * 博物馆id
     */
    private String museumId;

    /**
     * 异动数据
     */
    private Integer changeData;

    /**
     * 1：增加（新增），2：减少（注销）
     */
    private Byte upDown;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 修改时间
     */
    private Date gmtModified;

    private static final long serialVersionUID = 1L;
}