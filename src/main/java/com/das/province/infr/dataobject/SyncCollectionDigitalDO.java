package com.das.province.infr.dataobject;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * <AUTHOR> 同步博物馆藏品数字化数据
 */
@Data
public class SyncCollectionDigitalDO implements Serializable {
    /**
     * 自增id
     */
    private Long id;

    /**
     * 博物馆id
     */
    private String uniqueCode;

    /**
     * 藏品二维数字化数量
     */
    private Integer twoModelNum;

    /**
     * 藏品三维数字化数量
     */
    private Integer threeModelNum;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 修改时间
     */
    private Date gmtModified;

    private static final long serialVersionUID = 1L;
}