package com.das.province.infr.dataobject;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR> 博物馆授权信息表
 */
@Data
public class SyncMuseumAuthDO implements Serializable {

    private static final long serialVersionUID = -6667093766146259993L;

    /**
     * 自增id
     */
    private Long id;

    /**
     * 公司id
     */
    private Long museumBaseId;

    /**
     * 公司编码
     */
    private String uniqueCode;

    /**
     * 秘钥
     */
    private String key;

    /**
     * 创建人
     */
    private Long creator;

    /**
     * 修改人
     */
    private Long modifier;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 修改时间
     */
    private Date gmtModified;
}