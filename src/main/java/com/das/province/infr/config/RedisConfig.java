package com.das.province.infr.config;

import com.alibaba.fastjson.support.spring.GenericFastJsonRedisSerializer;
import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.PropertyAccessor;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.jsontype.impl.LaissezFaireSubTypeValidator;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.CacheManager;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.cache.RedisCacheConfiguration;
import org.springframework.data.redis.cache.RedisCacheManager;
import org.springframework.data.redis.cache.RedisCacheWriter;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.serializer.Jackson2JsonRedisSerializer;
import org.springframework.data.redis.serializer.RedisSerializationContext;
import org.springframework.data.redis.serializer.RedisSerializer;
import org.springframework.data.redis.serializer.StringRedisSerializer;

import java.time.Duration;
import java.util.Map;

@Slf4j
@Configuration
public class RedisConfig {
    private static final String NAME_SPACE = "wisdom:";

    /**
     * 使用Jackson2JsonRedisSerializer替代FastJSON，提高安全性和兼容性
     */
    @Bean
    public RedisSerializer<Object> jackson2JsonRedisSerializer() {
        try {
            Jackson2JsonRedisSerializer<Object> serializer = new Jackson2JsonRedisSerializer<>(Object.class);
            ObjectMapper objectMapper = new ObjectMapper();
            objectMapper.setVisibility(PropertyAccessor.ALL, JsonAutoDetect.Visibility.ANY);
            objectMapper.activateDefaultTyping(LaissezFaireSubTypeValidator.instance, ObjectMapper.DefaultTyping.NON_FINAL);
            serializer.setObjectMapper(objectMapper);
            log.info("Jackson2JsonRedisSerializer 初始化成功");
            return serializer;
        } catch (Exception e) {
            log.error("Jackson2JsonRedisSerializer 初始化失败，回退到FastJSON", e);
            return new GenericFastJsonRedisSerializer();
        }
    }

    /**
     * 备用的FastJSON序列化器
     */
    @Bean
    public RedisSerializer<Object> fastJson2JsonRedisSerializer() {
        return new GenericFastJsonRedisSerializer();
    }

    @Bean("redisTemplate")
    public RedisTemplate<String, Object> initRedisTemplate(RedisConnectionFactory redisConnectionFactory) {
        RedisTemplate<String, Object> redisTemplate = new RedisTemplate<>();

        try {
            redisTemplate.setConnectionFactory(redisConnectionFactory);

            // 使用Jackson序列化器
            RedisSerializer<Object> jackson2JsonRedisSerializer = jackson2JsonRedisSerializer();
            StringRedisSerializer stringRedisSerializer = new StringRedisSerializer();

            // key采用String的序列化方式
            redisTemplate.setKeySerializer(stringRedisSerializer);
            redisTemplate.setHashKeySerializer(stringRedisSerializer);

            // value序列化方式采用jackson
            redisTemplate.setValueSerializer(jackson2JsonRedisSerializer);
            redisTemplate.setHashValueSerializer(jackson2JsonRedisSerializer);

            // 设置默认序列化器
            redisTemplate.setDefaultSerializer(jackson2JsonRedisSerializer);

            redisTemplate.afterPropertiesSet();
            log.info("RedisTemplate 初始化成功");

        } catch (Exception e) {
            log.error("RedisTemplate 初始化失败", e);
            throw new RuntimeException("Redis配置初始化失败", e);
        }

        return redisTemplate;
    }
    @Bean("stringRedisTemplate")
    public StringRedisTemplate stringRedisTemplate(RedisConnectionFactory redisConnectionFactory) {
        try {
            StringRedisTemplate stringRedisTemplate = new StringRedisTemplate();
            stringRedisTemplate.setConnectionFactory(redisConnectionFactory);

            StringRedisSerializer stringRedisSerializer = new StringRedisSerializer();
            stringRedisTemplate.setKeySerializer(stringRedisSerializer);
            stringRedisTemplate.setValueSerializer(stringRedisSerializer);
            stringRedisTemplate.setHashKeySerializer(stringRedisSerializer);
            stringRedisTemplate.setHashValueSerializer(stringRedisSerializer);

            stringRedisTemplate.afterPropertiesSet();
            log.info("StringRedisTemplate 初始化成功");
            return stringRedisTemplate;

        } catch (Exception e) {
            log.error("StringRedisTemplate 初始化失败", e);
            throw new RuntimeException("StringRedisTemplate配置初始化失败", e);
        }
    }
    @Bean
    public CacheManager cacheManager(RedisConnectionFactory redisConnectionFactory) {
        try {
            log.info("RedisCacheManager 开始初始化");

            RedisCacheWriter cacheWriter = RedisCacheWriter.nonLockingRedisCacheWriter(redisConnectionFactory);
            RedisCacheConfiguration defaultConfig = this.redisCacheConfiguration(60);
            Map<String, RedisCacheConfiguration> configMap = this.getRedisCacheConfigurationMap();

            RedisCacheManager cacheManager = new RedisCacheManager(cacheWriter, defaultConfig, configMap);
            log.info("RedisCacheManager 初始化成功");
            return cacheManager;

        } catch (Exception e) {
            log.error("RedisCacheManager 初始化失败", e);
            throw new RuntimeException("RedisCacheManager配置初始化失败", e);
        }
    }
    private Map<String, RedisCacheConfiguration> getRedisCacheConfigurationMap() {
        Map<String, RedisCacheConfiguration> redisCacheConfigurationMap = Maps.newHashMap();
        redisCacheConfigurationMap.put("dec", this.redisCacheConfiguration(3600));
        return redisCacheConfigurationMap;
    }
    private RedisCacheConfiguration redisCacheConfiguration(int seconds) {
        try {
            return RedisCacheConfiguration
                    .defaultCacheConfig()
                    .serializeKeysWith(
                            RedisSerializationContext.SerializationPair
                                    .fromSerializer(new StringRedisSerializer()))
                    .serializeValuesWith(
                            RedisSerializationContext.SerializationPair
                                    .fromSerializer(jackson2JsonRedisSerializer()))
                    .entryTtl(Duration.ofSeconds(seconds))
                    .computePrefixWith(cacheName -> NAME_SPACE + cacheName + ":")
                    .disableCachingNullValues(); // 禁止缓存null值
        } catch (Exception e) {
            log.error("RedisCacheConfiguration 配置失败，使用默认配置", e);
            return RedisCacheConfiguration.defaultCacheConfig()
                    .entryTtl(Duration.ofSeconds(seconds));
        }
    }
}
