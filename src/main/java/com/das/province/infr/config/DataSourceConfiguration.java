package com.das.province.infr.config;

import com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceBuilder;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionFactoryBean;
import org.mybatis.spring.SqlSessionTemplate;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;

import javax.sql.DataSource;


@Configuration
@MapperScan(basePackages = "com.das.province.infr.mapper", sqlSessionTemplateRef = "provinceMuseumSqlSessionTemplate")
public class DataSourceConfiguration {
    @Bean(name = "provinceMuseumDataSource")
    @Primary
    @ConfigurationProperties(prefix = "spring.datasource.druid")
    public DataSource dataSource() {
        return DruidDataSourceBuilder.create().build();
    }
    @Primary
    @Bean(name = "provinceMuseumSqlSessionFactory")
    public SqlSessionFactory sqlSessionFactory(@Qualifier("provinceMuseumDataSource") DataSource dataSource) throws Exception {
        SqlSessionFactoryBean bean = new SqlSessionFactoryBean();
        bean.setDataSource(dataSource);
        bean.setMapperLocations(new PathMatchingResourcePatternResolver().getResources("classpath*:mybatis/mapper/*.xml"));
        return bean.getObject();
    }
    @Primary
    @Bean(name = "provinceMuseumTransactionManager")
    public DataSourceTransactionManager transactionManager(@Qualifier("provinceMuseumDataSource") DataSource dataSource) {
        return new DataSourceTransactionManager(dataSource);
    }
    @Primary
    @Bean(name = "provinceMuseumSqlSessionTemplate")
    public SqlSessionTemplate sqlSessionTemplate(@Qualifier("provinceMuseumSqlSessionFactory") SqlSessionFactory sqlSessionFactory)
            throws Exception {
        return new SqlSessionTemplate(sqlSessionFactory);
    }
}
