package com.das.province.infr.mapper;

import com.das.province.infr.dataobject.StatisticCollectionDO;
import com.das.province.infr.dataobjectexpand.GroupCollectionByCityDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface StatisticCollectionMapper {
    int deleteByPrimaryKey(Long id);

    int insert(StatisticCollectionDO record);

    int insertSelective(StatisticCollectionDO record);

    StatisticCollectionDO selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(StatisticCollectionDO record);

    int updateByPrimaryKey(StatisticCollectionDO record);

    List<StatisticCollectionDO> selectList();

    List<GroupCollectionByCityDO> groupCollectionByCity();

    List<StatisticCollectionDO> selectListByMuseumIds(@Param("museumIds") List<String> museumIds);

    List<StatisticCollectionDO> selectListByCity(@Param("regionCode") String regionCode);

    /**
     * 删除全部
     */
    void deleteAll();

    void batchInsert(@Param("statisticList") List<StatisticCollectionDO> statisticList);
}