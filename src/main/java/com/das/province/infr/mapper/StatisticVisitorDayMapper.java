package com.das.province.infr.mapper;

import com.das.province.infr.dataobject.StatisticVisitorDayDO;
import com.das.province.infr.dataobjectexpand.*;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

@Mapper
public interface StatisticVisitorDayMapper {
    int deleteByPrimaryKey(Long id);

    int insert(StatisticVisitorDayDO record);

    int insertSelective(StatisticVisitorDayDO record);

    StatisticVisitorDayDO selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(StatisticVisitorDayDO record);

    int updateByPrimaryKey(StatisticVisitorDayDO record);

    List<StatisticVisitorDayDO> listByStatisticDay(@Param("statisticDay") String statisticDay,
                                                   @Param("regionCode") String regionCode);

    List<StatisticVisitorDayDO> listByMuseumIds(@Param("statisticDay") String statisticDay,
                                                @Param("museumIds") List<String> museumIds);

    List<StatisticVisitorDayDO> listByTimes(@Param("startDay") String startDay,
                                            @Param("endDay") String endDay,
                                            @Param("museumIds") List<String> museumIds);

    List<StatisticVisitorDayDO> selectList();

    /**
     * 批量插入
     * @param visitorDayDOList
     */
    void batchInsert(@Param("visitorDayDOList") List<StatisticVisitorDayDO> visitorDayDOList);

    /**
     * 根据博物馆id和统计日期删除指定数据
     * @param statisticDay
     * @param museumId
     */
    void deleteByMuseumIdAndStatisticDay(@Param("museumId") String museumId,
                                         @Param("statisticDay") String statisticDay);


    /**
     * 统计观众月数据
     * @return
     */
    List<GroupVisitorMonthDataDO> groupVisitorMonthData();

    /**
     * 统计观众本年数据
     * @return
     */
    List<GroupVisitorYearDataDO> groupVisitorYearData(@Param("startDay") String startDay);


    List<GroupMuseumByHolidayDO> groupMuseumByHoliday(@Param("startDay") String startDay,@Param("endDay") String endDay);

    /**
     * 按时间统计观众数据
     * @param startDay
     * @param endDay
     * @return
     */
    List<GroupVisitorByTimeDO> groupVisitorByTime(@Param("startDay") String startDay,
                                                  @Param("endDay") String endDay);


    /**
     * 根据博物馆统计
     * @param startDay
     * @param endDay
     * @return
     */
    List<GroupVisitorByMuseumDO> groupVisitorByMuseum(@Param("museumIds") List<String> museumIds,
                                                      @Param("startDay") String startDay,
                                                      @Param("endDay") String endDay);

    /**
     * 根据城市统计
     * @param startDay
     * @param endDay
     * @return
     */
    List<GroupVisitorByCityDO> groupVisitorByCity(@Param("startDay") String startDay,
                                                   @Param("endDay") String endDay);


    /**
     * 根据时间类型统计观众数量
     * @param dateLength  4 年，7 月  10 日
     * @param startDay
     * @param endDay
     * @param museumIds
     * @return
     */
    List<GroupVisitorByDateTypeDO> groupVisitorByDateType(@Param("dateLength") Integer dateLength,
                                                          @Param("startDay") String startDay,
                                                          @Param("endDay") String endDay,
                                                          @Param("museumIds") List<String> museumIds);

}