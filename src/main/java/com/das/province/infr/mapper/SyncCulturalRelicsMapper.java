package com.das.province.infr.mapper;

import com.das.province.infr.dataobject.SyncCulturalRelicsDO;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

public interface SyncCulturalRelicsMapper {
    int deleteByPrimaryKey(Long id);

    int insert(SyncCulturalRelicsDO record);

    int insertSelective(SyncCulturalRelicsDO record);

    SyncCulturalRelicsDO selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(SyncCulturalRelicsDO record);

    int updateByPrimaryKey(SyncCulturalRelicsDO record);

    int deleteByUniqueCode(@Param("uniqueCode") String uniqueCode);

    SyncCulturalRelicsDO selectUniqueCodeAndNumberAndStatisticsDate(@Param("uniqueCode") String uniqueCode, @Param("number") String number,
                                                                    @Param("statisticsDate") Date statisticsDate);

    List<SyncCulturalRelicsDO> selectList();

    List<SyncCulturalRelicsDO> listUniqueCode(@Param("uniqueCode") String uniqueCode);

    List<SyncCulturalRelicsDO> listByCondition(@Param("statisticsDateStart") Date statisticsDateStart,
                                                @Param("statisticsDateEnd") Date statisticsDateEnd,
                                                @Param("uniqueCodes") List<String> uniqueCodes);
}