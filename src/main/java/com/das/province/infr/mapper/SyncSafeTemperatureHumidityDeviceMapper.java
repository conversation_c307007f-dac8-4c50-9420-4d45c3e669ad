package com.das.province.infr.mapper;

import com.das.province.infr.dataobject.SyncSafeTemperatureHumidityDeviceDO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface SyncSafeTemperatureHumidityDeviceMapper {
    int deleteByPrimaryKey(Long id);

    int insert(SyncSafeTemperatureHumidityDeviceDO record);

    int insertSelective(SyncSafeTemperatureHumidityDeviceDO record);

    SyncSafeTemperatureHumidityDeviceDO selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(SyncSafeTemperatureHumidityDeviceDO record);

    int updateByPrimaryKey(SyncSafeTemperatureHumidityDeviceDO record);

    List<SyncSafeTemperatureHumidityDeviceDO> selectByUniqueCode(@Param("uniqueCode") String uniqueCode);

    SyncSafeTemperatureHumidityDeviceDO selectByDeviceCode(@Param("deviceCode") String deviceCode);

    int deleteByUniqueCode(@Param("uniqueCode") String uniqueCode);

    int updateByDeviceCode(SyncSafeTemperatureHumidityDeviceDO record);

    List<SyncSafeTemperatureHumidityDeviceDO> selectList();

}