package com.das.province.infr.mapper;

import com.das.province.infr.dataobject.SyncMuseumAuthDO;
import org.apache.ibatis.annotations.Param;

public interface SyncMuseumAuthMapper {
    int deleteByPrimaryKey(Long id);

    int insert(SyncMuseumAuthDO record);

    int insertSelective(SyncMuseumAuthDO record);

    SyncMuseumAuthDO selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(SyncMuseumAuthDO record);

    int updateByPrimaryKey(SyncMuseumAuthDO record);

    SyncMuseumAuthDO selectByMuseumBaseId(@Param("museumBaseId") Long museumBaseId);
}