package com.das.province.infr.mapper;

import com.das.province.infr.dataobject.StatisticMuseumGeneralDO;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface StatisticMuseumGeneralMapper {
    int deleteByPrimaryKey(Long id);

    int insert(StatisticMuseumGeneralDO record);

    int insertSelective(StatisticMuseumGeneralDO record);

    StatisticMuseumGeneralDO selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(StatisticMuseumGeneralDO record);

    int updateByPrimaryKey(StatisticMuseumGeneralDO record);

    StatisticMuseumGeneralDO selectInfo();

    void deleteAll();
}