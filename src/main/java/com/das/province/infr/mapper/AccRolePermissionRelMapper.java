package com.das.province.infr.mapper;

import com.das.province.infr.dataobject.AccRolePermissionRelDO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface AccRolePermissionRelMapper {
    int deleteByPrimaryKey(Long id);
    int insert(AccRolePermissionRelDO record);
    int insertSelective(AccRolePermissionRelDO record);
    AccRolePermissionRelDO selectByPrimaryKey(Long id);
    int updateByPrimaryKeySelective(AccRolePermissionRelDO record);
    int updateByPrimaryKey(AccRolePermissionRelDO record);
    List<AccRolePermissionRelDO> listByRoleId(@Param("companyId") Long companyId, @Param("roleId") Long roleId);
    List<AccRolePermissionRelDO> listByRoleIds(@Param("companyId") Long companyId, @Param("roleIds") List<Long> roleIds);
    List<AccRolePermissionRelDO> listByPermissionCodes(@Param("companyId") Long companyId, @Param("permissionCodes") List<String> permissionCodes);
    int deleteByRoleId(@Param("companyId") Long companyId, @Param("roleId") Long roleId);

    /**
     * 查询对应菜单是否有角色绑定
     * @param companyId 公司id
     * @param permissionCodes 权限编码
     * @return
     */
    List<AccRolePermissionRelDO> listByPermissionCodesGroupByRoleId(@Param("companyId") Long companyId, @Param("permissionCodes") List<String> permissionCodes);

    /**
     * 批量删除已经被删除的菜单关联
     * @param companyId 公司id
     * @param roleIds 已经被删除的角色id
     * @return
     */
    int deleteByRoleIds(@Param("companyId") Long companyId, @Param("roleIds") List<Long> roleIds);
}