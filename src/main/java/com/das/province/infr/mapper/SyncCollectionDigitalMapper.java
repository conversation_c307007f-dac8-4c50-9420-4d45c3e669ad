package com.das.province.infr.mapper;

import com.das.province.infr.dataobject.SyncCollectionDigitalDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface SyncCollectionDigitalMapper {
    int deleteByPrimaryKey(Long id);

    int insert(SyncCollectionDigitalDO record);

    int insertSelective(SyncCollectionDigitalDO record);

    SyncCollectionDigitalDO selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(SyncCollectionDigitalDO record);

    int updateByPrimaryKey(SyncCollectionDigitalDO record);

    List<SyncCollectionDigitalDO> selectList();

    int deleteByUniqueCode(@Param("uniqueCode") String uniqueCode);

    SyncCollectionDigitalDO selectByUniqueCode(@Param("uniqueCode") String uniqueCode);
}