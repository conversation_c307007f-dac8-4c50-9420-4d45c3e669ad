package com.das.province.infr.mapper;

import com.das.province.infr.dataobject.StatisticVisitorMonthDO;
import com.das.province.infr.dataobjectexpand.GroupMuseumByYearDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface StatisticVisitorMonthMapper {
    int deleteByPrimaryKey(Long id);

    int insert(StatisticVisitorMonthDO record);

    int insertSelective(StatisticVisitorMonthDO record);

    StatisticVisitorMonthDO selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(StatisticVisitorMonthDO record);

    int updateByPrimaryKey(StatisticVisitorMonthDO record);

    List<StatisticVisitorMonthDO> selectListByMonths(@Param("months") List<String> months,
                                                     @Param("regionCode") String regionCode);

    List<StatisticVisitorMonthDO> selectListByMonth(@Param("statisticMonth") String statisticMonth);

    void deleteAll();

    void batchInsert(@Param("visitorMonthDOList") List<StatisticVisitorMonthDO> visitorMonthDOList);

    List<GroupMuseumByYearDO> groupMuseumByYear(@Param("year") String year);
}