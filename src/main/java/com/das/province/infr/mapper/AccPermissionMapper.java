package com.das.province.infr.mapper;

import com.das.province.infr.dataobject.AccPermissionDO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface AccPermissionMapper {
    int deleteByPrimaryKey(Long id);
    int insert(AccPermissionDO record);
    int insertSelective(AccPermissionDO record);
    AccPermissionDO selectByPrimaryKey(Long id);
    int updateByPrimaryKeySelective(AccPermissionDO record);
    int updateByPrimaryKey(AccPermissionDO record);
    List<AccPermissionDO> listByPermissionCodes(@Param("companyId") Long companyId, @Param("permissionCodes") List<String> permissionCodes, @Param("isDelete") Byte isDelete);
    AccPermissionDO selectByPermissionCode(@Param("companyId") Long companyId, @Param("permissionCode") String permissionCode, @Param("isDelete") Byte isDelete);
    List<AccPermissionDO> listDirectChildByPermissionId(@Param("companyId") Long companyId, @Param("permissionId") Long permissionId, @Param("isDelete") Byte isDelete,@Param("permissionType") String permissionType);
    AccPermissionDO selectByPermissionId(@Param("companyId") Long companyId, @Param("permissionId") Long permissionId, @Param("isDelete") Byte isDelete);
    List<AccPermissionDO> listByPermissionIds(@Param("companyId") Long companyId, @Param("permissionIds") List<Long> permissionIds, @Param("isDelete") Byte isDelete);
    List<AccPermissionDO> listIncludeChildByPermissionId(@Param("companyId") Long companyId, @Param("permissionId") Long permissionId, @Param("isDelete") Byte isDelete);
    List<AccPermissionDO> listIncludeChildByPermissionIds(@Param("companyId") Long companyId, @Param("permissionIds") List<Long> permissionIds, @Param("isDelete") Byte isDelete);
    List<AccPermissionDO> listChildByPermissionId(@Param("companyId") Long companyId, @Param("permissionId") Long permissionId, @Param("isDelete") Byte isDelete);
    List<AccPermissionDO> listByCondition(@Param("companyId") Long companyId, @Param("permissionId") Long permissionId,
                                          @Param("permissionName") String permissionName, @Param("level") Byte level, @Param("permissionType") String permissionType,
                                          @Param("sortBy") String sortBy, @Param("isDelete") Byte isDelete);
    AccPermissionDO selectRootByCompanyId(@Param("companyId") Long companyId, @Param("isDelete") Byte isDelete);
    int deleteFunctionByMenuId(@Param("companyId") Long companyId, @Param("menuPermissionId") Long menuPermissionId);
    int updateByPermissionIds(@Param("record") AccPermissionDO record, @Param("companyId") Long companyId, @Param("permissionIds") List<Long> permissionIds);
}