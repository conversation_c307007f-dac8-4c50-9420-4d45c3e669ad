package com.das.province.infr.mapper;

import com.das.province.infr.dataobject.AccCompanyDO;

import java.util.List;

public interface AccCompanyMapper {
    int deleteByPrimaryKey(Long id);
    int insert(AccCompanyDO record);
    int insertSelective(AccCompanyDO record);
    AccCompanyDO selectByPrimaryKey(Long id);
    int updateByPrimaryKeySelective(AccCompanyDO record);
    int updateByPrimaryKey(AccCompanyDO record);

    List<AccCompanyDO> selectList();
}