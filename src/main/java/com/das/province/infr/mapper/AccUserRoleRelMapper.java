package com.das.province.infr.mapper;

import com.das.province.infr.dataobject.AccUserRoleRelDO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface AccUserRoleRelMapper {
    int deleteByPrimaryKey(Long id);
    int insert(AccUserRoleRelDO record);
    int insertSelective(AccUserRoleRelDO record);
    AccUserRoleRelDO selectByPrimaryKey(Long id);
    int updateByPrimaryKeySelective(AccUserRoleRelDO record);
    int updateByPrimaryKey(AccUserRoleRelDO record);
    List<AccUserRoleRelDO> listByRoleId(@Param("companyId") Long companyId, @Param("roleId") Long roleId);
    List<AccUserRoleRelDO> listByUserId(@Param("companyId") Long companyId, @Param("userId") Long userId);
    List<AccUserRoleRelDO> listByUserIds(@Param("companyId") Long companyId, @Param("userIds") List<Long> userIds);
    List<AccUserRoleRelDO> listByRoleIds(@Param("companyId") Long companyId, @Param("roleIds") List<Long> roleIds);
    int batchDeleteUserRole(@Param("companyId") Long companyId, @Param("userIds") List<Long> userIds);
    int batchInsertUserRole(@Param("records") List<AccUserRoleRelDO> records);

    List<AccUserRoleRelDO> listByCompanyId(@Param("companyId") Long companyId);
}