package com.das.province.infr.mapper;

import com.das.province.infr.dataobject.DataChangeDetailTemporaryDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface DataChangeDetailTemporaryMapper {
    int deleteByPrimaryKey(Long id);

    int insert(DataChangeDetailTemporaryDO record);

    int insertSelective(DataChangeDetailTemporaryDO record);

    DataChangeDetailTemporaryDO selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(DataChangeDetailTemporaryDO record);

    int updateByPrimaryKey(DataChangeDetailTemporaryDO record);

    List<DataChangeDetailTemporaryDO> selectList();

    void deleteAll();
}