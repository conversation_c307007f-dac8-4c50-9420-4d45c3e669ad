package com.das.province.infr.mapper;

import com.das.province.infr.dataobject.DataChangeInfoDO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface DataChangeInfoMapper {
    int deleteByPrimaryKey(Long id);

    int insert(DataChangeInfoDO record);

    int insertSelective(DataChangeInfoDO record);

    DataChangeInfoDO selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(DataChangeInfoDO record);

    int updateByPrimaryKey(DataChangeInfoDO record);

    List<DataChangeInfoDO> listByCondition(@Param("changeType") Byte changeType,
                                           @Param("startTime") String startTime,
                                           @Param("endTime") String endTime,
                                           @Param("sortBy") String sortBy);
}