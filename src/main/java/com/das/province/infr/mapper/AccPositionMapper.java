package com.das.province.infr.mapper;

import com.das.province.infr.dataobject.AccPositionDO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface AccPositionMapper {
    int deleteByPrimaryKey(Long id);
    int insert(AccPositionDO record);
    int insertSelective(AccPositionDO record);
    AccPositionDO selectByPrimaryKey(Long id);
    int updateByPrimaryKeySelective(AccPositionDO record);
    int updateByPrimaryKey(AccPositionDO record);
    AccPositionDO selectByDepartmentId(@Param("companyId") Long companyId, @Param("departmentId") Long departmentId,
                                       @Param("positionName") String positionName, @Param("isDelete") Byte isDelete);
    AccPositionDO selectByPositionId(@Param("companyId") Long companyId, @Param("positionId") Long positionId, @Param("isDelete") Byte isDelete);
    List<AccPositionDO> listByPositionIds(@Param("companyId") Long companyId, @Param("positionIds") List<Long> positionIds, @Param("isDelete") Byte isDelete);
    List<AccPositionDO> listByNotInPositionIds(@Param("companyId") Long companyId, @Param("positionIds") List<Long> positionIds, @Param("isDelete") Byte isDelete);
    List<AccPositionDO> listByCondition(@Param("companyId") Long companyId, @Param("departmentIds") List<Long> departmentIds, @Param("isDelete") Byte isDelete);
    List<AccPositionDO> listByDepartmentIds(@Param("companyId") Long companyId, @Param("departmentIds") List<Long> departmentIds, @Param("isDelete") Byte isDelete);
    int updateByPositionIds(@Param("record") AccPositionDO record, @Param("companyId") Long companyId,
                            @Param("positionIds") List<Long> positionIds);
}