package com.das.province.infr.mapper;

import com.das.province.infr.dataobject.CommClassificationItemRelDO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface CommClassificationItemRelMapper {
    int deleteByPrimaryKey(Long id);
    int insert(CommClassificationItemRelDO record);
    int insertSelective(CommClassificationItemRelDO record);
    CommClassificationItemRelDO selectByPrimaryKey(Long id);
    int updateByPrimaryKeySelective(CommClassificationItemRelDO record);
    int updateByPrimaryKey(CommClassificationItemRelDO record);
    void insertParentPath(@Param("classificationId") Long classificationId,
                          @Param("parentId") Long parentId);
    int deleteByDescendantId(@Param("descendantId") Long descendantId);
    int deleteBatchByDescendantId(@Param("descendantIds") List<Long> descendantIds);
    List<CommClassificationItemRelDO> listByParentId(@Param("parentId") Long parentId);
}