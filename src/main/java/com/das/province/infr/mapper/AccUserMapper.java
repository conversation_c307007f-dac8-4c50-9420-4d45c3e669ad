package com.das.province.infr.mapper;

import com.das.province.infr.dataobject.AccUserDO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface AccUserMapper {
    int deleteByPrimaryKey(Long id);

    int insert(AccUserDO record);

    int insertSelective(AccUserDO record);

    AccUserDO selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(AccUserDO record);

    int updateByPrimaryKey(AccUserDO record);
    int updateByIdAndCompanyId(AccUserDO record);
    int deleteByUserId(@Param("companyId") Long companyId, @Param("userId") Long userId);
    AccUserDO selectByUserIdAndIsDelete(@Param("userId") Long userId, @Param("isDelete") Byte isDelete);
    AccUserDO selectByLoginAccountAndIsDelete(@Param("loginAccount") String loginAccount, @Param("isDelete") Byte isDelete);
    AccUserDO selectByUserId(@Param("companyId") Long companyId, @Param("userId") Long userId);
    List<AccUserDO> listByUserId(@Param("companyId") Long companyId, @Param("userIds") List<Long> userIds);
    List<AccUserDO> listNotInUserIds(@Param("companyId") Long companyId,@Param("userIds") List<Long> userIds,@Param("isDelete") Byte isDelete);

    AccUserDO selectByLoginAccount(@Param("companyId") Long companyId, @Param("loginAccount") String loginAccount, @Param("userType") String userType);
    AccUserDO selectByLoginAccountAndPassword(@Param("loginAccount") String loginAccount, @Param("password") String password,
                                              @Param("isDelete") Byte isDelete, @Param("accountStatus") Byte accountStatus, @Param("userType") String userType);
    List<AccUserDO> listByCondition(@Param("companyId") Long companyId, @Param("departmentIds") List<Long> departmentIds,
                                    @Param("accountStatus") Byte accountStatus, @Param("queryContent") String queryContent,
                                    @Param("sortBy")  String sortBy, @Param("isDelete") Byte isDelete);
    List<AccUserDO> listByConditionNoPage(@Param("companyId") Long companyId, @Param("realName") String realName,@Param("isDelete") Byte isDelete);
    List<AccUserDO> listByPositionId(@Param("companyId") Long companyId, @Param("positionId") Long positionId, @Param("isDelete") Byte isDelete);
    List<AccUserDO> listByPositionIds(@Param("companyId") Long companyId, @Param("positionIds") List<Long> positionIds, @Param("isDelete") Byte isDelete);
    int updateByUserIds(@Param("record") AccUserDO record, @Param("userIds") List<Long> userIds);
    List<AccUserDO> listByIds(@Param("companyId") Long companyId, @Param("ids") List<Long> ids);

    List<AccUserDO> listByDepartmentIds(@Param("companyId") Long companyId, @Param("departmentIds") List<Long> departmentIds, @Param("isDelete") Byte isDelete);

    List<AccUserDO> listByIsDelete(@Param("userName") String userName, @Param("isDelete") Byte isDelete);
}