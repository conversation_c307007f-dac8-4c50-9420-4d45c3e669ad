package com.das.province.infr.mapper;

import com.das.province.infr.dataobject.StatisticMuseumClassifyDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface StatisticMuseumClassifyMapper {
    int deleteByPrimaryKey(Long id);

    int insert(StatisticMuseumClassifyDO record);

    int insertSelective(StatisticMuseumClassifyDO record);

    StatisticMuseumClassifyDO selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(StatisticMuseumClassifyDO record);

    int updateByPrimaryKey(StatisticMuseumClassifyDO record);

    List<StatisticMuseumClassifyDO> selectList();

    void deleteAll();
}