package com.das.province.infr.mapper;

import com.das.province.infr.dataobject.SyncSafeDeviceInfoRealtimeDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Mapper
public interface SyncSafeDeviceInfoRealtimeMapper {
    int deleteByPrimaryKey(Long id);

    int insert(SyncSafeDeviceInfoRealtimeDO record);

    int insertSelective(SyncSafeDeviceInfoRealtimeDO record);

    SyncSafeDeviceInfoRealtimeDO selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(SyncSafeDeviceInfoRealtimeDO record);

    int updateByPrimaryKey(SyncSafeDeviceInfoRealtimeDO record);

    SyncSafeDeviceInfoRealtimeDO selectByDeviceCode(@Param("deviceCode") String deviceCode);

    int updateByDeviceCode(SyncSafeDeviceInfoRealtimeDO record);

    List<SyncSafeDeviceInfoRealtimeDO> selectList();
}