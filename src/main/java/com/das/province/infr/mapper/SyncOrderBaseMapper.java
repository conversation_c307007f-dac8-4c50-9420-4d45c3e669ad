package com.das.province.infr.mapper;

import com.das.province.infr.dataobject.ListGroupByProvinceDO;
import com.das.province.infr.dataobject.SyncOrderBaseDO;
import com.das.province.infr.dataobjectexpand.GroupOrderBaseDO;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

public interface SyncOrderBaseMapper {
    int deleteByPrimaryKey(Long id);

    /**
     * 按时间删除（伪满特殊使用）
     * @return
     */
    int deleteWmByTime(@Param("startTime") Date startTime,
                       @Param("endTime") Date endTime);

    /**
     * 按时间删除（东北沦陷特殊使用）
     * @return
     */
    int deleteLxByTime(@Param("startTime") Date startTime,
                       @Param("endTime") Date endTime);

    int deleteByUniqueCode(@Param("uniqueCode") String uniqueCode,
                           @Param("writeOffDateStart") Date writeOffDateStart,
                           @Param("writeOffDateEnd") Date writeOffDateEnd);

    int insert(SyncOrderBaseDO record);

    int insertSelective(SyncOrderBaseDO record);

    int batchInsert(@Param("orderList") List<SyncOrderBaseDO> orderList);

    SyncOrderBaseDO selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(SyncOrderBaseDO record);

    int updateByPrimaryKey(SyncOrderBaseDO record);

    SyncOrderBaseDO selectUniqueCodeAndOrderTypeAndStatisticsDate(@Param("uniqueCode") String uniqueCode, @Param("orderType") Byte orderType,
                                                                  @Param("statisticsDate") Date statisticsDate);

    /**
     * 查询所有订单列表
     * @return
     */
    List<SyncOrderBaseDO> selectList();

    List<GroupOrderBaseDO> groupOrderBase();

    List<SyncOrderBaseDO> listUniqueCode(@Param("uniqueCode") String uniqueCode);

    List<String> listOrderNoUniqueCode(@Param("uniqueCode") String uniqueCode);

    List<SyncOrderBaseDO> listByReserveDate(@Param("status") Byte status,
                                            @Param("reserveDateStart") Date reserveDateStart,
                                            @Param("reserveDateEnd") Date reserveDateEnd,
                                            @Param("uniqueCodes") List<String> uniqueCodes,
                                            @Param("orderType") Byte orderType);

    int countByReserveDate(@Param("status") Byte status,
                                            @Param("reserveDateStart") Date reserveDateStart,
                                            @Param("reserveDateEnd") Date reserveDateEnd,
                                            @Param("uniqueCodes") List<String> uniqueCodes,
                                            @Param("orderType") Byte orderType);

    List<ListGroupByProvinceDO> listGroupByProvince(@Param("status") Byte status,
                                                    @Param("reserveDateStart") Date reserveDateStart,
                                                    @Param("reserveDateEnd") Date reserveDateEnd,
                                                    @Param("uniqueCodes") List<String> uniqueCodes,
                                                    @Param("orderType") Byte orderType);
}