package com.das.province.infr.mapper;

import com.das.province.infr.dataobject.AccPermissionItemRelDO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface AccPermissionItemRelMapper {
    int deleteByPrimaryKey(Long id);
    int insert(AccPermissionItemRelDO record);
    int insertSelective(AccPermissionItemRelDO record);
    AccPermissionItemRelDO selectByPrimaryKey(Long id);
    int updateByPrimaryKeySelective(AccPermissionItemRelDO record);
    int updateByPrimaryKey(AccPermissionItemRelDO record);
    void insertParentPath(@Param("permissionId") Long permissionId,
                          @Param("parentId") Long parentId);
    int deleteBatchByDescendantIds(@Param("descendantIds") List<Long> descendantIds);
}