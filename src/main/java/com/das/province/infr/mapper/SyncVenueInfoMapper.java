package com.das.province.infr.mapper;

import com.das.province.infr.dataobject.SyncVenueInfoDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Mapper
@Repository
public interface SyncVenueInfoMapper {
    int deleteByPrimaryKey(Long id);

    int insert(SyncVenueInfoDO record);

    int insertSelective(SyncVenueInfoDO record);

    SyncVenueInfoDO selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(SyncVenueInfoDO record);

    int updateByPrimaryKey(SyncVenueInfoDO record);

    SyncVenueInfoDO selectByMuseumId(@Param("uniqueCode") String uniqueCode);

    int updateByUniqueCode(SyncVenueInfoDO record);

    List<SyncVenueInfoDO> selectListByUniqueCodes(@Param("uniqueCodes") List<String> uniqueCodes);
}