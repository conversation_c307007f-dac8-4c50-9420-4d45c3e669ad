package com.das.province.infr.mapper;

import com.das.province.infr.dataobject.SyncUserInfoDO;
import org.apache.ibatis.annotations.Param;

import java.util.Date;

import java.util.List;

public interface SyncUserInfoMapper {
    int deleteByPrimaryKey(Long id);

    int insert(SyncUserInfoDO record);

    int insertSelective(SyncUserInfoDO record);

    SyncUserInfoDO selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(SyncUserInfoDO record);

    int updateByPrimaryKey(SyncUserInfoDO record);

    List<SyncUserInfoDO> selectList();

    SyncUserInfoDO selectUniqueCodeAndPostCodeAndstatisticsDate(@Param("uniqueCode") String uniqueCode, @Param("postCode") String postCode,
                                                                @Param("statisticsDate") Date statisticsDate);

    List<SyncUserInfoDO> listUniqueCode(@Param("uniqueCode") String uniqueCode);

    int deleteByUniqueCode(@Param("uniqueCode") String uniqueCode);
}