package com.das.province.infr.mapper;

import com.das.province.infr.dataobject.SyncCommCollectionRegisterInfoDO;
import com.das.province.infr.dataobject.SyncOrderBaseDO;
import com.das.province.infr.dataobjectexpand.CollectionGroupByLevelDO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface SyncCommCollectionRegisterInfoMapper {
    int deleteByPrimaryKey(Long id);

    int insert(SyncCommCollectionRegisterInfoDO record);

    int insertSelective(SyncCommCollectionRegisterInfoDO record);

    int batchInsert(@Param("collectionList") List<SyncCommCollectionRegisterInfoDO> collectionList);

    SyncCommCollectionRegisterInfoDO selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(SyncCommCollectionRegisterInfoDO record);

    int updateByPrimaryKey(SyncCommCollectionRegisterInfoDO record);

    List<SyncCommCollectionRegisterInfoDO> selectList();

    int deleteByUniqueCode(@Param("uniqueCode") String uniqueCode);

    int deleteByUniqueCodeAndRegisterNos(@Param("uniqueCode") String uniqueCode, @Param("registerNos") List<String> registerNos);

    SyncCommCollectionRegisterInfoDO selectUniqueCodeAndRegisterNo(@Param("uniqueCode") String uniqueCode, @Param("registerNo") String registerNo);

    List<SyncCommCollectionRegisterInfoDO> selectListByUniqueCode(@Param("uniqueCode") String uniqueCode);

    List<SyncCommCollectionRegisterInfoDO> listUniqueCode(@Param("uniqueCode") String uniqueCode);

    List<SyncCommCollectionRegisterInfoDO> queryByCondition(@Param("uniqueCodes") List<String> uniqueCodes,
                                                            @Param("collectionName") String collectionName,
                                                            @Param("levelName") String levelName,
                                                            @Param("categoryName") String categoryName,
                                                            @Param("age") String age,
                                                            @Param("selectTexture") String selectTexture,
                                                            @Param("ids") List<Long> ids,
                                                            @Param("completeDegreeName") String completeDegreeName,
                                                            @Param("sourceName") String sourceName);

    List<CollectionGroupByLevelDO> collectionGroupByLevel();

    int countByUniqueCode(@Param("uniqueCode") String uniqueCode,
                          @Param("registerStatus") String registerStatus);
}