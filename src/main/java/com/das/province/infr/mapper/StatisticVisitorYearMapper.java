package com.das.province.infr.mapper;

import com.das.province.infr.dataobject.StatisticVisitorYearDO;
import com.das.province.infr.dataobjectexpand.GroupVisitorYearByCityDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface StatisticVisitorYearMapper {
    int deleteByPrimaryKey(Long id);

    int insert(StatisticVisitorYearDO record);

    int insertSelective(StatisticVisitorYearDO record);

    StatisticVisitorYearDO selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(StatisticVisitorYearDO record);

    int updateByPrimaryKey(StatisticVisitorYearDO record);

    List<StatisticVisitorYearDO> selectList();

    List<GroupVisitorYearByCityDO> groupVisitorYearByCity();

    List<StatisticVisitorYearDO> selectTopList();

    List<StatisticVisitorYearDO> selectListByRegionCode(@Param("regionCode") String regionCode);

    List<StatisticVisitorYearDO> selectListMuseumIds(@Param("museumIds") List<String> museumIds);

    void deleteAll();

    void batchInsert(@Param("visitorYearDOList") List<StatisticVisitorYearDO> visitorYearDOList);

}