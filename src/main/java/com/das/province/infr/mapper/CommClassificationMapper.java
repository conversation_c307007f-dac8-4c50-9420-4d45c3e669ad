package com.das.province.infr.mapper;

import com.das.province.infr.dataobject.CommClassificationDO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface CommClassificationMapper {
    int deleteByPrimaryKey(Long id);
    int insert(CommClassificationDO record);
    int insertSelective(CommClassificationDO record);
    CommClassificationDO selectByPrimaryKey(Long id);
    int updateByPrimaryKeySelective(CommClassificationDO record);
    int updateByPrimaryKey(CommClassificationDO record);
    List<CommClassificationDO> listByIds(@Param("companyId") Long companyId,
                                         @Param("ids") List<Long> ids);
    CommClassificationDO selectById(@Param("companyId") Long companyId,
                                    @Param("id") Long id);
    CommClassificationDO selectRootByCompanyIdAndType(@Param("companyId") Long companyId,
                                                      @Param("bizType") Byte bizType,
                                                      @Param("bizId") Long bizId,
                                                      @Param("bizCode") String bizCode);
    List<CommClassificationDO> selectAllChildById(@Param("companyId") Long companyId,
                                                  @Param("bizType") Byte bizType,
                                                  @Param("bizId") Long bizId,
                                                  @Param("bizCode") String bizCode);
    List<CommClassificationDO> selectIncludeChildById(@Param("companyId") Long companyId,
                                                      @Param("id") Long id);
    List<CommClassificationDO> selectChildById(@Param("companyId") Long companyId,
                                               @Param("id") Long id);
    List<CommClassificationDO> selectChildByParentId(@Param("companyId") Long companyId,
                                                     @Param("parentId") Long parentId);
    CommClassificationDO selectByParentIdAndName(@Param("companyId") Long companyId,
                                                 @Param("parentId") Long parentId,
                                                 @Param("name") String name);
    List<CommClassificationDO> selectIncludeParentById(@Param("companyId") Long companyId, @Param("id") Long id);
    List<CommClassificationDO> selectParentById(@Param("companyId") Long companyId, @Param("id") Long id);
    CommClassificationDO selectByTypeAndParentIdAndName(@Param("companyId") Long companyId,
                                                        @Param("bizType") Byte bizType,
                                                        @Param("parentId") Long parentId,
                                                        @Param("name") String name);
    CommClassificationDO selectByBizCodeAndBizType(@Param("companyId") Long companyId,
                                                   @Param("bizType") Byte bizType,
                                                   @Param("bizCode") String bizCode);
    int deleteBatchById(@Param("companyId") Long companyId, @Param("ids") List<Long> ids);
    CommClassificationDO selectByTypeAndNameAndLevel(@Param("companyId") Long companyId,
                                                     @Param("bizType") Byte bizType,
                                                     @Param("name") String name,
                                                     @Param("level") Integer level);
    List<CommClassificationDO> selectByTypeAndLevel(@Param("companyId") Long companyId,
                                                     @Param("bizType") Byte bizType,
                                                     @Param("level") Integer level);

    List<CommClassificationDO> selectBybizCodeList(@Param("companyId") Long companyId,
                                                   @Param("bizType") Byte bizType,
                                                   @Param("bizCodes") List<String> bizCodes);

    /**
     * 根据parentId
     * @param companyId
     * @param id
     * @param name
     * @return
     */
    List<CommClassificationDO> selectByParentAndName(@Param("companyId") Long companyId,
                                                     @Param("id") Long id,
                                                     @Param("name") String name);

    int updateEnableStatusByIds(@Param("companyId") Long companyId,
                                @Param("userId") Long userId,
                                @Param("enabledStatus") byte enabledStatus,
                                @Param("classificationIds") List<Long> classificationIds);
}