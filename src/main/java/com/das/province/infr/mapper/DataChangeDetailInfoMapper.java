package com.das.province.infr.mapper;

import com.das.province.infr.dataobject.DataChangeDetailInfoDO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface DataChangeDetailInfoMapper {
    int deleteByPrimaryKey(Long id);

    int insert(DataChangeDetailInfoDO record);

    int insertSelective(DataChangeDetailInfoDO record);

    DataChangeDetailInfoDO selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(DataChangeDetailInfoDO record);

    int updateByPrimaryKey(DataChangeDetailInfoDO record);

    List<DataChangeDetailInfoDO> selectListByChangeId(@Param("changeInfoId") Long changeInfoId);

    DataChangeDetailInfoDO selectByChangeId(@Param("changeInfoId") Long changeInfoId);
}