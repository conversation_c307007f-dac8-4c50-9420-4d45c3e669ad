package com.das.province.infr.mapper;

import com.das.province.infr.dataobject.SyncSafeDeviceInfoDO;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

public interface SyncSafeDeviceInfoMapper {
    int deleteByPrimaryKey(Long id);

    int insert(SyncSafeDeviceInfoDO record);

    int insertSelective(SyncSafeDeviceInfoDO record);

    SyncSafeDeviceInfoDO selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(SyncSafeDeviceInfoDO record);

    int updateByPrimaryKey(SyncSafeDeviceInfoDO record);

    List<SyncSafeDeviceInfoDO> selectByIds(@Param("ids") List<Long> ids);

    List<SyncSafeDeviceInfoDO> listByConditionNew(@Param("uniqueCode") String uniqueCode, @Param("regionCode") String regionCode,
                                               @Param("exceptionDegree") String exceptionDegree, @Param("regionName") String regionName);

    List<SyncSafeDeviceInfoDO> listByDeviceCode(@Param("deviceCode") String deviceCode, @Param("exceptionDegreeList") List<String> exceptionDegreeList,
                                                @Param("startTime") Date startTime, @Param("endTime") Date endTime);

    List<SyncSafeDeviceInfoDO> listMonitorException(@Param("uniqueCode") String uniqueCode, @Param("regionCode") String regionCode,
                                                @Param("startTime") Date startTime, @Param("endTime") Date endTime);

    List<SyncSafeDeviceInfoDO> listDeviceMonitorThException(@Param("uniqueCode") String uniqueCode, @Param("regionCode") String regionCode,
                                                            @Param("regionName") String regionName, @Param("deviceAddress") String deviceAddress,
                                                            @Param("deviceStatus") String deviceStatus, @Param("exceptionDegree") String exceptionDegree,
                                                            @Param("startTime") Date startTime, @Param("endTime") Date endTime, @Param("flag") String flag);

    List<SyncSafeDeviceInfoDO> listMonitorOfflineException(@Param("uniqueCode") String uniqueCode, @Param("regionCode") String regionCode, @Param("regionName") String regionName,
                                                           @Param("deviceAddress") String deviceAddress, @Param("exceptionDegree") String exceptionDegree,
                                                           @Param("startTime") Date startTime, @Param("endTime") Date endTime);

    List<SyncSafeDeviceInfoDO> listMonitorOnlineException(@Param("uniqueCode") String uniqueCode, @Param("regionCode") String regionCode, @Param("regionName") String regionName,
                                                           @Param("deviceAddress") String deviceAddress, @Param("exceptionDegree") String exceptionDegree,
                                                           @Param("startTime") Date startTime, @Param("endTime") Date endTime);

    List<SyncSafeDeviceInfoDO> listMonitorTemperatureDeviceCode(@Param("deviceCode") String deviceCode, @Param("startTime") Date startTime, @Param("endTime") Date endTime);

    List<SyncSafeDeviceInfoDO> listMonitorHumidityDeviceCode(@Param("deviceCode") String deviceCode, @Param("startTime") Date startTime, @Param("endTime") Date endTime);

}