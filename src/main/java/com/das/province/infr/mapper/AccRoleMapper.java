package com.das.province.infr.mapper;

import com.das.province.infr.dataobject.AccRoleDO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface AccRoleMapper {
    int deleteByPrimaryKey(Long id);
    int insert(AccRoleDO record);
    int insertSelective(AccRoleDO record);
    AccRoleDO selectByPrimaryKey(Long id);
    int updateByPrimaryKeySelective(AccRoleDO record);
    int updateByPrimaryKey(AccRoleDO record);
    AccRoleDO selectByRoleName(@Param("companyId") Long companyId, @Param("roleName") String roleName, @Param("isDelete") Byte isDelete);
    List<AccRoleDO> listByCondition(@Param("companyId") Long companyId, @Param("roleName") String roleName, @Param("isDelete") Byte isDelete);
    AccRoleDO selectByRoleId(@Param("companyId") Long companyId, @Param("roleId") Long roleId, @Param("isDelete") Byte isDelete);
    List<AccRoleDO> listByRoleIds(@Param("companyId") Long companyId, @Param("roleIds") List<Long> roleIds, @Param("isDelete") Byte isDelete);
    int updateByRoleIds(@Param("record") AccRoleDO record, @Param("companyId") Long companyId, @Param("roleIds") List<Long> roleIds);
    List<AccRoleDO> selectRoleList(@Param("companyId") Long companyId);
}