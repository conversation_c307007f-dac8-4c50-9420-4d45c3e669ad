package com.das.province.infr.mapper;

import com.das.province.infr.dataobject.MuseumBaseInfoDO;
import com.das.province.infr.dataobjectexpand.CityMuseumNumDO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface MuseumBaseInfoMapper {
    int deleteByPrimaryKey(String id);

    int insert(MuseumBaseInfoDO record);

    int insertSelective(MuseumBaseInfoDO record);

    MuseumBaseInfoDO selectByPrimaryKey(String id);

    int updateByPrimaryKeySelective(MuseumBaseInfoDO record);

    int updateByPrimaryKey(MuseumBaseInfoDO record);

    List<MuseumBaseInfoDO> listByCondition(@Param("museumId") String museumId,
                                           @Param("settleIn") Byte settleIn,
                                           @Param("museumName") String museumName,
                                           @Param("regionCode") String regionCode,
                                           @Param("levelId") Long levelId,
                                           @Param("natureId") Long natureId,
                                           @Param("typeId") Long typeId,
                                           @Param("sortBy") String sortBy);

    void updateDeleteStatus(@Param("id") String id,
                            @Param("isDelete") Byte isDelete);

    /**
     * 按城市统计
     * @return
     */
    List<CityMuseumNumDO> groupCityMuseumNum(@Param("settleIn") Byte settleIn);

    /**
     * 根据博物馆id集合获取博物馆详情列表
     * @return
     */
    List<MuseumBaseInfoDO> selectListByIds(@Param("ids") List<String> ids);

    /**
     * 查询所有博物馆列表
     * @return
     */
    List<MuseumBaseInfoDO> selectList(@Param("isDelete") Byte isDelete);

    /**
     * 查询入驻博物馆列表
     * @param settleIn
     * @param regionCode
     * @return
     */
    List<MuseumBaseInfoDO> selectSettleInList(@Param("settleIn") Byte settleIn,
                                              @Param("regionCode") String regionCode);

    /**
     * 查询博物馆列表
     * @param settleIn
     * @param regionCode
     * @return
     */
    List<MuseumBaseInfoDO> selectListByParam(@Param("settleIn") Byte settleIn,
                                             @Param("regionCode") String regionCode,
                                             @Param("museumId") String museumId);

    List<MuseumBaseInfoDO> selectAll();

}