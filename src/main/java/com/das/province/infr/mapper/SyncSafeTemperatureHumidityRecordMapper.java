package com.das.province.infr.mapper;

import com.das.province.infr.dataobject.SyncSafeTemperatureHumidityRecordDO;

public interface SyncSafeTemperatureHumidityRecordMapper {
    int deleteByPrimaryKey(Long id);

    int insert(SyncSafeTemperatureHumidityRecordDO record);

    int insertSelective(SyncSafeTemperatureHumidityRecordDO record);

    SyncSafeTemperatureHumidityRecordDO selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(SyncSafeTemperatureHumidityRecordDO record);

    int updateByPrimaryKey(SyncSafeTemperatureHumidityRecordDO record);
}