package com.das.province.infr.mapper;

import com.das.province.infr.dataobject.SyncCulturalRelicsInfoDO;
import com.das.province.infr.dataobjectexpand.GroupCulturalRelicsByCodeDO;

import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface SyncCulturalRelicsInfoMapper {
    int deleteByPrimaryKey(Long id);

    int insert(SyncCulturalRelicsInfoDO record);

    int insertSelective(SyncCulturalRelicsInfoDO record);

    SyncCulturalRelicsInfoDO selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(SyncCulturalRelicsInfoDO record);

    int updateByPrimaryKey(SyncCulturalRelicsInfoDO record);

    List<GroupCulturalRelicsByCodeDO> groupCulturalRelicsByCode();

    List<SyncCulturalRelicsInfoDO> queryByCondition(@Param("uniqueCodes") List<String> uniqueCodes, @Param("levelName") String levelName,
                                              @Param("textureName") String textureName, @Param("sourceName") String sourceName,
                                              @Param("dynastyName") String dynastyName, @Param("completeDegreeName") String completeDegreeName,
                                              @Param("sortBy") String sortBy);

    List<SyncCulturalRelicsInfoDO> listUniqueCode(@Param("uniqueCode") String uniqueCode);

    int deleteByUniqueCode(@Param("uniqueCode") String uniqueCode);

    int deleteByUniqueCodeAndNumbers(@Param("uniqueCode") String uniqueCode, @Param("numbers") List<String> numbers);

    int batchInsert(@Param("culturalRelicsInfoList") List<SyncCulturalRelicsInfoDO> culturalRelicsInfoList);
}