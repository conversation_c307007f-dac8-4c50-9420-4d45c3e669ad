package com.das.province.infr.repository;

import com.das.province.common.utils.BeanCopyUtils;
import com.das.province.infr.dataobject.AccUserDO;
import com.das.province.infr.entity.UserEntity;
import com.das.province.infr.mapper.AccUserMapper;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
public class UserRepositoryImpl implements UserRepository {

    @Resource
    private AccUserMapper accUserMapper;

    @Override
    public void saveUserEntity(UserEntity userEntity) {
        AccUserDO userDO = BeanCopyUtils.copyByJSON(userEntity, AccUserDO.class);
        this.accUserMapper.insertSelective(userDO);
        userEntity.setUserId(userDO.getId());
    }

    @Override
    public void editUserEntity(UserEntity userEntity) {
        AccUserDO userDO = BeanCopyUtils.copyByJSON(userEntity, AccUserDO.class);
        userDO.setId(userEntity.getUserId());
        this.accUserMapper.updateByPrimaryKeySelective(userDO);
    }

    @Override
    public void delUserById(Long companyId, Long userId) {
        this.accUserMapper.deleteByUserId(companyId, userId);
    }
}
