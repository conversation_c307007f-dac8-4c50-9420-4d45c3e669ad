package com.das.province.infr.repository;

import com.das.province.common.utils.BeanCopyUtils;
import com.das.province.infr.dataobject.AccRoleDO;
import com.das.province.infr.entity.RoleEntity;
import com.das.province.infr.mapper.AccRoleMapper;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
public class RoleRepositoryImpl implements RoleRepository {

    @Resource
    private AccRoleMapper accRoleMapper;

    @Override
    public void saveRoleEntity(RoleEntity roleEntity) {
        AccRoleDO accRoleDO = BeanCopyUtils.copyByJSON(roleEntity, AccRoleDO.class);
        this.accRoleMapper.insertSelective(accRoleDO);
        roleEntity.setRoleId(accRoleDO.getId());
    }

    @Override
    public void editRoleEntity(RoleEntity roleEntity) {
        AccRoleDO accRoleDO = BeanCopyUtils.copyByJSON(roleEntity, AccRoleDO.class);
        accRoleDO.setId(roleEntity.getRoleId());
        this.accRoleMapper.updateByPrimaryKeySelective(accRoleDO);
    }
}
