package com.das.province.infr.repository;

import com.das.province.common.utils.BeanCopyUtils;
import com.das.province.infr.dataobject.AccPermissionDO;
import com.das.province.infr.dataobject.AccPermissionItemRelDO;
import com.das.province.infr.entity.FunctionPermissionVO;
import com.das.province.infr.entity.PermissionEntity;
import com.das.province.infr.mapper.AccPermissionItemRelMapper;
import com.das.province.infr.mapper.AccPermissionMapper;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

@Component
public class PermissionRepositoryImpl implements PermissionRepository {

    @Resource
    private AccPermissionMapper accPermissionMapper;

    @Resource
    private AccPermissionItemRelMapper accPermissionItemRelMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void savePermissionEntity(PermissionEntity permissionEntity) {
        AccPermissionDO menuPermissionDO = BeanCopyUtils.copyByJSON(permissionEntity, AccPermissionDO.class);
        this.accPermissionMapper.insertSelective(menuPermissionDO);
        Long permissionId = menuPermissionDO.getId();
        permissionEntity.setPermissionId(permissionId);
        this.saveUrlPath(permissionId, menuPermissionDO.getParentId());
        this.addFunctionPermission(permissionId, permissionEntity.getFunctionPermissionList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void editPermissionEntity(PermissionEntity permissionEntity) {
        Long permissionId = permissionEntity.getPermissionId();
        AccPermissionDO permissionDO = BeanCopyUtils.copyByJSON(permissionEntity, AccPermissionDO.class);
        permissionDO.setId(permissionId);
        this.accPermissionMapper.updateByPrimaryKeySelective(permissionDO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveFunctionPermissionEntity(PermissionEntity permissionEntity) {
        Long parentPermissionId = permissionEntity.getPermissionId();
        Long companyId = permissionEntity.getCompanyId();
        List<AccPermissionDO> oldFunctionPermissionList = this.accPermissionMapper.listDirectChildByPermissionId(companyId, parentPermissionId, (byte)0,  "FCN");
        this.accPermissionMapper.deleteFunctionByMenuId(companyId, parentPermissionId);
        if (CollectionUtils.isNotEmpty(oldFunctionPermissionList)) {
            List<Long> delFunctionIds = oldFunctionPermissionList.stream().map(AccPermissionDO::getId).collect(Collectors.toList());
            this.accPermissionItemRelMapper.deleteBatchByDescendantIds(delFunctionIds);
        }
        this.addFunctionPermission(parentPermissionId, permissionEntity.getFunctionPermissionList());
    }

    @Transactional(rollbackFor = Exception.class)
    public void addFunctionPermission(Long parentPermissionId, List<FunctionPermissionVO> functionPermissionList) {
        if (CollectionUtils.isEmpty(functionPermissionList)) {
            return;
        }
        functionPermissionList.forEach(functionPermissionVO -> {
            AccPermissionDO functionPermissionDO = BeanCopyUtils.copyByJSON(functionPermissionVO, AccPermissionDO.class);
            functionPermissionDO.setParentId(parentPermissionId);
            this.accPermissionMapper.insertSelective(functionPermissionDO);
            this.saveUrlPath(functionPermissionDO.getId(), parentPermissionId);
        });
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveUrlPath(Long currPermissionId, Long parentId) {
        AccPermissionItemRelDO selfRelationDo = new AccPermissionItemRelDO();
        selfRelationDo.setAncestorId(currPermissionId);
        selfRelationDo.setDescendantId(currPermissionId);
        selfRelationDo.setDistance(0);
        this.accPermissionItemRelMapper.insertSelective(selfRelationDo);
        this.accPermissionItemRelMapper.insertParentPath(currPermissionId, parentId);
    }
}
