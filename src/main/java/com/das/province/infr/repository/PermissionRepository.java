package com.das.province.infr.repository;

import com.das.province.infr.entity.FunctionPermissionVO;
import com.das.province.infr.entity.PermissionEntity;

import java.util.List;

public interface PermissionRepository {
    void savePermissionEntity(PermissionEntity permissionEntity);
    void editPermissionEntity(PermissionEntity permissionEntity);
    void saveFunctionPermissionEntity(PermissionEntity permissionEntity);
    void addFunctionPermission(Long parentPermissionId, List<FunctionPermissionVO> functionPermissionList);
    void saveUrlPath(Long currPermissionId, Long parentId);
}
