package com.das.province.infr.repository;

import com.das.province.common.utils.BeanCopyUtils;
import com.das.province.infr.dataobject.CommClassificationDO;
import com.das.province.infr.dataobject.CommClassificationItemRelDO;
import com.das.province.infr.entity.ClassificationEntity;
import com.das.province.infr.mapper.CommClassificationItemRelMapper;
import com.das.province.infr.mapper.CommClassificationMapper;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

@Component
public class ClassificationRepositoryImpl implements ClassificationRepository {

    @Resource
    private CommClassificationMapper commClassificationMapper;

    @Resource
    private CommClassificationItemRelMapper commClassificationItemRelMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveClassification(ClassificationEntity entity) {
        CommClassificationDO classificationDO = BeanCopyUtils.copyByJSON(entity, CommClassificationDO.class);
        this.commClassificationMapper.insertSelective(classificationDO);
        Long classificationId = classificationDO.getId();
        entity.setClassificationId(classificationId);
        CommClassificationItemRelDO selfRelationDo = new CommClassificationItemRelDO();
        selfRelationDo.setAncestorId(classificationId);
        selfRelationDo.setDescendantId(classificationId);
        selfRelationDo.setDistance(0);
        this.commClassificationItemRelMapper.insertSelective(selfRelationDo);
        this.commClassificationItemRelMapper.insertParentPath(classificationId, classificationDO.getParentId());
    }

    @Override
    public void updateClassification(ClassificationEntity entity) {
        CommClassificationDO classificationDO = BeanCopyUtils.copyByJSON(entity, CommClassificationDO.class);
        classificationDO.setId(entity.getClassificationId());
        this.commClassificationMapper.updateByPrimaryKeySelective(classificationDO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delClassificationByIds(Long companyId, List<Long> classificationIds) {
        if (CollectionUtils.isEmpty(classificationIds)) {
            return;
        }
        this.commClassificationMapper.deleteBatchById(companyId, classificationIds);
        this.commClassificationItemRelMapper.deleteBatchByDescendantId(classificationIds);
    }
}
