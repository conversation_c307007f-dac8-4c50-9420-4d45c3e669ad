package com.das.province.infr.repository;

import com.das.province.common.utils.BeanCopyUtils;
import com.das.province.infr.dataobject.AccPositionDO;
import com.das.province.infr.entity.PositionEntity;
import com.das.province.infr.mapper.AccPositionMapper;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

@Component
public class PositionRepositoryImpl implements PositionRepository {

    @Resource
    private AccPositionMapper accPositionMapper;

    @Override
    public void savePositionEntity(PositionEntity positionEntity) {
        AccPositionDO positionDO = BeanCopyUtils.copyByJSON(positionEntity, AccPositionDO.class);
        this.accPositionMapper.insertSelective(positionDO);
        positionEntity.setPositionId(positionDO.getId());
    }

    @Override
    public void editPositionEntity(PositionEntity positionEntity) {
        AccPositionDO positionDO = BeanCopyUtils.copyByJSON(positionEntity, AccPositionDO.class);
        positionDO.setId(positionEntity.getPositionId());
        this.accPositionMapper.updateByPrimaryKeySelective(positionDO);
    }

    @Override
    public List<PositionEntity> listByDepartmentIds(Long companyId, List<Long> departmentIds, Byte isDeleted) {
        List<AccPositionDO> positionDOList = this.accPositionMapper.listByDepartmentIds(companyId, departmentIds, isDeleted);
        if (CollectionUtils.isEmpty(positionDOList)) {
            return Lists.newArrayList();
        }
        return positionDOList.stream().map(positionDO -> {
            PositionEntity positionEntity = BeanCopyUtils.copyByJSON(positionDO, PositionEntity.class);
            positionEntity.setPositionId(positionDO.getId());
            return positionEntity;
        }).collect(Collectors.toList());
    }
}
