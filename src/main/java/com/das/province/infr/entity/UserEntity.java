package com.das.province.infr.entity;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class UserEntity implements Serializable {
    private static final long serialVersionUID = 5410079216165015887L;
    private Long userId;
    private Long companyId;
    private String userName;
    private String realName;
    private String loginAccount;
    private String password;
    private String sourcePassword;
    private Long avatar;
    private String phone;
    private String email;
    private Long departmentId;
    private Long positionId;
    private String postCode;
    private String userType;
    private String remark;
    private Byte accountStatus;
    private Byte isDelete;
    private Long creator;
    private Long modifier;
    private Date gmtCreate;
    private Date gmtModified;
    /**
     * 职位名称
     */
    private String positionName;

    /**
     * 领导级别
     */
    private Byte leaderLevel;

    /**
     * 领导id
     */
    private Long leaderId;
}
