package com.das.province.infr.entity;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class PositionEntity implements Serializable {
    private static final long serialVersionUID = 174438715984910129L;
    private Long positionId;
    private Long companyId;
    private String positionName;
    private Long departmentId;
    private String remark;
    private Byte isDelete;
    private Long creator;
    private Long modifier;
    private Date gmtCreate;
    private Date gmtModified;
}