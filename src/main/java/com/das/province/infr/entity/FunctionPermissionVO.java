package com.das.province.infr.entity;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;


@Data
public class FunctionPermissionVO implements Serializable {
    private static final long serialVersionUID = 3148647563222423239L;
    private Long permissionId;
    private Long companyId;
    private String permissionName;
    private String permissionCode;
    private String permissionType;
    private Long parentId;
    private Byte level;
    private Byte sortIndex;
    private String permission;
    private Byte permissionStatus;
    private Byte functionStatus;
    private Byte isDelete;
    private String resourceUrl;
    private String resourceIcon;
    private String remark;
    private Long creator;
    private Long modifier;
    private Date gmtCreate;
    private Date gmtModified;
}