package com.das.province.infr.dataobjectexpand;

import lombok.Data;

@Data
public class GroupCollectionByCityDO {

    /**
     * 行政区划编码
     */
    private String regionCode;

    /**
     * 城市名称
     */
    private String cityName;

    /**
     * 藏品数量
     */
    private Integer collectionNum;

    /**
     * 定级藏品数量
     */
    private Integer valuableNum;

    /**
     * 未定级藏品数量
     */
    private Integer exhibitionNum;

    /**
     * 文物全息展示数量
     */
    private Integer holographicNum;

    /**
     * 二维数字化数量
     */
    private Integer twoModelNum;

    /**
     * 三维数字化数量
     */
    private Integer threeModelNum;

}
