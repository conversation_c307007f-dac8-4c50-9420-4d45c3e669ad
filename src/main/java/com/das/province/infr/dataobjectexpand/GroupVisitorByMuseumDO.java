package com.das.province.infr.dataobjectexpand;

import lombok.Data;

@Data
public class GroupVisitorByMuseumDO {

    /**
     * 博物馆id
     */
    private String museumId;

    /**
     * 博物馆名称
     */
    private String museumName;

    /**
     * 观众总量
     */
    private Integer totalVisitorNum;

    /**
     * 线上访问总量
     */
    private Integer onlineNum;

    /**
     * 线下入馆观众
     */
    private Integer offlineNum;

    /**
     * 入馆个人
     */
    private Integer personalNum;

    /**
     * 入馆团队个数
     */
    private Integer teamNum;

    /**
     * 入馆团队人数
     */
    private Integer teamPeopleNum;

    /**
     * 预约观众
     */
    private Integer reserveNum;

    /**
     * 成年观众
     */
    private Integer adultNum;

    /**
     * 未成年观众
     */
    private Integer childrenNum;

    /**
     * 本地观众
     */
    private Integer provinceNum;

    /**
     * 国内异地观众
     */
    private Integer outsideNum;

    /**
     * 境外观众
     */
    private Integer abroadNum;

    /**
     * 男性观众
     */
    private Integer manNum;

    /**
     * 女性观众
     */
    private Integer womanNum;
}
