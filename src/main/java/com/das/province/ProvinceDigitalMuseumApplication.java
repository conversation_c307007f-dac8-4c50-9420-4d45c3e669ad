package com.das.province;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.session.data.redis.config.annotation.web.http.EnableRedisHttpSession;
import org.springframework.web.servlet.config.annotation.EnableWebMvc;

@SpringBootApplication(scanBasePackages = {"com.das.province"})
@EnableScheduling
@EnableAsync
@EnableRedisHttpSession
@EnableWebMvc
public class ProvinceDigitalMuseumApplication {

	public static void main(String[] args) {
		SpringApplication.run(ProvinceDigitalMuseumApplication.class, args);
		System.out.println("==========> province digital museum start successful <==========");
	}

}
