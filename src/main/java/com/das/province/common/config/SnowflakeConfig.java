package com.das.province.common.config;

import com.das.province.common.utils.UniqueIDUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
@Slf4j
public class SnowflakeConfig {
    @Value("${snowflake.workerId}")
    private Long workerId;
    @Value("${snowflake.datacenterId}")
    private Long datacenterId;
    @Bean
    public UniqueIDUtil creatUniqueID() {
        return new UniqueIDUtil(workerId, datacenterId);
    }
}
