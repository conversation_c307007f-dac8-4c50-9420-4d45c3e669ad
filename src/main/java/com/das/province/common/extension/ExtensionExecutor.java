package com.das.province.common.extension;


import com.das.province.common.exception.CommonErrorCodeEnum;
import com.das.province.common.exception.CommonException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
@Slf4j
public class ExtensionExecutor extends AbstractComponentExecutor {
    @Resource
    private ExtensionRepository extensionRepository;
    @Override
    public <C> C locateComponent(Class<C> targetClz, BizScenario bizScenario) {
        C extension = locateExtension(targetClz, bizScenario);
        log.debug("[Located Extension]: " + extension.getClass().getSimpleName());
        return extension;
    }
    protected <Ext> Ext locateExtension(Class<Ext> targetClz, BizScenario bizScenario) {
        checkNull(bizScenario);
        Ext extension;
        String bizScenarioUniqueIdentity = bizScenario.getUniqueIdentity();
        extension = firstTry(targetClz, bizScenarioUniqueIdentity);
        if (extension != null) {
            return extension;
        }
        extension = loopTry(targetClz, bizScenarioUniqueIdentity);
        if (extension != null) {
            return extension;
        }
        throw new CommonException(CommonErrorCodeEnum.EXTENSION_EXECUTOR_ERROR.getCode(), "Can not find extension with ExtensionPoint: " + targetClz + " BizScenario:" + bizScenarioUniqueIdentity);
    }
    private <Ext> Ext firstTry(Class<Ext> targetClz, String bizScenario) {
        return (Ext) extensionRepository.getExtensionRepo().get(new ExtensionCoordinate(targetClz.getName(), bizScenario));
    }
    private <Ext> Ext loopTry(Class<Ext> targetClz, String bizScenario) {
        Ext extension;
        if (bizScenario == null) {
            return null;
        }
        int lastDotIndex = bizScenario.lastIndexOf(ExtensionConstant.DOT_SEPARATOR);
        while (lastDotIndex != -1) {
            bizScenario = bizScenario.substring(0, lastDotIndex);
            extension = (Ext) extensionRepository.getExtensionRepo().get(new ExtensionCoordinate(targetClz.getName(), bizScenario));
            if (extension != null) {
                return extension;
            }
            lastDotIndex = bizScenario.lastIndexOf(ExtensionConstant.DOT_SEPARATOR);
        }
        return null;
    }
    private void checkNull(BizScenario bizScenario) {
        if (bizScenario == null) {
            throw new CommonException(CommonErrorCodeEnum.EXTENSION_EXECUTOR_ERROR.getCode(), "BizScenario can not be null for extension");
        }
    }
}
