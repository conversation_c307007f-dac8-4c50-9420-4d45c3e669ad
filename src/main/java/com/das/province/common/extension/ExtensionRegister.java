package com.das.province.common.extension;

import com.das.province.common.exception.CommonErrorCodeEnum;
import com.das.province.common.exception.CommonException;
import com.das.province.common.utils.ApplicationContextHelper;
import org.apache.commons.lang3.ClassUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.DependsOn;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.List;
import java.util.Map;


@Component
@DependsOn(value = {"applicationContextHelper"})
public class ExtensionRegister {
    @Resource
    private ExtensionRepository extensionRepository;
    @PostConstruct
    public void doRegistration() {
        Map<String, ExtensionPointI> types = ApplicationContextHelper.getBeansOfType(ExtensionPointI.class);
        for (ExtensionPointI extension : types.values()) {
            Extension extensionAnn = extension.getClass().getDeclaredAnnotation(Extension.class);
            String extPtClassName = calculateExtensionPoint(extension.getClass());
            BizScenario bizScenario = BizScenario.valueOf(extensionAnn.bizId(), extensionAnn.useCase(), extensionAnn.scenario());
            ExtensionCoordinate extensionCoordinate = new ExtensionCoordinate(extPtClassName, bizScenario.getUniqueIdentity());
            ExtensionPointI preVal = extensionRepository.getExtensionRepo().put(extensionCoordinate, extension);
            if (preVal != null) {
                throw new CommonException(CommonErrorCodeEnum.EXTENSION_EXECUTOR_ERROR.getCode(), "Duplicate registration is not allowed for :" + extensionCoordinate);
            }
        }
    }
    private String calculateExtensionPoint(Class<?> targetClz) {
        List<Class<?>> interfaces = ClassUtils.getAllInterfaces(targetClz);
        if (CollectionUtils.isEmpty(interfaces)) {
            throw new CommonException(CommonErrorCodeEnum.EXTENSION_EXECUTOR_ERROR.getCode(), "Please assign a extension point interface for " + targetClz);
        }
        for (Class intf : interfaces) {
            String extensionPoint = intf.getSimpleName();
            if (StringUtils.contains(extensionPoint, ExtensionConstant.EXTENSION_EXTPT_NAMING)) {
                return intf.getName();
            }
        }
        throw new CommonException(CommonErrorCodeEnum.EXTENSION_EXECUTOR_ERROR.getCode(), "Your name of ExtensionPoint for " + targetClz + " is not valid, must be end of " + ExtensionConstant.EXTENSION_EXTPT_NAMING);
    }
}