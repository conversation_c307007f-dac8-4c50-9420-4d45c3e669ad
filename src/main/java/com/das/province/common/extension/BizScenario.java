package com.das.province.common.extension;


import com.google.common.base.Preconditions;
import org.apache.commons.lang3.StringUtils;

public class BizScenario {
    public final static String DEFAULT_BIZ_ID = "defaultBizId";
    public final static String DEFAULT_USE_CASE = "defaultUseCase";
    public final static String DEFAULT_SCENARIO = "defaultScenario";
    private final static String DOT_SEPARATOR = ".";
    private String bizId = DEFAULT_BIZ_ID;
    private String useCase = DEFAULT_USE_CASE;
    private String scenario = DEFAULT_SCENARIO;
    public String getUniqueIdentity() {
        StringBuilder sb = new StringBuilder();
        if (StringUtils.isNotBlank(bizId)) {
            sb.append(bizId);
        }
        if (StringUtils.isNotBlank(useCase)) {
            sb.append(DOT_SEPARATOR).append(useCase);
        }
        if (StringUtils.isNotBlank(scenario)) {
            sb.append(DOT_SEPARATOR).append(scenario);
        }
        return sb.toString();
    }
    public static BizScenario valueOf(String bizId, String useCase, String scenario) {
        Preconditions.checkArgument(bizId != null, "bizId can't be empty");
        BizScenario bizScenario = new BizScenario();
        bizScenario.bizId = bizId;
        bizScenario.useCase = useCase;
        bizScenario.scenario = scenario;
        return bizScenario;
    }
    public static BizScenario valueOf(String bizId) {
        return BizScenario.valueOf(bizId, DEFAULT_USE_CASE, DEFAULT_SCENARIO);
    }
    public static BizScenario valueOf(String useCase, String scenario) {
        return BizScenario.valueOf(DEFAULT_BIZ_ID, useCase, scenario);
    }
    public static BizScenario newDefault() {
        return BizScenario.valueOf(DEFAULT_BIZ_ID, DEFAULT_USE_CASE, DEFAULT_SCENARIO);
    }
}
