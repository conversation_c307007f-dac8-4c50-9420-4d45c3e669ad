package com.das.province.common.builder;

import com.das.province.common.exception.CommonException;
import com.das.province.common.http.SslConfig;
import org.apache.http.HttpEntityEnclosingRequest;
import org.apache.http.HttpHost;
import org.apache.http.HttpRequest;
import org.apache.http.NoHttpResponseException;
import org.apache.http.client.HttpRequestRetryHandler;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.protocol.HttpClientContext;
import org.apache.http.config.Registry;
import org.apache.http.config.RegistryBuilder;
import org.apache.http.conn.ConnectTimeoutException;
import org.apache.http.conn.socket.ConnectionSocketFactory;
import org.apache.http.conn.socket.PlainConnectionSocketFactory;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.impl.conn.DefaultProxyRoutePlanner;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.apache.http.protocol.HttpContext;

import javax.net.ssl.SSLException;
import javax.net.ssl.SSLHandshakeException;
import java.io.IOException;
import java.io.InterruptedIOException;
import java.net.UnknownHostException;

public class HttpClientCustomizeBuilder extends HttpClientBuilder {
    public boolean isSetPool = false;
    private SslConfig.SSLProtocolVersion sslpv = SslConfig.SSLProtocolVersion.SSLv3;
    private SslConfig ssls = SslConfig.getInstance();
    private HttpClientCustomizeBuilder() {
    }
    public static HttpClientCustomizeBuilder custom() {
        return new HttpClientCustomizeBuilder();
    }
    @Deprecated
    public HttpClientCustomizeBuilder timeout(int timeout) {
        return timeout(timeout, true);
    }
    @Deprecated
    public HttpClientCustomizeBuilder timeout(int timeout, boolean redirectEnable) {
        RequestConfig config = RequestConfig.custom()
                .setConnectionRequestTimeout(timeout)
                .setConnectTimeout(timeout)
                .setSocketTimeout(timeout)
                .setRedirectsEnabled(redirectEnable)
                .build();
        return (HttpClientCustomizeBuilder) this.setDefaultRequestConfig(config);
    }
    public HttpClientCustomizeBuilder ssl() throws CommonException {
        return (HttpClientCustomizeBuilder) this.setSSLSocketFactory(ssls.getSSLCONNSF(sslpv));
    }
    public HttpClientCustomizeBuilder ssl(String keyStorePath) throws CommonException {
        return ssl(keyStorePath, "nopassword");
    }
    public HttpClientCustomizeBuilder ssl(String keyStorePath, String keyStorepass) throws CommonException {
        this.ssls = SslConfig.custom().customSSL(keyStorePath, keyStorepass);
        return ssl();
    }
    public HttpClientCustomizeBuilder pool(int maxTotal, int defaultMaxPerRoute) throws CommonException {
        Registry<ConnectionSocketFactory> socketFactoryRegistry = RegistryBuilder
                .<ConnectionSocketFactory>create()
                .register("http", PlainConnectionSocketFactory.INSTANCE)
                .register("https", ssls.getSSLCONNSF(sslpv)).build();
        PoolingHttpClientConnectionManager connManager = new PoolingHttpClientConnectionManager(socketFactoryRegistry);
        connManager.setMaxTotal(maxTotal);
        connManager.setDefaultMaxPerRoute(defaultMaxPerRoute);
        isSetPool = true;
        return (HttpClientCustomizeBuilder) this.setConnectionManager(connManager);
    }
    public HttpClientCustomizeBuilder proxy(String hostOrIP, int port) {
        HttpHost proxy = new HttpHost(hostOrIP, port, "http");
        DefaultProxyRoutePlanner routePlanner = new DefaultProxyRoutePlanner(proxy);
        return (HttpClientCustomizeBuilder) this.setRoutePlanner(routePlanner);
    }
    public HttpClientCustomizeBuilder retry(final int tryTimes) {
        return retry(tryTimes, false);
    }
    public HttpClientCustomizeBuilder retry(final int tryTimes, final boolean retryWhenInterruptedIO) {
        HttpRequestRetryHandler httpRequestRetryHandler = new HttpRequestRetryHandler() {
            public boolean retryRequest(IOException exception, int executionCount, HttpContext context) {
                if (executionCount >= tryTimes) {
                    return false;
                }
                if (exception instanceof NoHttpResponseException) {
                    return true;
                }
                if (exception instanceof SSLHandshakeException) {
                    return false;
                }
                if (exception instanceof InterruptedIOException) {
                    return retryWhenInterruptedIO;
                }
                if (exception instanceof UnknownHostException) {
                    return true;
                }
                if (exception instanceof ConnectTimeoutException) {
                    return false;
                }
                if (exception instanceof SSLException) {
                    return false;
                }

                HttpClientContext clientContext = HttpClientContext.adapt(context);
                HttpRequest request = clientContext.getRequest();
                if (!(request instanceof HttpEntityEnclosingRequest)) {
                    return true;
                }
                return false;
            }
        };
        this.setRetryHandler(httpRequestRetryHandler);
        return this;
    }
    public HttpClientCustomizeBuilder sslpv(String sslpv) {
        return sslpv(SslConfig.SSLProtocolVersion.find(sslpv));
    }
    public HttpClientCustomizeBuilder sslpv(SslConfig.SSLProtocolVersion sslpv) {
        this.sslpv = sslpv;
        return this;
    }
}
