package com.das.province.common.http;

import org.apache.http.Header;
import org.apache.http.message.BasicHeader;

import java.util.HashMap;
import java.util.Map;

public class HttpHeader {
    private HttpHeader() {
    }
    public static HttpHeader custom() {
        return new HttpHeader();
    }
    private Map<String, Header> headerMaps = new HashMap<String, Header>();
    public HttpHeader other(String key, String value) {
        headerMaps.put(key, new BasicHeader(key, value));
        return this;
    }
    public HttpHeader date(String date) {
        headerMaps.put(HttpReqHead.DATE,
                new BasicHeader(HttpReqHead.DATE, date));
        return this;
    }
    public HttpHeader from(String from) {
        headerMaps.put(HttpReqHead.FROM,
                new BasicHeader(HttpReqHead.FROM, from));
        return this;
    }
    public String date() {
        return get(HttpReqHead.DATE);
    }
    public String from() {
        return get(HttpReqHead.FROM);
    }
    private String get(String headName) {
        if (headerMaps.containsKey(headName)) {
            return headerMaps.get(headName).getValue();
        }
        return null;
    }
    public Header[] build() {
        Header[] headers = new Header[headerMaps.size()];
        int i = 0;
        for (Header header : headerMaps.values()) {
            headers[i] = header;
            i++;
        }
        headerMaps.clear();
        headerMaps = null;
        return headers;
    }
    private static class HttpReqHead {
        public static final String DATE = "Date";
        public static final String FROM = "From";
    }
}
