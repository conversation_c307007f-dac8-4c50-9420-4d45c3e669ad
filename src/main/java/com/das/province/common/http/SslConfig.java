package com.das.province.common.http;

import com.das.province.common.exception.CommonErrorCodeEnum;
import com.das.province.common.exception.CommonException;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.conn.ssl.TrustSelfSignedStrategy;
import org.apache.http.nio.conn.ssl.SSLIOSessionStrategy;
import org.apache.http.ssl.SSLContexts;

import javax.net.ssl.*;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.security.KeyManagementException;
import java.security.KeyStore;
import java.security.NoSuchAlgorithmException;

public class SslConfig {
    private static final SSLHandler simpleVerifier = new SSLHandler();
	private static SSLSocketFactory sslFactory ;
	private static SSLConnectionSocketFactory sslConnFactory ;
	private static SSLIOSessionStrategy sslIOSessionStrategy ;
	private static SslConfig sslUtil = new SslConfig();
	private SSLContext sc;
	
	public static SslConfig getInstance(){
		return sslUtil;
	}
	public static SslConfig custom(){
		return new SslConfig();
	}
	private static class SSLHandler implements  X509TrustManager, HostnameVerifier{
		@Override
		public java.security.cert.X509Certificate[] getAcceptedIssuers() {
			return new java.security.cert.X509Certificate[]{};
		}
		
		@Override
		public void checkServerTrusted(java.security.cert.X509Certificate[] chain,
				String authType) {
		}
		
		@Override
		public void checkClientTrusted(java.security.cert.X509Certificate[] chain,
				String authType) {
		}

		@Override
		public boolean verify(String paramString, SSLSession paramSSLSession) {
			return true;
		}
	};

	public static HostnameVerifier getVerifier() {
        return simpleVerifier;
    }
    
    public synchronized SSLSocketFactory getSSLSF(SSLProtocolVersion sslpv) throws CommonException {
        if (sslFactory != null) {
			return sslFactory;
		}
		try {
			SSLContext sc = getSSLContext(sslpv);
			sc.init(null, new TrustManager[] { simpleVerifier }, null);
			sslFactory = sc.getSocketFactory();
		} catch (KeyManagementException e) {
			throw new CommonException(CommonErrorCodeEnum.UNKNOWN_ERROR, e.getMessage());
		}
        return sslFactory;
    }
    
    public synchronized SSLConnectionSocketFactory getSSLCONNSF(SSLProtocolVersion sslpv) throws CommonException {
    	if (sslConnFactory != null) {
			return sslConnFactory;
		}
    	try {
	    	SSLContext sc = getSSLContext(sslpv);
	    	sc.init(null, new TrustManager[] { simpleVerifier }, new java.security.SecureRandom());
	    	sslConnFactory = new SSLConnectionSocketFactory(sc, simpleVerifier);
		} catch (KeyManagementException e) {
			throw new CommonException(CommonErrorCodeEnum.UNKNOWN_ERROR, e.getMessage());
		}
    	return sslConnFactory;
    }
    
    public synchronized SSLIOSessionStrategy getSSLIOSS(SSLProtocolVersion sslpv) throws CommonException {
    	if (sslIOSessionStrategy != null) {
			return sslIOSessionStrategy;
		}
		try {
			SSLContext sc = getSSLContext(sslpv);
	    	sc.init(null, new TrustManager[] { simpleVerifier }, new java.security.SecureRandom());
			sslIOSessionStrategy = new SSLIOSessionStrategy(sc, simpleVerifier);
		} catch (KeyManagementException e) {
			throw new CommonException(CommonErrorCodeEnum.UNKNOWN_ERROR, e.getMessage());
		}
    	return sslIOSessionStrategy;
    }
    
    public SslConfig customSSL(String keyStorePath, String keyStorepass) throws CommonException{
    	FileInputStream instream =null;
    	KeyStore trustStore = null; 
		try {
			trustStore = KeyStore.getInstance(KeyStore.getDefaultType());
			instream = new FileInputStream(new File(keyStorePath));
			trustStore.load(instream, keyStorepass.toCharArray());
			sc= SSLContexts.custom().loadTrustMaterial(trustStore, new TrustSelfSignedStrategy()) .build();
		} catch (Exception e) {
			throw new CommonException(CommonErrorCodeEnum.UNKNOWN_ERROR, e.getMessage());
		} finally{
			try {
				assert instream != null;
				instream.close();
			} catch (IOException e) {}
		}
		return this;
    }
    
    public SSLContext getSSLContext(SSLProtocolVersion sslpv) throws CommonException{
    	try {
    		if(sc==null){
    			sc = SSLContext.getInstance(sslpv.getName());
    		}
    		return sc;
    	} catch (NoSuchAlgorithmException e) {
			throw new CommonException(CommonErrorCodeEnum.UNKNOWN_ERROR, e.getMessage());
    	}
    }

    public static enum SSLProtocolVersion{
    	SSL("SSL"),
    	SSLv3("SSLv3"),
    	TLSv1("TLSv1"),
    	TLSv1_1("TLSv1.1"),
    	TLSv1_2("TLSv1.2"),
    	;
    	private String name;
    	private SSLProtocolVersion(String name){
    		this.name = name;
    	}
    	public String getName(){
    		return this.name;
    	}
    	public static SSLProtocolVersion find(String name){
    		for (SSLProtocolVersion pv : SSLProtocolVersion.values()) {
				if(pv.getName().toUpperCase().equals(name.toUpperCase())){
					return pv;
				}
			}
    		throw new RuntimeException("未支持当前ssl版本号："+name);
    	}
    	
    }
}