package com.das.province.common.http;

import org.apache.http.Header;
import org.apache.http.client.HttpClient;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.protocol.HttpContext;

import java.io.OutputStream;
import java.nio.charset.Charset;
import java.util.HashMap;
import java.util.Map;

public class HttpConfig {
    private HttpConfig() {
    }
    public static HttpConfig custom() {
        return new HttpConfig();
    }
    private HttpClient client;
    private Header[] headers;
    private boolean isReturnRespHeaders;
    private HttpMethods method = HttpMethods.GET;
    private HttpContext context;
    private String json;
    private String encoding = Charset.defaultCharset().displayName();
    private String inEncoding;
    private String outEncoding;
    private RequestConfig requestConfig;
    private static final ThreadLocal<OutputStream> OUTPUT_STREAM = new ThreadLocal<OutputStream>();
    private static final ThreadLocal<String> URLS = new ThreadLocal<String>();
    private static final ThreadLocal<Map<String, Object>> MAPS = new ThreadLocal<Map<String, Object>>();
    public HttpConfig client(HttpClient client) {
        this.client = client;
        return this;
    }
    public HttpConfig url(String url) {
		URLS.set(url);
        return this;
    }
    public HttpConfig headers(Header[] headers) {
        this.headers = headers;
        return this;
    }
    public HttpConfig method(HttpMethods method) {
        this.method = method;
        return this;
    }
    public HttpConfig context(HttpContext context) {
        this.context = context;
        return this;
    }
    public HttpConfig map(Map<String, Object> map) {
        Map<String, Object> m = MAPS.get();
        if (m == null || map == null) {
            m = map;
        } else {
            m.putAll(map);
        }
		MAPS.set(m);
        return this;
    }
    public HttpConfig json(String json) {
        this.json = json;
        Map<String, Object> map = new HashMap<String, Object>();
        map.put(Utils.ENTITY_JSON, json);
		MAPS.set(map);
        return this;
    }
    public HttpConfig encoding(String encoding) {
        //设置输入输出
        setInEncoding(encoding);
        setOutEncoding(encoding);
        this.encoding = encoding;
        return this;
    }
    public HttpConfig out(OutputStream out) {
        OUTPUT_STREAM.set(out);
        return this;
    }
    public HttpConfig timeout(int timeout) {
        return timeout(timeout, true);
    }
    public HttpConfig timeout(int timeout, boolean redirectEnable) {
        RequestConfig config = RequestConfig.custom()
                .setConnectionRequestTimeout(timeout)
                .setConnectTimeout(timeout)
                .setSocketTimeout(timeout)
                .setRedirectsEnabled(redirectEnable)
                .build();
        return timeout(config);
    }
    public HttpConfig timeout(RequestConfig requestConfig) {
        this.requestConfig = requestConfig;
        return this;
    }
    public HttpClient client() {
        return client;
    }
    public Header[] headers() {
        return headers;
    }

    public boolean isReturnRespHeaders() {
        return isReturnRespHeaders;
    }

    public String url() {
        return URLS.get();
    }
    public HttpMethods method() {
        return method;
    }
    public HttpContext context() {
        return context;
    }
    public Map<String, Object> map() {
        return MAPS.get();
    }
    public String json() {
        return json;
    }
    public String encoding() {
        return encoding;
    }
	public String getEncoding() {
		return encoding;
	}
	public void setEncoding(String encoding) {
		this.encoding = encoding;
	}
	public String getInEncoding() {
		return inEncoding;
	}
	public HttpConfig setInEncoding(String inEncoding) {
		this.inEncoding = inEncoding;
        return this;
	}
	public String getOutEncoding() {
		return outEncoding;
	}
	public HttpConfig setOutEncoding(String outEncoding) {
		this.outEncoding = outEncoding;
        return this;
	}
	public OutputStream out() {
        return OUTPUT_STREAM.get();
    }
    public RequestConfig requestConfig() {
        return requestConfig;
    }
}