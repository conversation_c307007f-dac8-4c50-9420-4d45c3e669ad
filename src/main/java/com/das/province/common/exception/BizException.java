package com.das.province.common.exception;

public class BizException extends RuntimeException {
    private Integer code;
    private String detailMsg;
    public BizException(Integer code, String message) {
        super(message);
        this.code = code;
    }
    public BizException(Integer code, String message, String detailMs) {
        super(message);
        this.code = code;
        this.detailMsg = detailMs;
    }
    public BizException(Integer code, String message, Throwable e) {
        super(message, e);
        this.code = code;
        this.detailMsg = message;
    }
    public Integer getCode() {
        return code;
    }
    public String getDetailMsg() {
        return detailMsg;
    }
}
