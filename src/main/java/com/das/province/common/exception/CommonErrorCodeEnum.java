package com.das.province.common.exception;

public enum CommonErrorCodeEnum {
    SUCCESS(200, "请求成功"),
    UNKNOWN_ERROR(500, "未知错误"),
    USER_NOT_EXIST_CODE(10001, "用户不存在, 请重新登录"),
    EXTENSION_EXECUTOR_ERROR(10000001, "扩展点错误"),
    RESOURCE_NOT_FOUND(10000002, "资源未找到"),
    SERIALIZATION_ERROR(10000003, "序列化错误"),
    UPLOAD_EXCEED_LIMIT(10000005, "上传超过限制"),
    FILE_READ_FAIL(10000006, "文件读取失败"),
    VALIDATE_ERROR(10000007, "校验错误"),
    PARAM_ERROR(10000008, "参数错误"),
    OBJECT_NOT_EXIST(10000009, "对象不存在"),
    ENUM_NOT_EXIST(100000010, "枚举不存在"),
    UPLOAD_FILE_FAIL(100000011, "文件上传失败"),
    DATA_NOT_EXIST(100000012, "数据为空"),
    DEL_FILE_FAIL(100000013, "文件删除失败"),
    OBJECT_EXIST(100000014, "对象已经存在"),
    ;
    private int code;
    private String message;
    CommonErrorCodeEnum(int code, String message) {
        this.code = code;
        this.message = message;
    }
    public int getCode() {
        return code;
    }
    public String getMessage() {
        return message;
    }
}
