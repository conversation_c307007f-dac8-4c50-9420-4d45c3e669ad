package com.das.province.common.exception;

public class CommonException extends BizException {
    public CommonException(int code, String message) {
        super(code, message);
    }
    public CommonException(int code, String message, Throwable e) {
        super(code, message, e);
    }
    public CommonException(int code, String message, String detailMsg) {
        super(code, message, detailMsg);
    }
    public CommonException(CommonErrorCodeEnum commonErrorCodeEnum) {
        super(commonErrorCodeEnum.getCode(), commonErrorCodeEnum.getMessage());
    }
    public CommonException(CommonErrorCodeEnum commonErrorCodeEnum, String message) {
        super(commonErrorCodeEnum.getCode(), message);
    }
    public CommonException(CommonErrorCodeEnum commonErrorCodeEnum, Throwable e) {
        super(commonErrorCodeEnum.getCode(), commonErrorCodeEnum.getMessage(), e);
    }
}
