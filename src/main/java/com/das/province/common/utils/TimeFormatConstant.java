package com.das.province.common.utils;

/**
 * 时间格式化常量池
 * <AUTHOR>
 * 2022-3-20
 */
public class TimeFormatConstant {
    public static final String DATE_SECOND_WITH = "yyyy-MM-dd HH:mm:ss";

    public static final String DATE_MINUTE_WITH = "yyyy-MM-dd HH:mm";

    public static final String DATE_HOUR_WITH = "yyyy-MM-dd HH";

    public static final String DATE_DAY_WITH = "yyyy-MM-dd";

    public static final String DATE_MONTH_WITH = "yyyy-MM";

    public static final String DATE_YEAR_WITH = "yyyy";

    public static final String DATE_SECOND = "yyyyMMddHHmmss";

    public static final String DATE_SECOND2 = "yyMMddHHmmss";

    public static final String DATE_MINUTE = "yyyyMMddHHmm";

    public static final String DATE_HOUR = "yyyyMMddHH";

    public static final String DATE_DAY = "yyyyMMdd";

    public static final String DATE_MONTH = "yyyyMM";

    public static final String DATE_HOUR_MINUTE = "HH:mm";

    public static final String DATE_DAY_WITH_ELSE = "yyyy/MM/dd/";
}
