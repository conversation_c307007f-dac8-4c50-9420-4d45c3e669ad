package com.das.province.common.utils;

import lombok.extern.slf4j.Slf4j;

import java.io.File;

@Slf4j
public class FileUtil {
    public static void deleteFilePath(String loadPath) {
        File file = new File(loadPath);
        if (!file.exists()) {
            log.error("文件删除失败,请检查文件路径是否正确");
            return;
        }
        File[] files = file.listFiles();
        for (File f : files) {
            if (f.isDirectory()) {
                deleteFilePath(f.getPath());
            } else {
                f.delete();
            }
        }
        file.delete();
    }
}
