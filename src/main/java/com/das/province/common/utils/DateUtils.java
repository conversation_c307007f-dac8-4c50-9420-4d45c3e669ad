package com.das.province.common.utils;

import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.StringUtils;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.temporal.Temporal;
import java.time.temporal.TemporalAdjusters;
import java.util.*;

import static java.time.format.DateTimeFormatter.ISO_LOCAL_DATE;

public class DateUtils {

    private static Logger log = LoggerFactory.getLogger(DateUtils.class);

    private static String FORMAT_PATTERN = "yyyy-MM-dd HH:mm:ss";

    public static String formatDateYYY() {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy");
        return simpleDateFormat.format(new Date());
    }

    public static String formatDateYYYMM() {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM");
        return simpleDateFormat.format(new Date());
    }

    public static String formatDateYYYMMDD() {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
        return simpleDateFormat.format(new Date());
    }

    public static String formatDate(Date d, String formatStr) {
        if (null == d) {
            return null;
        }
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat(formatStr);
        return simpleDateFormat.format(d);
    }

    public static Date dateAdd(Date d, int offsetDay) {
        return org.apache.commons.lang3.time.DateUtils.addDays(d, offsetDay);
    }

    public static Date monthAdd(Date d, int offsetMonth) {
        return org.apache.commons.lang3.time.DateUtils.addMonths(d, offsetMonth);
    }

    public static boolean isBetween(Date from, Date to) {
        Date now = Calendar.getInstance().getTime();
        return !(now.before(from) || now.after(to));
    }

    public static boolean isBetweenContainEndsBy2YYYMMDD(Date from, Date to) {
        String fromStr = DateUtils.formatDateYYYMMDD(from);
        Date fromDate = DateUtils.parseDateYYYMMDD(fromStr);
        String toStr = DateUtils.formatDateYYYMMDD(to);
        Date toDate = DateUtils.parseDateYYYMMDD(toStr);
        String nowStr = DateUtils.formatDateYYYMMDD(new Date());
        Date nowDate = DateUtils.parseDateYYYMMDD(nowStr);

        return nowDate.compareTo(fromDate) >= 0 && nowDate.compareTo(toDate) <= 0;
    }

    public static boolean isNowDateBeforeToDate(Date from, Date to) {
        LocalDate fromLocalDate = DateUtils.dateToLocalDate(from);
        LocalDate toLocalDate = DateUtils.dateToLocalDate(to);
        return isNowDateBeforeToDate(fromLocalDate, toLocalDate);
    }

    public static boolean isNowDateBeforeToDate(LocalDate fromLocalDate, LocalDate toLocalDate) {
        if (fromLocalDate == null || toLocalDate == null) {
            return false;
        }
        if (fromLocalDate.isAfter(toLocalDate)) {
            return false;
        }
        LocalDate nowLocalDate = LocalDate.now();
        return nowLocalDate.isBefore(toLocalDate) || nowLocalDate.equals(toLocalDate);
    }

    public static boolean isTimeBetween(Date from, Date to) {
        Date now = Calendar.getInstance().getTime();

        now = setYearMonthDaySecondMillisecondToZero(now);
        from = setYearMonthDaySecondMillisecondToZero(from);
        to = setYearMonthDaySecondMillisecondToZero(to);
        return !(now.before(from) || now.after(to));
    }

    public static boolean isTimeBetween(LocalTime from, LocalTime to) {
        LocalTime now = LocalTime.now();
        return isTimeBetween(from, to, now);
    }

    public static boolean isTimeBetween(LocalTime from, LocalTime to, LocalTime spec) {
        return !(spec.isBefore(from) || spec.isAfter(to));
    }

    public static Date parseDate(String dateStr, String formatStr) {
        Date date = null;
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat(formatStr);
        try {
            date = simpleDateFormat.parse(dateStr);
        } catch (ParseException e) {
            log.warn("日期格式解析错误,日期:{}", dateStr, e);
        }
        return date;
    }

    public static String formatDateYYYMMDD(Date date) {
        if (date == null) {
            date = new Date();
        }
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
        return simpleDateFormat.format(date);
    }

    public static String formatDateYYYMMDDHHmmss(Date date) {
        if (date == null) {
            date = new Date();
        }
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        return simpleDateFormat.format(date);
    }


    public static String formatDateYYYMMDDHHmm(Date date) {
        if (date == null) {
            date = new Date();
        }
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm");
        return simpleDateFormat.format(date);
    }


    public static String formatDateYYYYMMDDHHmmss(Date date) {
        if (date == null) {
            date = new Date();
        }
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyyMMdd-HHmmss");
        return simpleDateFormat.format(date);
    }

    public static String formatDateHHmm(Date date) {
        if (date == null) {
            date = new Date();
        }
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("HH:mm");
        return simpleDateFormat.format(date);
    }

    public static Date parseDateYYYMMDD(String dateStr) {
        Date date = new Date();
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
        try {
            date = simpleDateFormat.parse(dateStr);
        } catch (ParseException e) {
            log.warn("日期格式解析错误,日期:{}", dateStr, e);
        }
        return date;
    }

    public static Date parseDateHHmm(String dateStr) {
        Date date = new Date();
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("HH:mm");
        try {
            date = simpleDateFormat.parse(dateStr);
        } catch (ParseException e) {
            log.warn("日期格式解析错误,日期:{}", dateStr, e);
        }
        return date;
    }

    public static Date parseDateYYYMMDDHHmmss(String dateStr) {
        Date date = new Date();
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        try {
            date = simpleDateFormat.parse(dateStr);
        } catch (ParseException e) {
            log.error("日期格式解析错误,日期:{}", dateStr, e);
        }
        return date;
    }

    public static Date parseDateYYYMMDDHHmmss2(String dateStr) {
        Date date = null;
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        try {
            date = simpleDateFormat.parse(dateStr);
        } catch (ParseException e) {
            log.error("日期格式解析错误,日期:{}", dateStr, e);
        }
        return date;
    }

    public static String formatDateMMDD(Date date) {
        if (date == null) {
            date = new Date();
        }
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("MM月dd日");
        return simpleDateFormat.format(date);
    }

    public static String formatStringMMDD(String dateStr) {
        return formatDateMMDD(parseDateYYYMMDD(dateStr));
    }


    public static String getThisMonthStr() {
        return DateFormatUtils.format(new Date(), "yyyy-MM");
    }

    public static String getMonthStrByDate(Date date) {
        return DateFormatUtils.format(date, "yyyy-MM");
    }

    public static String getMonthStrByDate(LocalDate localDate) {
        return localDate.format(DateTimeFormatter.ofPattern("yyyy-MM"));
    }


    /**
     * 时间格式字符串转换为时间戳
     *
     * @param date_str
     * @param format
     * @return
     */
    public static Long date2TimeStamp(String date_str, String format) {
        try {
            SimpleDateFormat sdf = new SimpleDateFormat(format);
            return sdf.parse(date_str).getTime();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return 0L;
    }

    /**
     * 判断一个时间是否在一个时间段内
     *
     * @param timeStart
     * @param timeEnd
     * @param timeStr
     * @return
     */
    public static boolean judgeTimeSlot(LocalTime timeStart, LocalTime timeEnd, String timeStr) {
        LocalTime time = LocalTime.parse(timeStr);
        return (timeStart.isBefore(time) || timeStart.equals(time)) && (timeEnd.isAfter(time) || timeEnd.equals(time));
    }

    /**
     * 获取日期对象
     *
     * @param dayOffset 相对于今天偏移的天数
     * @return
     */
    public static Calendar getDateOffset(int dayOffset) {
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DAY_OF_MONTH, dayOffset);
        return calendar;
    }

    /**
     * 获取日期对象
     *
     * @param minuteOffset 相对于今天偏移的分钟数
     * @return
     */
    public static Calendar getMinuteOffset(int minuteOffset) {
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.MINUTE, minuteOffset);
        return calendar;
    }

    /**
     * 获取日期对象
     *
     * @param minuteOffset 相对于偏移的分钟数
     * @return
     */
    public static Calendar getMinuteOffset(Date date, int minuteOffset) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.MINUTE, minuteOffset);
        return calendar;
    }

    /**
     * 获取日期对象
     *
     * @param dayOffset 相对于date偏移的天数
     * @return
     */
    public static Calendar getDateOffset(Date date, int dayOffset) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.DAY_OF_MONTH, dayOffset);
        return calendar;
    }


    /**
     * 获取日期对象
     *
     * @param date
     * @param monthOffset 相对于date偏移的月份
     * @return
     */
    public static Calendar getMonthOffset(Date date, int monthOffset) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.MONTH, monthOffset);
        return calendar;
    }

    /**
     * 获取日历对象，并且将日历对象的小时、分钟、秒钟、毫秒置为0
     *
     * @param dayOffset 相对于今天偏移的天数
     * @return
     */
    public static Calendar getCalendarWithHourMinuteSecondMillisecondToZero(int dayOffset) {
        Calendar calendar = Calendar.getInstance();
        setHourMinuteSecondMillisecondToZero(calendar);
        calendar.add(Calendar.DAY_OF_MONTH, dayOffset);
        return calendar;
    }

    /**
     * 将日历对象的小时、分钟、秒钟、毫秒置为0
     */
    public static void setHourMinuteSecondMillisecondToZero(Calendar calendar) {
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
    }

    /**
     * 将日期对象的小时、分钟、秒钟、毫秒置为0
     */
    public static Date setHourMinuteSecondMillisecondToZero(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        date.setTime(calendar.getTimeInMillis());
        return calendar.getTime();
    }

    /**
     * 将日期对象的年、月、日、秒钟、毫秒置为0
     */
    public static Date setYearMonthDaySecondMillisecondToZero(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        setYearMonthDaySecondMillisecondToZero(calendar);
        date.setTime(calendar.getTimeInMillis());
        return calendar.getTime();
    }


    /**
     * 将日历对象的年、月、日、秒钟、毫秒置为0
     */
    public static void setYearMonthDaySecondMillisecondToZero(Calendar calendar) {
        calendar.set(Calendar.YEAR, 1970);
        calendar.set(Calendar.MONTH, 0);
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
    }

    /**
     * Date转换成LocalDateTime
     *
     * @param date date
     * @return LocalDateTime
     */
    public static LocalDateTime dateToLocalDateTime(Date date) {
        if (date == null) {
            return null;
        }
        Instant instant = date.toInstant();
        ZoneId zone = ZoneId.systemDefault();
        return LocalDateTime.ofInstant(instant, zone);
    }

    /**
     * Date转换成LocalDate
     *
     * @param date date
     * @return LocalDate
     */
    public static LocalDate dateToLocalDate(Date date) {
        if (date == null) {
            return null;
        }
        Instant instant = date.toInstant();
        ZoneId zone = ZoneId.systemDefault();
        LocalDateTime localDateTime = LocalDateTime.ofInstant(instant, zone);
        return localDateTime.toLocalDate();
    }

    /**
     * Date转换成LocalTime
     *
     * @param date date
     * @return LocalTime
     */
    public static LocalTime dateToLocalTime(Date date) {
        if (date == null) {
            return null;
        }
        Instant instant = date.toInstant();
        ZoneId zone = ZoneId.systemDefault();
        LocalDateTime localDateTime = LocalDateTime.ofInstant(instant, zone);
        return localDateTime.toLocalTime();
    }

    /**
     * localDateTime转化成Date
     *
     * @param localDateTime localDateTime
     * @return Date
     */
    public static Date localDateTimeToUdate(LocalDateTime localDateTime) {
        if (localDateTime == null) {
            return null;
        }
        ZoneId zone = ZoneId.systemDefault();
        Instant instant = localDateTime.atZone(zone).toInstant();
        return Date.from(instant);
    }

    /**
     * localDate转化成Date
     *
     * @param localDate localDate
     * @return Date
     */
    public static Date localDateToUdate(LocalDate localDate) {
        if (localDate == null) {
            return null;
        }
        ZoneId zone = ZoneId.systemDefault();
        Instant instant = localDate.atStartOfDay().atZone(zone).toInstant();
        return Date.from(instant);
    }

    /**
     * localTime转化成Date
     *
     * @param localTime localTime
     * @return Date
     */
    public static Date localTimeToUdate(LocalTime localTime) {
        if (localTime == null) {
            return null;
        }
        LocalDate localDate = LocalDate.now();
        LocalDateTime localDateTime = LocalDateTime.of(localDate, localTime);
        ZoneId zone = ZoneId.systemDefault();
        Instant instant = localDateTime.atZone(zone).toInstant();
        return Date.from(instant);
    }

    /**
     * 取距离当前时间最近的整点时间或半点时间
     *
     * @param localTime 当前时间
     * @return
     */
    public static LocalTime ceilHour(LocalTime localTime) {
        int minute = localTime.getMinute();
        if (minute < 30) {
            //不足30分钟，按30分钟算
            localTime = localTime.withMinute(30).withSecond(0).withNano(0);
        } else if (minute > 30 && minute < 60) {
            //大于30分钟小于60分钟,算一个小时
            localTime = localTime.withMinute(0).withSecond(0).withNano(0);
            localTime = localTime.plusHours(1);
        }
        return localTime;
    }

    /**
     * 日期格式化
     *
     * @param localDate 日期
     * @param pattern   日期格式
     * @return
     */
    public static String formatLocalDate(LocalDate localDate, String pattern) {
        DateTimeFormatter formater = DateTimeFormatter.ofPattern(pattern);
        return localDate.format(formater);
    }

    public static String formatLocalDate(LocalDate localDate) {
        DateTimeFormatter formater = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        return localDate.format(formater);
    }

    public static String formatLocalDateTime(LocalDateTime localDateTime) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(FORMAT_PATTERN);
        return localDateTime.format(formatter);
    }

    /**
     * 日期格式化
     *
     * @param localDateTime 日期
     * @param pattern       日期格式
     * @return
     */
    public static String formatLocalDateTime(LocalDateTime localDateTime, String pattern) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(pattern);
        return localDateTime.format(formatter);
    }

    public static long getDaysBetweenTwoDate(Date begin, Date end) {
        begin = setHourMinuteSecondMillisecondToZero(begin);
        end = setHourMinuteSecondMillisecondToZero(end);
        return ((end.getTime() - begin.getTime()) / (24 * 3600 * 1000)) + 1;
    }

    public static boolean isBeforeOrEqualLocalDate(LocalDate leftDate, LocalDate afterDate) {
        return leftDate.isBefore(afterDate) || leftDate.isEqual(afterDate);
    }

    public static boolean isAfterOrEqualLocalDate(LocalDate leftDate, LocalDate afterDate) {
        return leftDate.isBefore(afterDate) || leftDate.isEqual(afterDate);
    }

    public static boolean isBeforeOrEqual(Date leftDate, Date afterDate) {
        return leftDate.before(afterDate) || leftDate.equals(afterDate);
    }

    public static boolean isAfterOrEqual(Date leftDate, Date afterDate) {
        return leftDate.after(afterDate) || leftDate.equals(afterDate);
    }

    /**
     * 某天最晚
     */
    public static Date getTimesDayNight(Date date) {
        if (null == date) {
            return null;
        }
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        cal.set(Calendar.HOUR_OF_DAY, 23);
        cal.set(Calendar.MINUTE, 59);
        cal.set(Calendar.SECOND, 59);
        return cal.getTime();
    }

    /**
     * 获取今日日零点
     *
     * @return
     */
    public static Date getTimesDayMorning() {
        Date time = new Date();
        Calendar calendar2 = Calendar.getInstance();
        calendar2.setTime(time);
        calendar2.set(calendar2.get(Calendar.YEAR), calendar2.get(Calendar.MONTH), calendar2.get(Calendar.DAY_OF_MONTH),
                0, 0, 0);
        calendar2.set(Calendar.MILLISECOND,0);
        Date date = calendar2.getTime();
        return date;
    }

    /**
     * 获取今日最晚
     *
     * @return
     */
    public static Date getTimesDayNight() {
        Date time = new Date();
        Calendar calendar2 = Calendar.getInstance();
        calendar2.setTime(time);
        calendar2.set(calendar2.get(Calendar.YEAR), calendar2.get(Calendar.MONTH), calendar2.get(Calendar.DAY_OF_MONTH),
                23, 59, 59);
        calendar2.set(Calendar.MILLISECOND,0);
        Date date = calendar2.getTime();
        return date;
    }

    /**
     * 获得本周一0点时间
     */
    public static Date getTimesWeekMorning() {
        Calendar cal = Calendar.getInstance();
        cal.setFirstDayOfWeek(Calendar.MONDAY);
        cal.set(Calendar.DAY_OF_WEEK, Calendar.MONDAY);
        cal.set(Calendar.HOUR_OF_DAY, cal.getActualMinimum(Calendar.HOUR_OF_DAY));
        cal.set(Calendar.MINUTE, cal.getActualMinimum(Calendar.MINUTE));
        cal.set(Calendar.SECOND, cal.getActualMinimum(Calendar.SECOND));
        cal.set(Calendar.MILLISECOND,0);
        return cal.getTime();
    }

    /**
     * 获得本周日24点时间
     */
    public static Date getTimesWeekNight() {
        Calendar cal = Calendar.getInstance();
        cal.setFirstDayOfWeek(Calendar.MONDAY);
        cal.set(Calendar.DAY_OF_WEEK, Calendar.SUNDAY);
        cal.set(Calendar.HOUR_OF_DAY, cal.getActualMaximum(Calendar.HOUR_OF_DAY));
        cal.set(Calendar.MINUTE, cal.getActualMaximum(Calendar.MINUTE));
        cal.set(Calendar.SECOND, cal.getActualMaximum(Calendar.SECOND));
        cal.set(Calendar.MILLISECOND,0);
        return cal.getTime();
    }

    /**
     * 获得本月第一天0点时间
     */
    public static Date getTimesMonthMorning() {
        Calendar cal = Calendar.getInstance();
        cal.set(Calendar.DAY_OF_MONTH, cal.getActualMinimum(Calendar.DAY_OF_MONTH));
        cal.set(Calendar.HOUR_OF_DAY, cal.getActualMinimum(Calendar.HOUR_OF_DAY));
        cal.set(Calendar.MINUTE, cal.getActualMinimum(Calendar.MINUTE));
        cal.set(Calendar.SECOND, cal.getActualMinimum(Calendar.SECOND));
        cal.set(Calendar.MILLISECOND,0);
        return cal.getTime();
    }

    /**
     * 获得本月最后一天24点时间
     */
    public static Date getTimesMonthNight() {
        Calendar cal = Calendar.getInstance();
        cal.set(Calendar.DAY_OF_MONTH, cal.getActualMaximum(Calendar.DAY_OF_MONTH));
        cal.set(Calendar.HOUR_OF_DAY, cal.getActualMaximum(Calendar.HOUR_OF_DAY));
        cal.set(Calendar.MINUTE, cal.getActualMaximum(Calendar.MINUTE));
        cal.set(Calendar.SECOND, cal.getActualMaximum(Calendar.SECOND));
        cal.set(Calendar.MILLISECOND,0);
        return cal.getTime();
    }

    /**
     * 获得本年第一天0点时间
     */
    public static Date getTimesYearMorning() {
        Calendar cal = Calendar.getInstance();
        cal.set(Calendar.DAY_OF_YEAR, cal.getActualMinimum(Calendar.DAY_OF_YEAR));
        //cal.set(Calendar.DAY_OF_MONTH, cal.getActualMinimum(Calendar.DAY_OF_MONTH));
        cal.set(Calendar.HOUR_OF_DAY, cal.getActualMinimum(Calendar.HOUR_OF_DAY));
        cal.set(Calendar.MINUTE, cal.getActualMinimum(Calendar.MINUTE));
        cal.set(Calendar.SECOND, cal.getActualMinimum(Calendar.SECOND));
        cal.set(Calendar.MILLISECOND,0);
        return cal.getTime();
    }

    /**
     * 获得本年最后一天24点时间
     */
    public static Date getTimesYearNight() {
        Calendar cal = Calendar.getInstance();
        cal.set(Calendar.DAY_OF_YEAR, cal.getActualMinimum(Calendar.DAY_OF_YEAR));
        cal.set(Calendar.DAY_OF_MONTH, cal.getActualMaximum(Calendar.DAY_OF_MONTH));
        cal.set(Calendar.HOUR_OF_DAY, cal.getActualMaximum(Calendar.HOUR_OF_DAY));
        cal.set(Calendar.MINUTE, cal.getActualMaximum(Calendar.MINUTE));
        cal.set(Calendar.SECOND, cal.getActualMaximum(Calendar.SECOND));
        cal.set(Calendar.MILLISECOND,0);
        return cal.getTime();
    }

    /**
     * 获取本季度第一天0点时间
     */
    public static Date getTimesSeasonMorning() {
        Calendar cal = Calendar.getInstance();
        int month = cal.get(Calendar.MONTH);
        switch (month) {
            case 0:
            case 1:
            case 2:
                cal.set(Calendar.MONTH, Calendar.JANUARY);
                break;
            case 3:
            case 4:
            case 5:
                cal.set(Calendar.MONTH, Calendar.APRIL);
                break;
            case 6:
            case 7:
            case 8:
                cal.set(Calendar.MONTH, Calendar.JULY);
                break;
            case 9:
            case 10:
            case 11:
            default:
                cal.set(Calendar.MONTH, Calendar.OCTOBER);
                break;
        }
        cal.set(Calendar.SECOND, cal.getActualMinimum(Calendar.SECOND));
        cal.set(Calendar.MINUTE, cal.getActualMinimum(Calendar.MINUTE));
        cal.set(Calendar.HOUR_OF_DAY, cal.getActualMinimum(Calendar.HOUR_OF_DAY));
        cal.set(Calendar.DAY_OF_MONTH, cal.getActualMinimum(Calendar.DAY_OF_MONTH));
        return cal.getTime();
    }

    public static LocalDate getQuarterStart() {
        LocalDate now = LocalDate.now();
        int month = now.getMonth().getValue();
        switch (month) {
            // 第一季度
            case 1:
            case 2:
            case 3:
                month = 1;
                break;
            // 第二季度
            case 4:
            case 5:
            case 6:
                month = 4;
                break;
            // 第三季度
            case 7:
            case 8:
            case 9:
                month = 7;
                break;
            // 第四季度
            case 10:
            case 11:
            case 12:
            default:
                month = 10;
                break;
        }
        return now.withMonth(month).with(TemporalAdjusters.firstDayOfMonth());
    }


    /**
     * 获得上月第一天0点时间
     *
     * @return
     */
    public static LocalDateTime getFrontMonthStart() {
        return LocalDateTime.now().minusMonths(1).with(TemporalAdjusters.firstDayOfMonth()).withHour(0).withMinute(0).withSecond(0);
    }

    /**
     * 获得上月月最后一天24点时间
     *
     * @return
     */
    public static LocalDateTime getFrontMonthEnd() {
        return LocalDateTime.now().minusMonths(1).with(TemporalAdjusters.lastDayOfMonth()).withHour(23).withMinute(59).withSecond(59);
    }


    /**
     * 秒转分钟和秒
     *
     * @param seconds
     * @return
     */
    public static String secondToMinuteSecond(int seconds) {
        if (seconds < 60) {
            return seconds + "秒";
        } else {
            return (seconds / 60) + "分" + (seconds % 60) + "秒";
        }
    }

    public static String getDuration(Date from, Date to) {
        LocalDate fromDate = dateToLocalDate(from);
        LocalDate toDate = dateToLocalDate(to);

        Period period = Period.between(fromDate, toDate);
        period = period.plusDays(1);

        String retStr = "";
        if (period.getYears() != 0) {
            retStr += period.getYears() + "年";
        }
        if (period.getMonths() != 0) {
            retStr += period.getMonths() + "月";
        }
        if (period.getDays() != 0) {
            retStr += period.getDays() + "天";
        }
        return retStr;
    }

    public static int getDays(LocalDate fromDate, LocalDate toDate) {
        return (int) (toDate.toEpochDay() - fromDate.toEpochDay());
    }

    public static int getDays(Date fromDate, Date toDate) {
        LocalDate fromLocalDate = DateUtils.dateToLocalDate(fromDate);
        LocalDate toLocalDate = DateUtils.dateToLocalDate(toDate);
        return getDays(fromLocalDate, toLocalDate);
    }

    /**
     * 转换为GMT格式时间
     *
     * @param date 原始时间
     * @return GMT格式时间
     */
    public static String toGMTString(Date date) {
        SimpleDateFormat df = new SimpleDateFormat("E, dd MMM yyyy HH:mm:ss z", Locale.UK);
        df.setTimeZone(new SimpleTimeZone(0, "GMT"));
        return df.format(date);
    }

    public static LocalDateTime parseNormalTimeStr(String time) {
        LocalDateTime localDateTime = LocalDateTime.parse(time, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        return localDateTime;
    }

    public static final String DATE_PATTERN = "yyyy-MM-dd";

    public static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern(DATE_PATTERN);

    public static LocalDate getCurrentDate() {
        YearMonth lastYearMonth = YearMonth.now().minusMonths(1);
        return LocalDate.of(lastYearMonth.getYear(), lastYearMonth.getMonth(), 1);
    }

    public static LocalDate parseDateString(String date) {
        // date默认是当月
        LocalDate currentDate;
        if (StringUtils.isEmpty(date)) {
            currentDate = getCurrentDate();
        } else {
            currentDate = LocalDate.parse(date, DATE_FORMATTER);
        }
        return currentDate;
    }

    public static LocalDateTime parseDateTimeString(String dateStr) {
        LocalDate localDate = LocalDate.parse(dateStr, ISO_LOCAL_DATE);
        return LocalDateTime.of(localDate, LocalTime.of(0, 0, 0));
    }

    public static LocalDate parseDateString(String date, String pattern) {
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern(pattern);
        return LocalDate.parse(date, dateTimeFormatter);
    }

    public static String concatCurrentYearMonth(LocalDateTime localDateTime) {
        return localDateTime.getYear() + "-" + (localDateTime.getMonthValue() < 10 ? "0" + localDateTime.getMonthValue() : localDateTime.getMonthValue());
    }

    /**
     * 获取下个月的年月字符串
     *
     * @param currentYearMonth 当前年月
     * @param monthNum         月数
     * @return 下个月的年月字符串
     */
    public static String strYearMonthAddMonthNumToStr(String currentYearMonth, Integer monthNum) {
        LocalDateTime now = DateUtils.parseNormalTimeStr(currentYearMonth + "-01 00:00:00");
        LocalDateTime nextConsumedDate = now.plusMonths(monthNum);
        return concatCurrentYearMonth(nextConsumedDate);
    }

    /**
     * 将date转换成年月字符串
     *
     * @param date 日期
     * @return 'yyyy-mm'
     */
    public static String dateToYearMonthStr(Date date) {
        return DateFormatUtils.format(date, "yyyy-MM");
    }

    public static String parseTimestampToYYYMMDDStr(long timestamp) {
        Date date = new Date(timestamp);
        return formatDateYYYMMDD(date);
    }

    public static String parseTimestampToYYYYMMDDHHmmStr(long timestamp) {
        Date date = new Date(timestamp);
        return formatDateYYYMMDDHHmm(date);
    }

    public static String localDateTimeToYYYYMMDDHHmmStr(LocalDateTime time) {
        return formatLocalDateTime(time, "yyyy-MM-dd HH:mm");
    }

    public static String localDateTimeToYYYYMMDDStr(LocalDateTime time) {
        return formatLocalDateTime(time, "yyyy-MM-dd");
    }

    public static LocalDateTime parseTimestampToLocalDateTime(long timestamp) {
        Date date = new Date(timestamp);
        return dateToLocalDateTime(date);
    }

    public static Long localDateTimeToTimestamp(LocalDateTime expiredTime) {
        return localDateTimeToUdate(expiredTime).getTime();
    }

    /**
     * 获取当前时间n小时之前德时间
     * @return
     */
    public static Date getTimeBeforeHours(){
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.HOUR, -3);
        return calendar.getTime();
    }

    /**
     * 计算两个时间差【秒】
     *
     * @return
     */
    public static long getDurationSeconds(Temporal startInclusive, Temporal endExclusive) {
        return Duration.between(startInclusive, endExclusive).getSeconds();
    }

    /**
     * 计算两个日期间相差天数
     *
     * @param startDate
     * @param endDate
     * @return
     * @throws ParseException
     */
    public static int getDayDiffer(Date startDate, Date endDate) {
        try {
            //判断是否跨年
            SimpleDateFormat yearFormat = new SimpleDateFormat("yyyy");
            String startYear = yearFormat.format(startDate);
            String endYear = yearFormat.format(endDate);
            if (startYear.equals(endYear)) {
                /*  使用Calendar跨年的情况会出现问题    */
                Calendar calendar = Calendar.getInstance();
                calendar.setTime(startDate);
                int startDay = calendar.get(Calendar.DAY_OF_YEAR);
                calendar.setTime(endDate);
                int endDay = calendar.get(Calendar.DAY_OF_YEAR);
                return endDay - startDay;
            } else {
                /*  跨年不会出现问题，需要注意不满24小时情况（2016-03-18 11:59:59 和 2016-03-19 00:00:01的话差值为 0）  */
                //  只格式化日期，消除不满24小时影响

                SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
                long startDateTime = dateFormat.parse(dateFormat.format(startDate)).getTime();
                long endDateTime = dateFormat.parse(dateFormat.format(endDate)).getTime();
                return (int) ((endDateTime - startDateTime) / (1000 * 3600 * 24));
            }
        } catch (Exception e) {
            log.error("日期转换Exception，startDate：{},endDate:{}", startDate, endDate, e);
        }
        log.warn("日期转换异常，startDate：{},endDate:{}", startDate, endDate);
        return 0;
    }

    /**
     * 加上秒数
     *
     * @param date    日期
     * @param seconds 秒
     * @return
     */
    public static Date addSeconds(Date date, int seconds) {
        return date == null ? null : new Date(date.getTime() + 1000l * seconds);
    }

    public static String secondToHourString(Long seconds) {
        if (seconds < 60) {
            return seconds + "秒";
        } else if (seconds < 3600) {
            return (seconds / 60) + "分" + (seconds % 60) + "秒";
        } else {
            return (seconds / 3600) + "时" + secondToMinuteSecond(seconds % 3600);
        }
    }

    /**
     * 秒转分钟和秒
     *
     * @param seconds
     * @return
     */
    public static String secondToMinuteSecond(Long seconds) {
        if (seconds < 60) {
            return seconds + "秒";
        } else {
            return (seconds / 60) + "分" + (seconds % 60) + "秒";
        }
    }


    /**
     * 获取当前小时的0分0秒
     *
     * @return
     */
    public static Date getHeadHour(Date time) {
        Calendar ca = Calendar.getInstance();
        ca.setTime(time);
        ca.set(Calendar.MINUTE, 0);
        ca.set(Calendar.SECOND, 0);
        ca.set(Calendar.MILLISECOND, 0);
        Date date = ca.getTime();
        return date;
    }

    /**
     * 获取当前小时的59分59秒
     *
     * @return
     */
    public static Date getTailHour(Date time) {
        Calendar ca = Calendar.getInstance();
        ca.setTime(time);
        ca.set(Calendar.MINUTE, 59);
        ca.set(Calendar.SECOND, 59);
        ca.set(Calendar.MILLISECOND, 999);
        Date date = ca.getTime();
        return date;
    }

    /**
     * 获取今日零点
     *
     * @return
     */
    public static Date getHeadDate(Date time) {
        Calendar calendar2 = Calendar.getInstance();
        calendar2.setTime(time);
        calendar2.set(calendar2.get(Calendar.YEAR), calendar2.get(Calendar.MONTH), calendar2.get(Calendar.DAY_OF_MONTH),
                0, 0, 0);
        Date date = calendar2.getTime();
        return date;
    }

    public static Date addDays(Date date, int day) {
        return date == null ? null : new Date(date.getTime() + 24 * 60 * 60 * 1000l * day);
    }

    public static Date addHours(Date date, int hour) {
        return date == null ? null : new Date(date.getTime() + 1 * 60 * 60 * 1000l * hour);
    }

    /**
     * 是否同一天
     *
     * @param date1
     * @param date2
     * @return
     */
    public static boolean isSameDay(Date date1, Date date2) {
        if (date1 != null && date2 != null) {
            Calendar cal1 = Calendar.getInstance();
            cal1.setTime(date1);
            Calendar cal2 = Calendar.getInstance();
            cal2.setTime(date2);
            return isSameDay(cal1, cal2);
        } else {
            throw new IllegalArgumentException("The date must not be null");
        }
    }

    public static boolean isSameDay(Calendar cal1, Calendar cal2) {
        if (cal1 != null && cal2 != null) {
            return cal1.get(0) == cal2.get(0) && cal1.get(1) == cal2.get(1) && cal1.get(6) == cal2.get(6);
        } else {
            throw new IllegalArgumentException("The date must not be null");
        }
    }

    public static boolean isSameHour(Date date1, Date date2) {
        if (date1 != null && date2 != null) {
            Calendar cal1 = Calendar.getInstance();
            cal1.setTime(date1);
            Calendar cal2 = Calendar.getInstance();
            cal2.setTime(date2);
            return isSamehour(cal1, cal2);
        } else {
            throw new IllegalArgumentException("The date must not be null");
        }
    }

    public static boolean isSamehour(Calendar cal1, Calendar cal2) {
        if (cal1 != null && cal2 != null) {
            return cal1.get(0) == cal2.get(0) && cal1.get(1) == cal2.get(1) && cal1.get(6) == cal2.get(6) && cal1.get(11) == cal2.get(11);
        } else {
            throw new IllegalArgumentException("The date must not be null");
        }
    }

    /**
     * 获取昨天此刻
     * @return
     */
    public static Date getYesterdayDate() {
        Date date = new Date();
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        cal.add(Calendar.DATE, -1);
        return cal.getTime();
    }

    /**
     * 获取前日此刻
     * @return
     */
    public static Date getBeforeYesterdayDate() {
        Date date = new Date();
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        cal.add(Calendar.DATE, -2);
        return cal.getTime();
    }

    /**
     * 判断当前时间距离第二天凌晨的秒数
     *
     * @return 返回值单位为[s:秒]
     */
    public static Long getSecondsNextEarlyMorning() {
        Calendar cal = Calendar.getInstance();
        cal.add(Calendar.DAY_OF_YEAR, 1);
        cal.set(Calendar.HOUR_OF_DAY, 0);
        cal.set(Calendar.SECOND, 0);
        cal.set(Calendar.MINUTE, 0);
        cal.set(Calendar.MILLISECOND, 0);
        return (cal.getTimeInMillis() - System.currentTimeMillis()) / 1000;
    }




    /**
     * 获取指定时间当年的第一天
     * @param date
     * @return
     */
    public static Date getFirstDayDateOfYear(Date date) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        int last = cal.getActualMinimum(Calendar.DAY_OF_YEAR);
        cal.set(Calendar.DAY_OF_YEAR, last);
        return cal.getTime();
    }


    /**
     * 输入一个开始时间和一个结束时间，获取这个区间内的所有时间列表
     *
     * @param startTime
     * @param endTime
     * @param format    输入和输出的格式需要保持一致
     * @param type      1 月类型 2 天类型 3 小时类型 4 年类型
     */
    public static List<String> getDateList(String startTime, String endTime, String format, int type) {
        int type2;
        if (type == 1) { //月
            type2 = Calendar.MONTH;
        } else if (type == 2) {//日
            type2 = Calendar.DAY_OF_MONTH;
        } else if (type == 3) {//小时
            type2 = Calendar.HOUR_OF_DAY;
        } else {
            type2 = Calendar.YEAR;
        }
        List<String> result = new ArrayList<>();
        try {
            GregorianCalendar[] dateArr = getDate(startTime, endTime, format, type2);
            SimpleDateFormat sdf = new SimpleDateFormat(format);
            for (GregorianCalendar e : dateArr) {
                result.add(sdf.format(e.getTime()));
            }
        } catch (ParseException e) {
            e.printStackTrace();
        }

        return result;
    }

    /**
     * 根据开始时间和结束时间获取这段时间范围内的时间列表
     */
    private static GregorianCalendar[] getDate(String startTime, String endTime, String format, int type) throws ParseException {
        Vector<GregorianCalendar> v = new Vector<>();
        SimpleDateFormat sdf = new SimpleDateFormat(format);
        GregorianCalendar gc1 = new GregorianCalendar(), gc2 = new GregorianCalendar();
        gc1.setTime(sdf.parse(startTime));
        gc2.setTime(sdf.parse(endTime));
        do {
            GregorianCalendar gc3 = (GregorianCalendar) gc1.clone();
            v.add(gc3);
            gc1.add(type, 1);
        } while (!gc1.after(gc2));
        return v.toArray(new GregorianCalendar[v.size()]);
    }

    /**
     * 返回当前时间指定天数之后(或之前)的日期
     *
     * @param date 传入日期
     * @param days 天数（可以传正负数）
     * @return type 0：日期，1：开始时刻，2：结束时刻
     */
    public static String getOrderDay(Date date, int days,int type) {

        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        cal.add(Calendar.DATE, days);
        date = cal.getTime();
        String returnDate = (new SimpleDateFormat("yyyy-MM-dd")).format(date);
        if(type == 1){
            return returnDate+" 00:00:00";
        }else if(type == 2){
            return returnDate+" 23:59:59";
        }else{
            return returnDate;
        }
    }

    /**
     * 计算两个日期时间时间差天数
     * @return
     */
    public static int calculateDateDif(Date one,Date two){
        if(one == null || two == null){
            return 0;
        }
        long difference =  (one.getTime()-two.getTime())/86400000;
        int days = (int)Math.abs(difference)+1;
        return days;
    }

    /**
     * 获取某年某月的第一天
     * @Title:getFisrtDayOfMonth
     * @Description:
     * @param:@param year
     * @param:@param month
     * @param:@return
     * @return:String
     * @throws
     */
    public static Date getFirstDayOfMonth(int year,int month) {
        Calendar cal = Calendar.getInstance();
        //设置年份
        cal.set(Calendar.YEAR,year);
        //设置月份
        cal.set(Calendar.MONTH, month-1);
        //获取某月最小天数
        int firstDay = cal.getActualMinimum(Calendar.DAY_OF_MONTH);
        //设置日历中月份的最小天数
        cal.set(Calendar.DAY_OF_MONTH, firstDay);
        cal.set(Calendar.HOUR_OF_DAY, 0);
        cal.set(Calendar.MINUTE, 0);
        cal.set(Calendar.SECOND, 0);
        return cal.getTime();
    }

    /**
     * 获取某月的最后一天
     * @Title:getLastDayOfMonth
     * @Description:
     * @param:@param year
     * @param:@param month
     * @param:@return
     * @return:String
     * @throws
     */
    public static Date getLastDayOfMonth(int year,int month)
    {
        Calendar cal = Calendar.getInstance();
        //设置年份
        cal.set(Calendar.YEAR,year);
        //设置月份
        cal.set(Calendar.MONTH, month-1);
        //获取某月最大天数
        int lastDay = cal.getActualMaximum(Calendar.DAY_OF_MONTH);
        //设置日历中月份的最大天数
        cal.set(Calendar.DAY_OF_MONTH, lastDay);
        cal.set(Calendar.HOUR_OF_DAY, 23);
        cal.set(Calendar.MINUTE, 59);
        cal.set(Calendar.SECOND, 59);
        return cal.getTime();
    }

    /**
     *根据时间范围获得月份集
     * @return
     */
    public static List<String> getRangeSet(String beginDate,String endDate){
        List<String> rangeSet =null;
        SimpleDateFormat sdf = null;
        Date begin_date = null;
        Date end_date = null;
        rangeSet = new java.util.ArrayList<String>();
        sdf = new SimpleDateFormat("yyyy-MM");
        try {
            begin_date = sdf.parse(beginDate);//定义起始日期
            end_date = sdf.parse(endDate);//定义结束日期
        } catch (ParseException e) {
            System.out.println("时间转化异常，请检查你的时间格式是否为yyyy-MM或yyyy-MM-dd");
        }
        Calendar dd = Calendar.getInstance();//定义日期实例
        assert begin_date != null;
        dd.setTime(begin_date);//设置日期起始时间
        while(!dd.getTime().after(end_date)){//判断是否到结束日期
            rangeSet.add(sdf.format(dd.getTime()));
            dd.add(Calendar.MONTH, 1);//进行当前日期月份加1
        }
        return rangeSet;
    }

    /**
     *根据时间范围获得季度集
     * @return
     */
    public static List<String> getRangeSet_Q(String beginDate,String endDate){
        List<String> rangeSet =null;
        SimpleDateFormat sdf = null;
        Date begin_date = null;
        Date end_date = null;
        String[] numStr =null;
        String Q=null;
        rangeSet = new ArrayList<>();
        sdf = new SimpleDateFormat("yyyy-MM");
        try {
            begin_date = sdf.parse(beginDate);//定义起始日期
            end_date = sdf.parse(endDate);//定义结束日期
        } catch (ParseException e) {
            System.out.println("时间转化异常，请检查你的时间格式是否为yyyy-MM或yyyy-MM-dd");
        }
        Calendar dd = Calendar.getInstance();//定义日期实例
        assert begin_date != null;
        dd.setTime(begin_date);//设置日期起始时间
        while(!dd.getTime().after(end_date)){//判断是否到结束日期
            numStr=  sdf.format(dd.getTime()).split("-",0);
            Q = getQuarter(Integer.parseInt(numStr[1]))+"";
            // System.out.println(numStr[0].toString()+"年"+Q+"季");
            if (!rangeSet.contains(numStr[0].toString()+"年"+Q+"季")) {
                rangeSet.add(numStr[0].toString()+"年"+Q+"季");
            }
            dd.add(Calendar.MONTH, 1);//进行当前日期月份加1
        }
        return rangeSet;
    }

    /**
     * 根据月获得季度
     * @param month  月
     * @return  季度
     */
    private static int getQuarter(int month) {
        if(month == 1 || month == 2 || month == 3){
            return 1;
        }else if(month == 4 || month == 5 || month == 6){
            return  2;
        }else if(month == 7 || month == 8 || month == 9){
            return 3;
        }else{
            return 4;
        }
    }

    /**
     * 获取近12个月的月份列表
     * @return
     */
    public static List<String> getMonthList(){
        List<String> list = Lists.newArrayList();
        Calendar c = Calendar.getInstance();
        //取当前时间12个月内的年月
        for(int i = 0; i < 12; i ++){
            int k = c.get(Calendar.YEAR);
            int j = c.get(Calendar.MONTH) + 1 - i;
            String date = "";
            if(j >= 1){
                date = k + "-"+ (j >= 10 ? "" : "0") + j;
            } else {
                int p = 11 - i;
                int m = c.get(Calendar.YEAR) - 1;
                int n = c.get(Calendar.MONTH) + 2 + p;
                date = m + "-" + (n >= 10 ? "" : "0") + n;
            }
            list.add(date);
        }
        return list;
    }

    /**
     * 根据身份证号获取年龄
     * @param
     * @return
     */
    public static int getAge(String idCard){
        if(org.apache.commons.lang3.StringUtils.isBlank(idCard) || idCard.length() != 18){
            return 0;
        }
        int age;
        // 身份证上的年份
        int year = Integer.parseInt(idCard.substring(6,10));
        // 身份证上的月份
        int yue = Integer.parseInt(idCard.substring(10,12));
        // 身份证上的日
        int day = Integer.parseInt(idCard.substring(12,14));
        // 当前时间
        String nowDay = formatDateYYYMMDD();
        int thisYear = Integer.parseInt(nowDay.substring(0,4));
        int thisMonth = Integer.parseInt(nowDay.substring(5,7));
        int thisDay = Integer.parseInt(nowDay.substring(8,10));
        // 判断是否过生日
        if(thisMonth > yue || (thisMonth == yue && thisDay >= day)){
            age = thisYear - year;
        }else{
            age = thisYear - year - 1;
        }
        return age;
    }

    public static void main(String[] args) {
        System.out.println(formatDateYYYMMDDHHmm(getTimeBeforeHours()));
    }
}
