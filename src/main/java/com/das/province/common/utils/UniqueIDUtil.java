package com.das.province.common.utils;

import cn.hutool.core.util.IdUtil;

public class UniqueIDUtil {
    private static Long workerId;
    private static Long datacenterId;
    private UniqueIDUtil() {
    }
    public UniqueIDUtil(Long workerId, Long datacenterId) {
        UniqueIDUtil.workerId = workerId;
        UniqueIDUtil.datacenterId = datacenterId;
    }
    public static String getUniqueID(){
        return IdUtil.getSnowflake(workerId,datacenterId).nextIdStr();
    }
    public static void main(String[] args) {
        System.out.println(getUniqueID());
    }

}
