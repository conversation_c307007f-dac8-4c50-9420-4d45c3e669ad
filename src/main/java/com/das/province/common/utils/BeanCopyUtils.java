package com.das.province.common.utils;

import com.alibaba.fastjson.JSON;

import java.util.List;
import java.util.Objects;

public class BeanCopyUtils {
    public static <T> T copyByJSON(Object source, Class<T> clazz) {
        if (Objects.isNull(source)) {
            throw new NullPointerException("source can't be null");
        }
        String sourceString = JSON.toJSONString(source);
        return JSON.parseObject(sourceString, clazz);
    }
    public static <T> List<T> copyArrayByJSON(Object source, Class<T> clazz) {
        if (Objects.isNull(source)) {
            throw new NullPointerException("source can't be null");
        }
        String sourceString = JSON.toJSONString(source);
        return JSON.parseArray(sourceString, clazz);
    }
}
