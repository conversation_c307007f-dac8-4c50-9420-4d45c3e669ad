package com.das.province.common.utils;


import com.das.province.common.bo.SimplePageInfo;

import java.util.List;

/**
 * 内存分页工具类
 *
 * <AUTHOR>
 * @date 2022/7/12
 */
public class PaginationUtils {

    /**
     * <p>Description: 内存分页 </p>
     *
     * @param data     待分页的数据
     * @param pageNum  当前页码
     * @param pageSize 每页显示的条数
     * @return 分页之后的数据
     */
    public static <T> SimplePageInfo<T> pagination(List<T> data, int pageNum, int pageSize) {
        if (data == null || data.isEmpty()) {
            return new SimplePageInfo<>();
        }
        if (pageNum < 0) {
            pageNum = 1;
        }
        int from = (pageNum - 1) * pageSize;
        int to = pageNum * pageSize;
        if (to > data.size()) {
            to = data.size();
        }
        if (from >= data.size() || to <= from) {
            return new SimplePageInfo<>();
        }
        List<T> list = data.subList(from, to);
        SimplePageInfo<T> simplePageInfo = new SimplePageInfo<>(list);
        simplePageInfo.setTotal(data.size());
        simplePageInfo.setPageNum(pageNum);
        simplePageInfo.setPageSize(pageSize);
        int pages = data.size() / pageSize;
        simplePageInfo.setPages(data.size() % pageSize == 0 ? pages : pages + 1);
        return simplePageInfo;
    }

}
