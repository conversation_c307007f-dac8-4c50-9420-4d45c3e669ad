package com.das.province.common.utils;

import cn.hutool.crypto.digest.DigestAlgorithm;
import cn.hutool.crypto.digest.Digester;

/**
 * <AUTHOR>
 * @date 2023/3/17
 */
public class Md5DigesterUtil {

    public static String digestHex(String context, String salt) {
        Digester md5 = new Digester(DigestAlgorithm.MD5);
        md5.setSalt(salt.getBytes());
        return md5.digestHex(context);
    }
}
