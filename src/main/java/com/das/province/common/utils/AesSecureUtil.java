package com.das.province.common.utils;

import cn.hutool.core.util.CharsetUtil;
import cn.hutool.crypto.symmetric.SymmetricAlgorithm;
import cn.hutool.crypto.symmetric.SymmetricCrypto;

/**
 * <AUTHOR>
 * @date 2023/3/17
 */
public class AesSecureUtil {

    /**
     * 加密
     * @param key 密钥
     * @param content 加密内容
     * @return 密文
     */
    public static String encrypt(String key, String content) {
        SymmetricCrypto aes = new SymmetricCrypto(SymmetricAlgorithm.AES, key.getBytes());
        return aes.encryptHex(content);
    }

    /**
     * 解密
     * @param key 密钥
     * @param content 加密内容
     * @return 明文
     */
    public static String decrypt(String key, String content) {
        SymmetricCrypto aes = new SymmetricCrypto(SymmetricAlgorithm.AES, key.getBytes());
        return aes.decryptStr(content, CharsetUtil.CHARSET_UTF_8);
    }
}
