package com.das.province.common.utils;

import com.das.province.common.builder.HttpClientCustomizeBuilder;
import com.das.province.common.exception.CommonErrorCodeEnum;
import com.das.province.common.exception.CommonException;
import com.das.province.common.http.HttpConfig;
import com.das.province.common.http.HttpMethods;
import com.das.province.common.http.HttpResult;
import com.das.province.common.http.Utils;
import org.apache.http.Header;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.NameValuePair;
import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.*;
import org.apache.http.protocol.HttpContext;
import org.apache.http.util.EntityUtils;

import java.io.IOException;
import java.io.OutputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public class HttpClientUtil {
    private static HttpClient client4HTTP;
    private static HttpClient client4HTTPS;
    static {
        try {
            client4HTTP = HttpClientCustomizeBuilder.custom().build();
            client4HTTPS = HttpClientCustomizeBuilder.custom().ssl().build();
        } catch (Exception e) {
            Utils.errorException("创建https协议的HttpClient对象出错：{}", e);
        }
    }
    private static void create(HttpConfig config) throws CommonException {
        if (config.client() == null) {
            if (config.url().toLowerCase().startsWith("https://")) {
                config.client(client4HTTPS);
            } else {
                config.client(client4HTTP);
            }
        }
    }
    public static String get(HttpClient client, String url, Header[] headers, HttpContext context, String encoding) throws CommonException {
        return get(HttpConfig.custom().client(client).url(url).headers(headers).context(context).encoding(encoding));
    }
    public static String get(HttpConfig config) throws CommonException {
        return send(config.method(HttpMethods.GET));
    }
    public static String post(HttpClient client, String url, Header[] headers, Map<String, Object> parasMap, HttpContext context, String encoding) throws CommonException {
        return post(HttpConfig.custom().client(client).url(url).headers(headers).map(parasMap).context(context).encoding(encoding));
    }
    public static String post(HttpConfig config) throws CommonException {
        return send(config.method(HttpMethods.POST));
    }
    public static String put(HttpClient client, String url, Map<String, Object> parasMap, Header[] headers, HttpContext context, String encoding) throws CommonException {
        return put(HttpConfig.custom().client(client).url(url).headers(headers).map(parasMap).context(context).encoding(encoding));
    }
    public static String put(HttpConfig config) throws CommonException {
        return send(config.method(HttpMethods.PUT));
    }
    public static String delete(HttpClient client, String url, Header[] headers, HttpContext context, String encoding) throws CommonException {
        return delete(HttpConfig.custom().client(client).url(url).headers(headers).context(context).encoding(encoding));
    }
    public static String delete(HttpConfig config) throws CommonException {
        return send(config.method(HttpMethods.DELETE));
    }
    public static String patch(HttpClient client, String url, Map<String, Object> parasMap, Header[] headers, HttpContext context, String encoding) throws CommonException {
        return patch(HttpConfig.custom().client(client).url(url).headers(headers).map(parasMap).context(context).encoding(encoding));
    }
    public static String patch(HttpConfig config) throws CommonException {
        return send(config.method(HttpMethods.PATCH));
    }
    public static String head(HttpClient client, String url, Header[] headers, HttpContext context, String encoding) throws CommonException {
        return head(HttpConfig.custom().client(client).url(url).headers(headers).context(context).encoding(encoding));
    }
    public static String head(HttpConfig config) throws CommonException {
        return send(config.method(HttpMethods.HEAD));
    }
    public static String options(HttpClient client, String url, Header[] headers, HttpContext context, String encoding) throws CommonException {
        return options(HttpConfig.custom().client(client).url(url).headers(headers).context(context).encoding(encoding));
    }
    public static String options(HttpConfig config) throws CommonException {
        return send(config.method(HttpMethods.OPTIONS));
    }
    public static String trace(HttpClient client, String url, Header[] headers, HttpContext context, String encoding) throws CommonException {
        return trace(HttpConfig.custom().client(client).url(url).headers(headers).context(context).encoding(encoding));
    }
    public static String trace(HttpConfig config) throws CommonException {
        return send(config.method(HttpMethods.TRACE));
    }
    public static OutputStream down(HttpClient client, String url, Header[] headers, HttpContext context, OutputStream out) throws CommonException {
        return down(HttpConfig.custom().client(client).url(url).headers(headers).context(context).out(out));
    }
    public static OutputStream down(HttpConfig config) throws CommonException {
        if (config.method() == null) {
            config.method(HttpMethods.GET);
        }
        return fmt2Stream(execute(config), config.out());
    }
    public static String upload(HttpClient client, String url, Header[] headers, HttpContext context) throws CommonException {
        return upload(HttpConfig.custom().client(client).url(url).headers(headers).context(context));
    }
    public static String upload(HttpConfig config) throws CommonException {
        if (config.method() != HttpMethods.POST && config.method() != HttpMethods.PUT) {
            config.method(HttpMethods.POST);
        }
        return send(config);
    }
    public static int status(HttpClient client, String url, Header[] headers, HttpContext context, HttpMethods method) throws CommonException {
        return status(HttpConfig.custom().client(client).url(url).headers(headers).context(context).method(method));
    }
    public static int status(HttpConfig config) throws CommonException {
        return fmt2Int(execute(config));
    }
    public static String send(HttpConfig config) throws CommonException {
        return fmt2String(execute(config), config.getOutEncoding());
    }
    public static HttpResult sendAndGetResp(HttpConfig config) throws CommonException {
        Header[] reqHeaders = config.headers();
        HttpResponse resp = execute(config);
        HttpResult result = new HttpResult(resp);
        result.setResult(fmt2String(resp, config.getOutEncoding()));
        result.setReqHeaders(reqHeaders);
        return result;
    }
    private static HttpResponse execute(HttpConfig config) throws CommonException {
        create(config);
        HttpResponse resp = null;

        try {
            HttpRequestBase request = getRequest(config.url(), config.method());
            request.setConfig(config.requestConfig());
            request.setHeaders(config.headers());
            if (HttpEntityEnclosingRequestBase.class.isAssignableFrom(request.getClass())) {
                List<NameValuePair> nvps = new ArrayList<NameValuePair>();
                if (HttpGet.METHOD_NAME.equals(request.getMethod())) {
                    config.url(Utils.checkHasParas(config.url(), nvps, config.getInEncoding()));
                }
                HttpEntity entity = Utils.map2HttpEntity(nvps, config.map(), config.getInEncoding());
                ((HttpEntityEnclosingRequestBase) request).setEntity(entity);

                Utils.info("请求地址：" + config.url());
                if (nvps.size() > 0) {
                    Utils.info("请求参数：" + nvps.toString());
                }
                if (config.json() != null) {
                    Utils.info("请求参数：" + config.json());
                }
            } else {
                int idx = config.url().indexOf("?");
                Utils.info("请求地址：" + config.url().substring(0, (idx > 0 ? idx : config.url().length())));
                if (idx > 0) {
                    Utils.info("请求参数：" + config.url().substring(idx + 1));
                }
            }
            resp = (config.context() == null) ? config.client().execute(request) : config.client().execute(request, config.context());

            if (config.isReturnRespHeaders()) {
                config.headers(resp.getAllHeaders());
            }
            return resp;

        } catch (IOException e) {
            throw new CommonException(CommonErrorCodeEnum.UNKNOWN_ERROR, e.getMessage());
        }
    }
    private static String fmt2String(HttpResponse resp, String encoding) throws CommonException {
        String body = "";
        try {
            if (resp.getEntity() != null) {
                body = EntityUtils.toString(resp.getEntity(), encoding);
                Utils.info(body);
            } else {
                body = resp.getStatusLine().toString();
            }
            EntityUtils.consume(resp.getEntity());
        } catch (IOException e) {
            throw new CommonException(CommonErrorCodeEnum.UNKNOWN_ERROR, e.getMessage());
        } finally {
            close(resp);
        }
        return body;
    }
    private static int fmt2Int(HttpResponse resp) throws CommonException {
        int statusCode;
        try {
            statusCode = resp.getStatusLine().getStatusCode();
            EntityUtils.consume(resp.getEntity());
        } catch (IOException e) {
            throw new CommonException(CommonErrorCodeEnum.UNKNOWN_ERROR, e.getMessage());
        } finally {
            close(resp);
        }
        return statusCode;
    }
    public static OutputStream fmt2Stream(HttpResponse resp, OutputStream out) throws CommonException {
        try {
            resp.getEntity().writeTo(out);
            EntityUtils.consume(resp.getEntity());
        } catch (IOException e) {
            throw new CommonException(CommonErrorCodeEnum.UNKNOWN_ERROR, e.getMessage());
        } finally {
            close(resp);
        }
        return out;
    }
    private static HttpRequestBase getRequest(String url, HttpMethods method) {
        HttpRequestBase request = null;
        switch (method.getCode()) {
            case 0:
                request = new HttpGet(url);
                break;
            case 1:
                request = new HttpPost(url);
                break;
            case 2:
                request = new HttpHead(url);
                break;
            case 3:
                request = new HttpPut(url);
                break;
            case 4:
                request = new HttpDelete(url);
                break;
            case 5:
                request = new HttpTrace(url);
                break;
            case 6:
                request = new HttpPatch(url);
                break;
            case 7:
                request = new HttpOptions(url);
                break;
            default:
                request = new HttpPost(url);
                break;
        }
        return request;
    }
    private static void close(HttpResponse resp) {
        try {
            if (resp == null) {
                return;
            }
            if (CloseableHttpResponse.class.isAssignableFrom(resp.getClass())) {
                ((CloseableHttpResponse) resp).close();
            }
        } catch (IOException e) {
            Utils.exception(e);
        }
    }
}