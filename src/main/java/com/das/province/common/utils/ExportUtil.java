package com.das.province.common.utils;

import com.alibaba.fastjson.util.IOUtils;
import freemarker.template.Configuration;
import freemarker.template.Template;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Component;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
 * 根据模板导出word工具类
 */

@Component
@Slf4j
public class ExportUtil {

    /**
     * word模板存放路径
     */
    private static String inFilePath;

    /**
     * word生成后临时存放路径
     */
    private static String outFilePath;

    private ExportUtil() {
    }

    public ExportUtil(String inFilePath, String outFilePath) {
        ExportUtil.inFilePath = inFilePath;
        ExportUtil.outFilePath = outFilePath;
    }

    public static void exportWord(Map<String, Object> map, HttpServletResponse response, String fileName,String ftl) {
        InputStream bufferedInputStream = null;
        Writer out = null;
        ServletOutputStream outputStream = null;
        FileOutputStream fileOutputStream = null;
        File outFileDelete = null;
        try {
            Configuration configuration = new Configuration(Configuration.VERSION_2_3_23);
            configuration.setDefaultEncoding(StandardCharsets.UTF_8.name());

            File outPath = new File(outFilePath);
            if(!outPath.exists()){
                outPath.mkdirs();
            }

            File inFile = new File(inFilePath);
            if(!inFile.exists()){
                inFile.mkdirs();
            }

            if(!new File(inFilePath + ftl).exists()){
                copyTemplateFromResource(inFilePath,ftl);
            }

            configuration.setDirectoryForTemplateLoading(inFile);

            String outFileUrl = outFilePath + fileName + ".doc";
            log.info("---outFileUrl---:" + outFileUrl);
            File outFile = new File(outFileUrl);

            Template t = configuration.getTemplate(ftl, StandardCharsets.UTF_8.name());
            fileOutputStream = new FileOutputStream(outFile);
            out = new BufferedWriter(new OutputStreamWriter(fileOutputStream, StandardCharsets.UTF_8.name()), 10240);
            t.process(map, out);
            bufferedInputStream = new BufferedInputStream(new FileInputStream(outFileUrl));
            byte[] buf = new byte[1024];
            int len = 0;
            response.reset();
            String name = outFile.getName();
            //name = new String(name.getBytes("gb2312"), "ISO8859-1");
            name = URLEncoder.encode(name, "gb2312");
            response.setContentType("application/x-msdownload");
            response.addHeader("Content-Disposition", "attachment;filename=" + name);
            outputStream = response.getOutputStream();
            while ((len = bufferedInputStream.read(buf)) > 0) {
                outputStream.write(buf, 0, len);
            }
            outFileDelete = outFile;
        } catch (Exception e) {
            log.error("export Exception fileName:" + fileName, e);
        } finally {
            IOUtils.close(bufferedInputStream);
            IOUtils.close(out);
            IOUtils.close(outputStream);
            IOUtils.close(fileOutputStream);
        }
        if(outFileDelete != null){
            outFileDelete.delete();
        }
    }

    /**
     * 从minio复制模板
     * @param path
     * @param ftl
     */
    public static void copyTemplate(String path,String ftl)  {
        try {
            InputStream templateInput = MinIoUtils.getObject(MinIoUtils.getBucketName(),"template/"+ftl);
            File infile = new File(path + ftl);
            if (Objects.nonNull(templateInput)) {
                FileUtils.copyInputStreamToFile(templateInput,infile);
            }
        } catch (Exception e) {
            log.error("copyTemplate Exception!", e);
        }
    }

    /**
     * 从resource下复制模板
     * @param path
     * @param ftl
     */
    public static void copyTemplateFromResource(String path,String ftl)  {
        try {
            org.springframework.core.io.Resource resource = new ClassPathResource("templates" + File.separator+ ftl);
            InputStream templateInput = resource.getInputStream();
            File infile = new File(path + ftl);
            FileUtils.copyInputStreamToFile(templateInput,infile);
        } catch (Exception e) {
            log.error("copyTemplateFromResource Exception!", e);
        }
    }

    /**
     * 仅生成doc文档的方法
     * @param map 数据装载的键值对
     * @param fileName 文件名称
     * @param ftl 模板名称
     */
    public static File GenerateWordDoc(Map<String, Object> map, String fileName,String ftl){
        Writer out = null;
        File outFile = null;
        FileOutputStream fileOutputStream = null;
        try {
            Configuration configuration = new Configuration(Configuration.VERSION_2_3_23);
            configuration.setDefaultEncoding(StandardCharsets.UTF_8.name());

            File outPath = new File(outFilePath);
            if(!outPath.exists()){
                outPath.mkdirs();
            }

            File inFile = new File(inFilePath);
            if(!inFile.exists()){
                inFile.mkdirs();
            }

            if(!new File(inFilePath + ftl).exists()){
                copyTemplateFromResource(inFilePath,ftl);
            }
            configuration.setDirectoryForTemplateLoading(inFile);

            String outFileUrl = outFilePath + fileName + ".doc";
            log.info("---outFileUrl---:" + outFileUrl);
            outFile = new File(outFileUrl);

            Template t = configuration.getTemplate(ftl, StandardCharsets.UTF_8.name());
            fileOutputStream = new FileOutputStream(outFile);
            out = new BufferedWriter(new OutputStreamWriter(fileOutputStream, StandardCharsets.UTF_8.name()), 10240);
            t.process(map, out);
        } catch (Exception e) {
            log.error("export Exception fileName:" + fileName, e);
        } finally {
            IOUtils.close(out);
            IOUtils.close(fileOutputStream);
        }
        return outFile;
    }

    /**
     * 导出zip
     * @param fileList 文件列表
     * @param zipName 压缩包名称
     * @param response
     */
    public static void exportZip(List<File> fileList, String zipName, HttpServletResponse response){
        byte[] buffer = new byte[1024];
        BufferedOutputStream outputStream = null;
        try {
            outputStream = new BufferedOutputStream(response.getOutputStream());
        } catch (IOException e) {
            e.printStackTrace();
        }

        FileInputStream fileInputStream = null;
        ZipOutputStream zipOutputStream = null;

        try {
            response.reset();
            String fileName = "";
            fileName = URLEncoder.encode(zipName, "gb2312");

            response.setContentType("application/x-msdownload");
            response.setCharacterEncoding("gb2312");
            response.addHeader("Content-Disposition", "attachment;filename=" + fileName + ".zip");

            zipOutputStream = new ZipOutputStream(outputStream);
            for (int i = 0; i < fileList.size(); i++){
                fileInputStream = new FileInputStream(fileList.get(i));

                zipOutputStream.putNextEntry(new ZipEntry(fileList.get(i).getName()));
                int len = -1;
                while ((len = fileInputStream.read(buffer)) != -1){
                    zipOutputStream.write(buffer, 0, len);
                }
                fileInputStream.close();
            }
            zipOutputStream.close();
            outputStream.close();
        } catch (Exception e) {
            e.printStackTrace();
        }finally {
            try {
                if(fileInputStream != null){
                    fileInputStream.close();
                }
                if(zipOutputStream != null){
                    zipOutputStream.close();
                }
            } catch (Exception e){
                e.printStackTrace();
            }
        }
        fileList.forEach(file -> file.delete());
    }

}
