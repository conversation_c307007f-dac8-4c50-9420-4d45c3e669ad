package com.das.province.common.utils;

import com.das.province.common.domain.DomainEvent;
import com.das.province.common.domain.DomainSubscriber;
import com.google.common.eventbus.AsyncEventBus;
import com.google.common.eventbus.EventBus;
import org.apache.commons.lang3.concurrent.BasicThreadFactory;
import org.springframework.transaction.support.TransactionSynchronizationAdapter;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

public class DomainEventHolder {
    private static EventBus asyncEventBus;
    private static EventBus syncEventBus;
    static {
        int coreSize = Runtime.getRuntime().availableProcessors();
        BasicThreadFactory build = new BasicThreadFactory.Builder().namingPattern("event-bus-pool-%d").daemon(true).build();
        ThreadPoolExecutor executor = new ThreadPoolExecutor(coreSize, coreSize * 2, 5, TimeUnit.MINUTES, new LinkedBlockingQueue<Runnable>(1024), build);
        asyncEventBus = new AsyncEventBus("async", executor);
        syncEventBus = new EventBus("sync");
    }
    public static void asyncPublishAfterTransaction(DomainEvent domainEvent) {
        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
            @Override
            public void afterCommit() {
                asyncEventBus.post(domainEvent);
            }
        });
    }
    public static void syncPublishAfterTransaction(DomainEvent domainEvent) {
        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
            @Override
            public void afterCommit() {
                syncEventBus.post(domainEvent);
            }
        });
    }
    public static void asyncPublish(DomainEvent domainEvent) {
        asyncEventBus.post(domainEvent);
    }
    public static void syncPublish(DomainEvent domainEvent) {
        syncEventBus.post(domainEvent);
    }
    public static void asyncRegister(DomainSubscriber subscriber) {
        asyncEventBus.register(subscriber);
    }
    public static void syncRegister(DomainSubscriber subscriber) {
        syncEventBus.register(subscriber);
    }
}
