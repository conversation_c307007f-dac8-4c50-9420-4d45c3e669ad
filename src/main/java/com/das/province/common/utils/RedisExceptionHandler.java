package com.das.province.common.utils;

import lombok.extern.slf4j.Slf4j;
import org.springframework.dao.DataAccessException;
import org.springframework.data.redis.RedisConnectionFailureException;
import org.springframework.data.redis.RedisSystemException;
import org.springframework.data.redis.connection.PoolException;
import org.springframework.data.redis.serializer.SerializationException;

import java.net.ConnectException;
import java.net.SocketTimeoutException;
import java.util.concurrent.TimeoutException;

/**
 * Redis异常处理工具类
 */
@Slf4j
public class RedisExceptionHandler {

    /**
     * 处理Redis异常并返回友好的错误信息
     */
    public static String handleRedisException(Exception e) {
        if (e instanceof RedisConnectionFailureException) {
            log.error("Redis连接失败", e);
            return "Redis服务连接失败，请检查Redis服务状态";
        }
        
        if (e instanceof PoolException) {
            log.error("Redis连接池异常", e);
            return "Redis连接池异常，请检查连接池配置";
        }
        
        if (e instanceof SerializationException) {
            log.error("Redis序列化异常", e);
            return "Redis数据序列化失败，请检查数据格式";
        }
        
        if (e instanceof RedisSystemException) {
            log.error("Redis系统异常", e);
            return "Redis系统异常: " + e.getMessage();
        }
        
        if (e instanceof DataAccessException) {
            log.error("Redis数据访问异常", e);
            return "Redis数据访问异常: " + e.getMessage();
        }
        
        if (e instanceof SocketTimeoutException || e instanceof TimeoutException) {
            log.error("Redis操作超时", e);
            return "Redis操作超时，请稍后重试";
        }
        
        if (e instanceof ConnectException) {
            log.error("Redis连接异常", e);
            return "无法连接到Redis服务器";
        }
        
        // 其他异常
        log.error("Redis未知异常", e);
        return "Redis操作异常: " + e.getMessage();
    }

    /**
     * 判断是否为可重试的异常
     */
    public static boolean isRetryableException(Exception e) {
        return e instanceof SocketTimeoutException ||
               e instanceof TimeoutException ||
               e instanceof RedisConnectionFailureException ||
               e instanceof ConnectException;
    }

    /**
     * 判断是否为连接相关异常
     */
    public static boolean isConnectionException(Exception e) {
        return e instanceof RedisConnectionFailureException ||
               e instanceof ConnectException ||
               e instanceof PoolException;
    }

    /**
     * 判断是否为序列化相关异常
     */
    public static boolean isSerializationException(Exception e) {
        return e instanceof SerializationException;
    }

    /**
     * 获取异常类型
     */
    public static RedisExceptionType getExceptionType(Exception e) {
        if (isConnectionException(e)) {
            return RedisExceptionType.CONNECTION;
        }
        if (isSerializationException(e)) {
            return RedisExceptionType.SERIALIZATION;
        }
        if (e instanceof SocketTimeoutException || e instanceof TimeoutException) {
            return RedisExceptionType.TIMEOUT;
        }
        return RedisExceptionType.UNKNOWN;
    }

    /**
     * Redis异常类型枚举
     */
    public enum RedisExceptionType {
        CONNECTION("连接异常"),
        SERIALIZATION("序列化异常"),
        TIMEOUT("超时异常"),
        UNKNOWN("未知异常");

        private final String description;

        RedisExceptionType(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 带重试的Redis操作执行器
     */
    public static <T> T executeWithRetry(RedisOperation<T> operation, int maxRetries, T defaultValue) {
        Exception lastException = null;
        
        for (int i = 0; i <= maxRetries; i++) {
            try {
                return operation.execute();
            } catch (Exception e) {
                lastException = e;
                
                if (!isRetryableException(e) || i == maxRetries) {
                    break;
                }
                
                try {
                    // 指数退避重试
                    Thread.sleep(Math.min(1000 * (1L << i), 5000));
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                    break;
                }
                
                log.warn("Redis操作失败，正在重试 ({}/{}): {}", i + 1, maxRetries + 1, e.getMessage());
            }
        }
        
        log.error("Redis操作最终失败，返回默认值", lastException);
        return defaultValue;
    }

    /**
     * 带重试的Redis操作执行器（无返回值）
     */
    public static void executeWithRetry(RedisVoidOperation operation, int maxRetries) {
        Exception lastException = null;
        
        for (int i = 0; i <= maxRetries; i++) {
            try {
                operation.execute();
                return;
            } catch (Exception e) {
                lastException = e;
                
                if (!isRetryableException(e) || i == maxRetries) {
                    break;
                }
                
                try {
                    // 指数退避重试
                    Thread.sleep(Math.min(1000 * (1L << i), 5000));
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                    break;
                }
                
                log.warn("Redis操作失败，正在重试 ({}/{}): {}", i + 1, maxRetries + 1, e.getMessage());
            }
        }
        
        log.error("Redis操作最终失败", lastException);
    }

    /**
     * Redis操作接口
     */
    @FunctionalInterface
    public interface RedisOperation<T> {
        T execute() throws Exception;
    }

    /**
     * Redis无返回值操作接口
     */
    @FunctionalInterface
    public interface RedisVoidOperation {
        void execute() throws Exception;
    }

    /**
     * 安全执行Redis操作
     */
    public static <T> T safeExecute(RedisOperation<T> operation, T defaultValue, String operationName) {
        try {
            return operation.execute();
        } catch (Exception e) {
            String errorMsg = handleRedisException(e);
            log.error("Redis操作 [{}] 失败: {}", operationName, errorMsg);
            return defaultValue;
        }
    }

    /**
     * 安全执行Redis操作（无返回值）
     */
    public static void safeExecute(RedisVoidOperation operation, String operationName) {
        try {
            operation.execute();
        } catch (Exception e) {
            String errorMsg = handleRedisException(e);
            log.error("Redis操作 [{}] 失败: {}", operationName, errorMsg);
        }
    }
}
