package com.das.province.common.utils;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;

/**
 * 根据excel模板导入数据工具类
 * @Author: shaochengwei
 * @Date: 2023-01-07
 */
@Slf4j
public class ImportExcelUtil {

    //2003- 版本的excel
    private final static String excel2003L =".xls";
    //2007+ 版本的excel
    private final static String excel2007U =".xlsx";

    /**
     * 描述：获取IO流中的数据，组装成List<List<Object>>对象
     * @param in,fileName
     * @return
     * @throws Exception
     */
    public static List<List<Object>> getListByExcel(InputStream in, String fileName) throws Exception {
        List<List<Object>> list;
        //创建Excel工作薄
        Workbook work = getWorkbook(in,fileName);
        if(work == null){
            throw new Exception("创建Excel工作薄为空！");
        }
        Sheet sheet;
        Row row;
        Cell cell;
        list = new ArrayList<>();
        //遍历Excel中第一个sheet
        sheet = work.getSheetAt(0);
        //根据表头记录列数
        int cellNums = sheet.getRow(sheet.getFirstRowNum()).getLastCellNum();
        //遍历当前sheet中的所有行
        for (int j = sheet.getFirstRowNum(); j < sheet.getLastRowNum()+1; j++) {
            row = sheet.getRow(j);
            if(row==null || row.getFirstCellNum()==j){
                continue;
            }
            //遍历所有的列
            List<Object> li = new ArrayList<>();
            for (int y = 0; y < cellNums; y++) {
                cell = row.getCell(y);
                li.add(getCellValue(cell));
            }
            //第一列数据为空数据无效
            if(StringUtils.isNotBlank(String.valueOf(li.get(0)))){
                list.add(li);
            }
        }
        return list;
    }

    /**
     * 描述：根据文件后缀，自适应上传文件的版本
     * @param inStr,fileName
     * @return
     * @throws Exception
     */
    public static Workbook getWorkbook(InputStream inStr, String fileName) throws Exception{
        Workbook wb ;
        String fileType = fileName.substring(fileName.lastIndexOf("."));
        if(excel2003L.equals(fileType)){
            wb = new HSSFWorkbook(inStr);  //2003-
        }else if(excel2007U.equals(fileType)){
            wb = new XSSFWorkbook(inStr);  //2007+
        }else{
            throw new Exception("解析的文件格式有误！");
        }
        return wb;
    }

    /**
     * 描述：对表格中数值进行格式化
     * @param cell
     * @return
     */
    public  static Object getCellValue(Cell cell){
        Object value = "";
        if (cell!=null){
            switch (cell.getCellTypeEnum()) {
                case STRING:
                    value = cell.getRichStringCellValue().getString();
                    break;
                case NUMERIC:
                    value = cell.getNumericCellValue();
                    break;
                case BOOLEAN:
                    value = cell.getBooleanCellValue();
                    break;
                case BLANK:
                    value = "";
                    break;
                default:
                    break;
            }
        }
        return value;
    }

}
