package com.das.province.common.utils;

import cn.hutool.crypto.asymmetric.KeyType;
import cn.hutool.crypto.asymmetric.RSA;

/**
 * <AUTHOR>
 * @date 2023/3/17
 */
public class RasSecureUtil {

    private static final String PRIVATE_KEY_STR = "MIICdgIBADANBgkqhkiG9w0BAQEFAASCAmAwggJcAgEAAoGBAPcBaVCcrTVpOlE/nOzq+lAp3nvPkRiwdr4FtKKYy8GED1YCbGVs1M7Yube03DDEH5fMw69sI0x0ElLnn3sCooRAhz+yNvaUzI9eLQ6lC+YYIQ50JlODyZ971ULRvZ3ewf5ct6OeCXDScV520tUnBJoZpswJHz+z0mIIvxyD+MzrAgMBAAECgYBh0MRE8RbCnP5EKJ3bUJ552mrJuwQps9ACJj5HWZzu8H4mQGVusO+BTmAVeODtyuz771HbCUbdQuXywNjuHLgAQl+phpyTXCDwBg6O7FVujhx3LHfHxKLsUdgxRmRtBbpXIlhHTdfqqaWL6dehKDG1gjYPzv/NYTBXHDB7/pPS0QJBAP/tJrNFE3vlfQQ7CbvYt+RcTsLNeXabEIeRGydOS7vG2veLP1I6q5DwE6NwON/+D1tEVECxeoGHn66Y9Cl5wnMCQQD3E5prJFTwwNZeVsetJ4Mx39yxi3iuE40fK2UpdNIj2vabIBXCA/jMrIfNER/EHzb4OzWEHVr4plBRuq2GzBWpAkEAvpI4TCu2u36CnkDcGa3iGUEHQRXCSahkIZ7Sym0KBfSonsZoy3F2ygGLjhuFrn2/r6Vs25OM9qEoLdoljjDlswJAQiFQEl8+ENY8SjHF7fWGauH1Ctw+p7D5857Ey8zFte0UVj7HNA+mFvoSuvNbYj3Mk7IdR8gnoie5lbZ2XX7gSQJATSubwaD7orfniuRaWv38uGV2hJu0InVItWG3Fsq2XMvHsbqj9wzIOsPx9ZhtdvByfEEM9stYYAmENkR3bmJS9g==";

    /**
     * 私钥加密
     * @param content 加密内容
     * @return 密文
     */
    public static String privateEncrypt(String content) {
        RSA rsa = new RSA(PRIVATE_KEY_STR, null);
        return rsa.encryptHex(content, KeyType.PrivateKey);
    }

    /**
     * 私钥解密
     * @param content 加密内容
     * @return 明文
     */
    public static String privateDecrypt(String content) {
        RSA rsa = new RSA(PRIVATE_KEY_STR, null);
        return rsa.decryptStr(content, KeyType.PrivateKey);
    }
}
