package com.das.province.common.utils;

import java.util.regex.Pattern;

public class RegexUtil {

    /**
     * 密码校验，密码由四种元素组成（数字、大写字母、小写字母、特殊字符），且必须包含全部四种元素；密码长度大于等于8个字符。
     */
    public static boolean isValid(String password) {
        if(password == null){
            return false;
        }
        String pattern = "^(?![0-9A-Za-z]+$)(?![0-9A-Z\\W]+$)(?![0-9a-z\\W]+$)(?![A-Za-z\\W]+$)[0-9A-Za-z~!@#$%^&*()_+`\\-={}|\\[\\]\\\\:\";'<>?,./]{8,}$";
        return Pattern.matches(pattern, password);
    }

}
