package com.das.province.common.utils;

import com.das.province.common.bo.SimplePageInfo;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

public class SimplePageInfoUtils {
    private SimplePageInfoUtils() {

    }
    public static <T, E> SimplePageInfo<T> convertSimplePageInfoResult(List<E> sourceList, Class<T> clazz) {
        SimplePageInfo<T> targetSimplePageInfo = new SimplePageInfo<>(Collections.emptyList());
        BeanUtils.copyProperties(new SimplePageInfo<>(sourceList), targetSimplePageInfo);
        if (CollectionUtils.isEmpty(sourceList)) {
            return targetSimplePageInfo;
        }
        List<T> result = sourceList.stream().map(source -> {
            T target = BeanUtils.instantiateClass(clazz);
            BeanUtils.copyProperties(source, target);
            return target;
        }).collect(Collectors.toList());
        targetSimplePageInfo.setList(result);
        return targetSimplePageInfo;
    }
}
