package com.das.province.common.utils;

import java.util.UUID;

public class UUIDUtils {
    public static String generateUUID() {
        return UUID.randomUUID().toString().replaceAll("-", "");
    }
    public static String generateUUID(int num) {
        String uid = generateUUID();
        return uid.substring(0, num);
    }
    public static String generateEightUUID() {
        long uid = UUID.randomUUID().getMostSignificantBits();
        if (uid < 0) {
            uid = -uid;
        }
        String str = "" + uid;
        str = str.substring(0, 8);
        return str;
    }
    public static String generateFourUUID() {
        long uid = UUID.randomUUID().getMostSignificantBits();
        if (uid < 0) {
            uid = -uid;
        }
        String str = "" + uid;
        str = str.substring(0, 4);
        return str;
    }
}
