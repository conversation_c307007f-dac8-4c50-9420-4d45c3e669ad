package com.das.province.common.bo;


import com.das.province.common.exception.CommonErrorCodeEnum;
import com.das.province.common.exception.CommonException;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;

@Data
public class PlainResult<T> implements Serializable {
    private static final long serialVersionUID = -6436929142362308687L;
    private Boolean success = true;
    private Integer code = CommonErrorCodeEnum.SUCCESS.getCode();
    private String resultMsg = CommonErrorCodeEnum.SUCCESS.getMessage();
    private String errorMessage = null;
    private String errorStackTrace;
    private String requestId;
    private T data;
    @JsonIgnore
    public boolean isSuccess() {
        return code == CommonErrorCodeEnum.SUCCESS.getCode();
    }
    public static <T> PlainResult<T> success(T data) {
        return success(data, CommonErrorCodeEnum.SUCCESS.getMessage());
    }
    public static <T> PlainResult<T> success(T data, String msg) {
        return new PlainResult<>(CommonErrorCodeEnum.SUCCESS.getCode(), data, msg, null);
    }
    public static <T> PlainResult<T> fail(CommonException ex) {
        String resultMsg = ex.getMessage();
        if (StringUtils.isNotEmpty(ex.getMessage())) {
            resultMsg = ex.getMessage();
        }
        return new PlainResult<>(ex.getCode(), null, resultMsg, ex.getMessage());
    }
    public static <T> PlainResult<T> fail(CommonException ex, T data) {
        String resultMsg = ex.getMessage();
        if (StringUtils.isNotEmpty(ex.getMessage())) {
            resultMsg = ex.getMessage();
        }
        return new PlainResult<>(ex.getCode(), data, resultMsg, ex.getMessage());
    }
    public static <T> PlainResult<T> fail(CommonErrorCodeEnum error) {
        return new PlainResult<>(error.getCode(), null, error.getMessage(), null);
    }
    public static <T> PlainResult<T> fail(CommonErrorCodeEnum error, String msg) {
        return new PlainResult<>(error.getCode(), null, msg, null);
    }
    public static <T> PlainResult<T> fail(int error, T data, String msg) {
        return new PlainResult<>(error, data, msg, null);
    }
    public static <T> PlainResult<T> fail(CommonErrorCodeEnum error, Throwable ex) {
        return new PlainResult<>(error.getCode(), null, error.getMessage(), ex.getMessage());
    }
    public static <T> PlainResult<T> fail(CommonErrorCodeEnum error, String msg, Throwable ex) {
        return new PlainResult<>(error.getCode(), null, msg, ex.getMessage());
    }
    public static <T> PlainResult<T> fail(String requestId, CommonException ex, T data) {
        String resultMsg = ex.getMessage();
        if (StringUtils.isNotEmpty(ex.getMessage())) {
            resultMsg = ex.getMessage();
        }
        return new PlainResult<>(requestId, ex.getCode(), data, resultMsg, ex.getMessage());
    }
    public static PlainResult<String> fail(String requestId, CommonErrorCodeEnum error, String msg, Throwable ex) {
        return new PlainResult<>(requestId, error.getCode(), error.getMessage(), msg, ex.getMessage());
    }
    public static PlainResult<String> fail(String requestId, CommonErrorCodeEnum error, String msg) {
        return new PlainResult<>(requestId, error.getCode(), error.getMessage(), msg, null);
    }
    public PlainResult() {
    }
    public PlainResult(int code, T data, String resultMsg, String errorStackTrace) {
        this.code = code;
        this.data = data;
        this.resultMsg = resultMsg;
        this.errorStackTrace = errorStackTrace;
    }
    public PlainResult(String requestId, int code, T data, String resultMsg, String errorStackTrace) {
        this.code = code;
        this.data = data;
        this.resultMsg = resultMsg;
        this.errorStackTrace = errorStackTrace;
        this.requestId = requestId;
    }
}
