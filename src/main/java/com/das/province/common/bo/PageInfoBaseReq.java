package com.das.province.common.bo;

import com.github.pagehelper.PageHelper;
import lombok.Data;

import java.io.Serializable;

@Data
public class PageInfoBaseReq implements Serializable {
    private static final long serialVersionUID = -6867471008850876480L;
    private Integer pageNum;
    private Integer pageSize;
    private String orderBy;
    private String searchKeyword;
    public void startPage() {
        PageHelper.startPage((pageNum == null || pageNum <= 0) ? 1 : pageNum, (pageSize == null || pageSize < 0) ? 10 : pageSize, orderBy);
    }
    public void startPage(Integer pageNum, Integer pageSize, String orderBy) {
        this.pageNum = pageNum;
        this.pageSize = pageSize;
        this.orderBy = orderBy;
        startPage();
    }
}
