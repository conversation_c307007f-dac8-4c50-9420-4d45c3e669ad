package com.das.province.common.bo;

import com.github.pagehelper.Page;
import com.google.common.collect.Lists;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Objects;

@Data
public class SimplePageInfo<T> implements Serializable {
    private static final long serialVersionUID = 507930521786167142L;
    private int pageNum;
    private int pageSize;
    private int size;
    private String orderBy;
    private int startRow;
    private int endRow;
    private long total;
    private int pages;
    private List<T> list;
    public SimplePageInfo(List<T> list) {
        this(list, 8);
    }
    public SimplePageInfo(List<T> list, int navigatePages) {
        if (list instanceof Page) {
            Page page = (Page) list;
            this.pageNum = page.getPageNum();
            this.pageSize = page.getPageSize();
            this.orderBy = page.getOrderBy();
            this.pages = page.getPages();
            this.list = page;
            this.size = page.size();
            this.total = page.getTotal();
            if (this.size == 0) {
                this.startRow = 0;
                this.endRow = 0;
            } else {
                this.startRow = (int) (page.getStartRow() + 1);
                this.endRow = this.startRow - 1 + this.size;
            }
        } else if (list != null) {
            this.pageNum = 1;
            this.pageSize = list.size();
            this.pages = 1;
            this.list = list;
            this.size = list.size();
            this.total = list.size();
            this.startRow = 0;
            this.endRow = list.isEmpty() ? 0 : list.size() - 1;
        }
    }
    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("PageInfo{");
        sb.append("pageNum=").append(pageNum);
        sb.append(", pageSize=").append(pageSize);
        sb.append(", size=").append(size);
        sb.append(", startRow=").append(startRow);
        sb.append(", endRow=").append(endRow);
        sb.append(", total=").append(total);
        sb.append(", pages=").append(pages);
        sb.append(", list=").append(list);
        sb.append('}');
        return sb.toString();
    }
    @Override
    public boolean equals(Object o) {
        if (this == o)
            return true;
        if (o == null || getClass() != o.getClass())
            return false;
        SimplePageInfo<?> that = (SimplePageInfo<?>) o;
        return pageNum == that.pageNum &&
                pageSize == that.pageSize &&
                size == that.size &&
                startRow == that.startRow &&
                endRow == that.endRow &&
                total == that.total &&
                pages == that.pages &&
                Objects.equals(orderBy, that.orderBy) &&
                Objects.equals(list, that.list);
    }
    @Override
    public int hashCode() {
        return Objects.hash(pageNum, pageSize, size, orderBy, startRow, endRow, total, pages, list);
    }
    public SimplePageInfo() {
        this.pageNum = 1;
        this.pageSize = 10;
        this.list = Lists.newArrayList();
    }
}
