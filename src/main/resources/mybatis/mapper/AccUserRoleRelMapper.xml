<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.das.province.infr.mapper.AccUserRoleRelMapper">
    <resultMap id="BaseResultMap" type="com.das.province.infr.dataobject.AccUserRoleRelDO">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="company_id" jdbcType="BIGINT" property="companyId"/>
        <result column="user_id" jdbcType="BIGINT" property="userId"/>
        <result column="role_id" jdbcType="BIGINT" property="roleId"/>
        <result column="creator" jdbcType="BIGINT" property="creator"/>
        <result column="modifier" jdbcType="BIGINT" property="modifier"/>
        <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate"/>
        <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified"/>
    </resultMap>
    <sql id="Base_Column_List">
        id
        , company_id, user_id, role_id, creator, modifier, gmt_create, gmt_modified
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from acc_user_role_rel
        where id = #{id,jdbcType=BIGINT}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from acc_user_role_rel
        where id = #{id,jdbcType=BIGINT}
    </delete>
    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.das.province.infr.dataobject.AccUserRoleRelDO"
            useGeneratedKeys="true">
        insert into acc_user_role_rel (company_id, user_id, role_id,
                                       creator, modifier, gmt_create,
                                       gmt_modified)
        values (#{companyId,jdbcType=BIGINT}, #{userId,jdbcType=BIGINT}, #{roleId,jdbcType=BIGINT},
                #{creator,jdbcType=BIGINT}, #{modifier,jdbcType=BIGINT}, #{gmtCreate,jdbcType=TIMESTAMP},
                #{gmtModified,jdbcType=TIMESTAMP})
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id"
            parameterType="com.das.province.infr.dataobject.AccUserRoleRelDO" useGeneratedKeys="true">
        insert into acc_user_role_rel
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="companyId != null">
                company_id,
            </if>
            <if test="userId != null">
                user_id,
            </if>
            <if test="roleId != null">
                role_id,
            </if>
            <if test="creator != null">
                creator,
            </if>
            <if test="modifier != null">
                modifier,
            </if>
            <if test="gmtCreate != null">
                gmt_create,
            </if>
            <if test="gmtModified != null">
                gmt_modified,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="companyId != null">
                #{companyId,jdbcType=BIGINT},
            </if>
            <if test="userId != null">
                #{userId,jdbcType=BIGINT},
            </if>
            <if test="roleId != null">
                #{roleId,jdbcType=BIGINT},
            </if>
            <if test="creator != null">
                #{creator,jdbcType=BIGINT},
            </if>
            <if test="modifier != null">
                #{modifier,jdbcType=BIGINT},
            </if>
            <if test="gmtCreate != null">
                #{gmtCreate,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtModified != null">
                #{gmtModified,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.das.province.infr.dataobject.AccUserRoleRelDO">
        update acc_user_role_rel
        <set>
            <if test="companyId != null">
                company_id = #{companyId,jdbcType=BIGINT},
            </if>
            <if test="userId != null">
                user_id = #{userId,jdbcType=BIGINT},
            </if>
            <if test="roleId != null">
                role_id = #{roleId,jdbcType=BIGINT},
            </if>
            <if test="creator != null">
                creator = #{creator,jdbcType=BIGINT},
            </if>
            <if test="modifier != null">
                modifier = #{modifier,jdbcType=BIGINT},
            </if>
            <if test="gmtCreate != null">
                gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtModified != null">
                gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.das.province.infr.dataobject.AccUserRoleRelDO">
        update acc_user_role_rel
        set company_id   = #{companyId,jdbcType=BIGINT},
            user_id      = #{userId,jdbcType=BIGINT},
            role_id      = #{roleId,jdbcType=BIGINT},
            creator      = #{creator,jdbcType=BIGINT},
            modifier     = #{modifier,jdbcType=BIGINT},
            gmt_create   = #{gmtCreate,jdbcType=TIMESTAMP},
            gmt_modified = #{gmtModified,jdbcType=TIMESTAMP}
        where id = #{id,jdbcType=BIGINT}
    </update>

    <!--  ===========================>自定义方法===========================  -->
    <select id="listByRoleId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from acc_user_role_rel
        where company_id = #{companyId,jdbcType=BIGINT} and role_id = #{roleId,jdbcType=BIGINT}
    </select>

    <select id="listByUserId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from acc_user_role_rel
        where user_id = #{userId,jdbcType=BIGINT}
        <if test="companyId != null">
            and company_id = #{companyId,jdbcType=BIGINT}
        </if>
    </select>

    <select id="listByUserIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from acc_user_role_rel
        where user_id in
        <foreach collection="userIds" open="(" close=")" separator="," item="id" index="index">
            #{id}
        </foreach>
        <if test="companyId != null">
            and company_id = #{companyId,jdbcType=BIGINT}
        </if>
    </select>

    <select id="listByRoleIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from acc_user_role_rel
        where company_id = #{companyId,jdbcType=BIGINT} and role_id in
        <foreach collection="roleIds" open="(" close=")" separator="," item="id" index="index">
            #{id}
        </foreach>
    </select>

    <delete id="batchDeleteUserRole">
        delete
        from acc_user_role_rel
        where company_id = #{companyId,jdbcType=BIGINT}
        and user_id in
        <foreach collection="userIds" open="(" close=")" separator="," item="id" index="index">
        #{id}
        </foreach>
    </delete>

    <insert id="batchInsertUserRole">
        insert into acc_user_role_rel (company_id, user_id, role_id,creator, modifier) values
        <foreach collection="records" item="record" separator=",">
            (#{record.companyId},#{record.userId},#{record.roleId},#{record.creator},#{record.modifier})
        </foreach>
    </insert>

    <select id="listByCompanyId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from acc_user_role_rel
        where company_id = #{companyId,jdbcType=BIGINT}
    </select>
</mapper>