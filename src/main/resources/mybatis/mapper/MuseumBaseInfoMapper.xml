<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.das.province.infr.mapper.MuseumBaseInfoMapper">
  <resultMap id="BaseResultMap" type="com.das.province.infr.dataobject.MuseumBaseInfoDO">
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="museum_name" jdbcType="VARCHAR" property="museumName" />
    <result column="region_code" jdbcType="VARCHAR" property="regionCode" />
    <result column="province_name" jdbcType="VARCHAR" property="provinceName" />
    <result column="city_name" jdbcType="VARCHAR" property="cityName" />
    <result column="county_name" jdbcType="VARCHAR" property="countyName" />
    <result column="level_id" jdbcType="BIGINT" property="levelId" />
    <result column="nature_id" jdbcType="BIGINT" property="natureId" />
    <result column="type_id" jdbcType="BIGINT" property="typeId" />
    <result column="platform_address" jdbcType="VARCHAR" property="platformAddress" />
    <result column="curator_name" jdbcType="VARCHAR" property="curatorName" />
    <result column="report_collection_num" jdbcType="BIGINT" property="reportCollectionNum" />
    <result column="lng" jdbcType="VARCHAR" property="lng" />
    <result column="lat" jdbcType="VARCHAR" property="lat" />
    <result column="curator_phone" jdbcType="VARCHAR" property="curatorPhone" />
    <result column="firemen_name" jdbcType="VARCHAR" property="firemenName" />
    <result column="firemen_phone" jdbcType="VARCHAR" property="firemenPhone" />
    <result column="security_name" jdbcType="VARCHAR" property="securityName" />
    <result column="security_phone" jdbcType="VARCHAR" property="securityPhone" />
    <result column="collection_name" jdbcType="VARCHAR" property="collectionName" />
    <result column="collection_phone" jdbcType="VARCHAR" property="collectionPhone" />
    <result column="patrol_name" jdbcType="VARCHAR" property="patrolName" />
    <result column="patrol_phone" jdbcType="VARCHAR" property="patrolPhone" />
    <result column="settle_in" jdbcType="TINYINT" property="settleIn" />
    <result column="is_delete" jdbcType="TINYINT" property="isDelete" />
    <result column="creator" jdbcType="BIGINT" property="creator" />
    <result column="modifier" jdbcType="BIGINT" property="modifier" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="sdx" jdbcType="INTEGER" property="sdx" />
  </resultMap>
  <sql id="Base_Column_List">
    id, museum_name, region_code, province_name, city_name, county_name, level_id, nature_id, 
    type_id, platform_address, report_collection_num, lng, lat, curator_name, curator_phone, firemen_name, firemen_phone,
    security_name, security_phone, collection_name, collection_phone, patrol_name, patrol_phone, 
    settle_in, is_delete, creator, modifier, gmt_create, gmt_modified, report_collection_num, 
    sdx
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from museum_base_info
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from museum_base_info
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.das.province.infr.dataobject.MuseumBaseInfoDO" useGeneratedKeys="true">
    insert into museum_base_info (id, museum_name, region_code, province_name,
      city_name, county_name, level_id, 
      nature_id, type_id, platform_address, report_collection_num, lng, lat,
      curator_name, curator_phone, firemen_name, 
      firemen_phone, security_name, security_phone, 
      collection_name, collection_phone, patrol_name, 
      patrol_phone, settle_in, is_delete, creator,
      modifier, gmt_create, gmt_modified, sdx
      )
    values (#{id,jdbcType=BIGINT},#{museumName,jdbcType=VARCHAR}, #{regionCode,jdbcType=VARCHAR}, #{provinceName,jdbcType=VARCHAR},
      #{cityName,jdbcType=VARCHAR}, #{countyName,jdbcType=VARCHAR}, #{levelId,jdbcType=BIGINT}, 
      #{natureId,jdbcType=BIGINT}, #{typeId,jdbcType=BIGINT}, #{platformAddress,jdbcType=VARCHAR},
      #{reportCollectionNum,jdbcType=BIGINT}, #{lng,jdbcType=VARCHAR},#{lat,jdbcType=VARCHAR},
      #{curatorName,jdbcType=VARCHAR}, #{curatorPhone,jdbcType=VARCHAR}, #{firemenName,jdbcType=VARCHAR}, 
      #{firemenPhone,jdbcType=VARCHAR}, #{securityName,jdbcType=VARCHAR}, #{securityPhone,jdbcType=VARCHAR}, 
      #{collectionName,jdbcType=VARCHAR}, #{collectionPhone,jdbcType=VARCHAR}, #{patrolName,jdbcType=VARCHAR}, 
      #{patrolPhone,jdbcType=VARCHAR}, #{settleIn,jdbcType=TINYINT}, #{isDelete,jdbcType=TINYINT}, #{creator,jdbcType=BIGINT},
      #{modifier,jdbcType=BIGINT}, #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtModified,jdbcType=TIMESTAMP}, #{sdx,jdbcType=INTEGER}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.das.province.infr.dataobject.MuseumBaseInfoDO" useGeneratedKeys="true">
    insert into museum_base_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="museumName != null">
        museum_name,
      </if>
      <if test="regionCode != null">
        region_code,
      </if>
      <if test="provinceName != null">
        province_name,
      </if>
      <if test="cityName != null">
        city_name,
      </if>
      <if test="countyName != null">
        county_name,
      </if>
      <if test="levelId != null">
        level_id,
      </if>
      <if test="natureId != null">
        nature_id,
      </if>
      <if test="typeId != null">
        type_id,
      </if>
      <if test="platformAddress != null">
        platform_address,
      </if>
      <if test="reportCollectionNum != null">
        report_collection_num,
      </if>
      <if test="lng != null">
        lng,
      </if>
      <if test="lat != null">
        lat,
      </if>
      <if test="curatorName != null">
        curator_name,
      </if>
      <if test="curatorPhone != null">
        curator_phone,
      </if>
      <if test="firemenName != null">
        firemen_name,
      </if>
      <if test="firemenPhone != null">
        firemen_phone,
      </if>
      <if test="securityName != null">
        security_name,
      </if>
      <if test="securityPhone != null">
        security_phone,
      </if>
      <if test="collectionName != null">
        collection_name,
      </if>
      <if test="collectionPhone != null">
        collection_phone,
      </if>
      <if test="patrolName != null">
        patrol_name,
      </if>
      <if test="patrolPhone != null">
        patrol_phone,
      </if>
      <if test="settleIn != null">
        settle_in,
      </if>
      <if test="isDelete != null">
        is_delete,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="modifier != null">
        modifier,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
      <if test="sdx != null">
        sdx,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="museumName != null">
        #{museumName,jdbcType=VARCHAR},
      </if>
      <if test="regionCode != null">
        #{regionCode,jdbcType=VARCHAR},
      </if>
      <if test="provinceName != null">
        #{provinceName,jdbcType=VARCHAR},
      </if>
      <if test="cityName != null">
        #{cityName,jdbcType=VARCHAR},
      </if>
      <if test="countyName != null">
        #{countyName,jdbcType=VARCHAR},
      </if>
      <if test="levelId != null">
        #{levelId,jdbcType=BIGINT},
      </if>
      <if test="natureId != null">
        #{natureId,jdbcType=BIGINT},
      </if>
      <if test="typeId != null">
        #{typeId,jdbcType=BIGINT},
      </if>
      <if test="platformAddress != null">
        #{platformAddress,jdbcType=VARCHAR},
      </if>
      <if test="reportCollectionNum != null">
        #{reportCollectionNum,jdbcType=BIGINT},
      </if>
      <if test="lng != null">
        #{lng,jdbcType=VARCHAR},
      </if>
      <if test="lat != null">
        #{lat,jdbcType=VARCHAR},
      </if>
      <if test="curatorName != null">
        #{curatorName,jdbcType=VARCHAR},
      </if>
      <if test="curatorPhone != null">
        #{curatorPhone,jdbcType=VARCHAR},
      </if>
      <if test="firemenName != null">
        #{firemenName,jdbcType=VARCHAR},
      </if>
      <if test="firemenPhone != null">
        #{firemenPhone,jdbcType=VARCHAR},
      </if>
      <if test="securityName != null">
        #{securityName,jdbcType=VARCHAR},
      </if>
      <if test="securityPhone != null">
        #{securityPhone,jdbcType=VARCHAR},
      </if>
      <if test="collectionName != null">
        #{collectionName,jdbcType=VARCHAR},
      </if>
      <if test="collectionPhone != null">
        #{collectionPhone,jdbcType=VARCHAR},
      </if>
      <if test="patrolName != null">
        #{patrolName,jdbcType=VARCHAR},
      </if>
      <if test="patrolPhone != null">
        #{patrolPhone,jdbcType=VARCHAR},
      </if>
      <if test="settleIn != null">
        #{settleIn,jdbcType=TINYINT},
      </if>
      <if test="isDelete != null">
        #{isDelete,jdbcType=TINYINT},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=BIGINT},
      </if>
      <if test="modifier != null">
        #{modifier,jdbcType=BIGINT},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="sdx != null">
        #{sdx,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.das.province.infr.dataobject.MuseumBaseInfoDO">
    update museum_base_info
    <set>
      <if test="museumName != null">
        museum_name = #{museumName,jdbcType=VARCHAR},
      </if>
      <if test="regionCode != null">
        region_code = #{regionCode,jdbcType=VARCHAR},
      </if>
      <if test="provinceName != null">
        province_name = #{provinceName,jdbcType=VARCHAR},
      </if>
      <if test="cityName != null">
        city_name = #{cityName,jdbcType=VARCHAR},
      </if>
      <if test="countyName != null">
        county_name = #{countyName,jdbcType=VARCHAR},
      </if>
      <if test="levelId != null">
        level_id = #{levelId,jdbcType=BIGINT},
      </if>
      <if test="natureId != null">
        nature_id = #{natureId,jdbcType=BIGINT},
      </if>
      <if test="typeId != null">
        type_id = #{typeId,jdbcType=BIGINT},
      </if>
      <if test="platformAddress != null">
        platform_address = #{platformAddress,jdbcType=VARCHAR},
      </if>
      <if test="reportCollectionNum != null">
        report_collection_num = #{reportCollectionNum,jdbcType=BIGINT},
      </if>
      <if test="lng != null">
        lng = #{lng,jdbcType=VARCHAR},
      </if>
      <if test="lat != null">
        lat = #{lat,jdbcType=VARCHAR},
      </if>
      <if test="curatorName != null">
        curator_name = #{curatorName,jdbcType=VARCHAR},
      </if>
      <if test="curatorPhone != null">
        curator_phone = #{curatorPhone,jdbcType=VARCHAR},
      </if>
      <if test="firemenName != null">
        firemen_name = #{firemenName,jdbcType=VARCHAR},
      </if>
      <if test="firemenPhone != null">
        firemen_phone = #{firemenPhone,jdbcType=VARCHAR},
      </if>
      <if test="securityName != null">
        security_name = #{securityName,jdbcType=VARCHAR},
      </if>
      <if test="securityPhone != null">
        security_phone = #{securityPhone,jdbcType=VARCHAR},
      </if>
      <if test="collectionName != null">
        collection_name = #{collectionName,jdbcType=VARCHAR},
      </if>
      <if test="collectionPhone != null">
        collection_phone = #{collectionPhone,jdbcType=VARCHAR},
      </if>
      <if test="patrolName != null">
        patrol_name = #{patrolName,jdbcType=VARCHAR},
      </if>
      <if test="patrolPhone != null">
        patrol_phone = #{patrolPhone,jdbcType=VARCHAR},
      </if>
      <if test="settleIn != null">
        settle_in = #{settleIn,jdbcType=TINYINT},
      </if>
      <if test="isDelete != null">
        is_delete = #{isDelete,jdbcType=TINYINT},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=BIGINT},
      </if>
      <if test="modifier != null">
        modifier = #{modifier,jdbcType=BIGINT},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="sdx != null">
        sdx = #{sdx,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.das.province.infr.dataobject.MuseumBaseInfoDO">
    update museum_base_info
    set museum_name = #{museumName,jdbcType=VARCHAR},
      region_code = #{regionCode,jdbcType=VARCHAR},
      province_name = #{provinceName,jdbcType=VARCHAR},
      city_name = #{cityName,jdbcType=VARCHAR},
      county_name = #{countyName,jdbcType=VARCHAR},
      level_id = #{levelId,jdbcType=BIGINT},
      nature_id = #{natureId,jdbcType=BIGINT},
      type_id = #{typeId,jdbcType=BIGINT},
      platform_address = #{platformAddress,jdbcType=VARCHAR},
      report_collection_num = #{reportCollectionNum,jdbcType=BIGINT},
      lng = #{lng,jdbcType=VARCHAR},
      lat = #{lat,jdbcType=VARCHAR},
      curator_name = #{curatorName,jdbcType=VARCHAR},
      curator_phone = #{curatorPhone,jdbcType=VARCHAR},
      firemen_name = #{firemenName,jdbcType=VARCHAR},
      firemen_phone = #{firemenPhone,jdbcType=VARCHAR},
      security_name = #{securityName,jdbcType=VARCHAR},
      security_phone = #{securityPhone,jdbcType=VARCHAR},
      collection_name = #{collectionName,jdbcType=VARCHAR},
      collection_phone = #{collectionPhone,jdbcType=VARCHAR},
      patrol_name = #{patrolName,jdbcType=VARCHAR},
      patrol_phone = #{patrolPhone,jdbcType=VARCHAR},
      settle_in = #{settleIn,jdbcType=TINYINT},
      is_delete = #{isDelete,jdbcType=TINYINT},
      creator = #{creator,jdbcType=BIGINT},
      modifier = #{modifier,jdbcType=BIGINT},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      sdx = #{sdx,jdbcType=INTEGER}
    where id = #{id,jdbcType=VARCHAR}
  </update>

  <select id="listByCondition" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from museum_base_info
    where is_delete = 0
    <if test="museumId != null and museumId != '' ">
      and id = #{museumId,jdbcType=VARCHAR}
    </if>
    <if test="museumName != null and museumName != '' ">
      and museum_name like CONCAT('%',#{museumName,jdbcType=VARCHAR},'%')
    </if>
    <if test="regionCode != null and regionCode != '' ">
      and region_code like CONCAT(#{regionCode,jdbcType=VARCHAR},'%')
    </if>
    <if test="levelId != null">
      and level_id = #{levelId,jdbcType=BIGINT}
    </if>
    <if test="natureId != null">
      and nature_id = #{natureId,jdbcType=BIGINT}
    </if>
    <if test="typeId != null">
      and type_id = #{typeId,jdbcType=BIGINT}
    </if>
    <if test="settleIn != null">
      and settle_in = #{settleIn,jdbcType=TINYINT}
    </if>
    order by ${sortBy}
  </select>

  <update id="updateDeleteStatus" >
    update museum_base_info
    set
        is_delete = #{isDelete,jdbcType=TINYINT}
    where id = #{id,jdbcType=VARCHAR}
  </update>

  <select id="groupCityMuseumNum" resultType="com.das.province.infr.dataobjectexpand.CityMuseumNumDO">
    SELECT LEFT
      ( region_code, 4 ) regionCode,
      city_name cityName,
      count( region_code ) museumNum,
      sum( report_collection_num ) reportCollectionNum
    FROM
      museum_base_info
    where is_delete = 0
    <if test="settleIn != null ">
      and settle_in = #{settleIn,jdbcType=TINYINT}
    </if>
    GROUP BY
      LEFT ( region_code, 4 ),
      city_name
    order by museumNum desc
  </select>

  <select id="selectListByIds" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from museum_base_info
    where id in
    <foreach collection="ids" open="(" close=")" separator="," item="id" index="index">
      #{id}
    </foreach>
  </select>

  <select id="selectList" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from museum_base_info
    where 1 = 1
    <if test="isDelete != null ">
      and is_delete = #{isDelete,jdbcType=TINYINT}
    </if>

  </select>

  <select id="selectSettleInList" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from museum_base_info
    where is_delete = 0
    <if test="regionCode != null and regionCode != '' ">
      and region_code like CONCAT(#{regionCode,jdbcType=VARCHAR},'%')
    </if>
    <if test="settleIn != null">
      and settle_in = #{settleIn,jdbcType=TINYINT}
    </if>
  </select>

  <select id="selectListByParam" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from museum_base_info
    where is_delete = 0
    <if test="regionCode != null and regionCode != '' ">
      and region_code like CONCAT(#{regionCode,jdbcType=VARCHAR},'%')
    </if>
    <if test="museumId != null and museumId != '' ">
      and id = #{museumId,jdbcType=VARCHAR}
    </if>
    <if test="settleIn != null">
      and settle_in = #{settleIn,jdbcType=TINYINT}
    </if>
  </select>

  <select id="selectAll" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from museum_base_info
    where is_delete = 0
  </select>
</mapper>