<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.das.province.infr.mapper.AccPermissionMapper">
    <resultMap id="BaseResultMap" type="com.das.province.infr.dataobject.AccPermissionDO">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="company_id" jdbcType="BIGINT" property="companyId"/>
        <result column="permission_name" jdbcType="VARCHAR" property="permissionName"/>
        <result column="permission_code" jdbcType="VARCHAR" property="permissionCode"/>
        <result column="permission_type" jdbcType="VARCHAR" property="permissionType"/>
        <result column="parent_id" jdbcType="BIGINT" property="parentId"/>
        <result column="level" jdbcType="TINYINT" property="level"/>
        <result column="sort_index" jdbcType="TINYINT" property="sortIndex"/>
        <result column="permission" jdbcType="VARCHAR" property="permission"/>
        <result column="permission_status" jdbcType="TINYINT" property="permissionStatus"/>
        <result column="function_status" jdbcType="TINYINT" property="functionStatus"/>
        <result column="is_delete" jdbcType="TINYINT" property="isDelete"/>
        <result column="resource_url" jdbcType="VARCHAR" property="resourceUrl"/>
        <result column="resource_icon" jdbcType="VARCHAR" property="resourceIcon"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="creator" jdbcType="BIGINT" property="creator"/>
        <result column="modifier" jdbcType="BIGINT" property="modifier"/>
        <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate"/>
        <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified"/>
    </resultMap>
    <sql id="Base_Column_List">
        id
        , company_id, permission_name, permission_code, permission_type, parent_id, `level`,
    sort_index, permission, permission_status, function_status, is_delete, resource_url, 
    resource_icon, remark, creator, modifier, gmt_create, gmt_modified
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from acc_permission
        where id = #{id,jdbcType=BIGINT}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from acc_permission
        where id = #{id,jdbcType=BIGINT}
    </delete>
    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.das.province.infr.dataobject.AccPermissionDO"
            useGeneratedKeys="true">
        insert into acc_permission (company_id, permission_name, permission_code,
                                    permission_type, parent_id, `level`,
                                    sort_index, permission, permission_status,
                                    function_status, is_delete, resource_url,
                                    resource_icon, remark, creator,
                                    modifier, gmt_create, gmt_modified)
        values (#{companyId,jdbcType=BIGINT}, #{permissionName,jdbcType=VARCHAR}, #{permissionCode,jdbcType=VARCHAR},
                #{permissionType,jdbcType=VARCHAR}, #{parentId,jdbcType=BIGINT}, #{level,jdbcType=TINYINT},
                #{sortIndex,jdbcType=TINYINT}, #{permission,jdbcType=VARCHAR}, #{permissionStatus,jdbcType=TINYINT},
                #{functionStatus,jdbcType=TINYINT}, #{isDelete,jdbcType=TINYINT}, #{resourceUrl,jdbcType=VARCHAR},
                #{resourceIcon,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR}, #{creator,jdbcType=BIGINT},
                #{modifier,jdbcType=BIGINT}, #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtModified,jdbcType=TIMESTAMP})
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id"
            parameterType="com.das.province.infr.dataobject.AccPermissionDO" useGeneratedKeys="true">
        insert into acc_permission
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="companyId != null">
                company_id,
            </if>
            <if test="permissionName != null">
                permission_name,
            </if>
            <if test="permissionCode != null">
                permission_code,
            </if>
            <if test="permissionType != null">
                permission_type,
            </if>
            <if test="parentId != null">
                parent_id,
            </if>
            <if test="level != null">
                `level`,
            </if>
            <if test="sortIndex != null">
                sort_index,
            </if>
            <if test="permission != null">
                permission,
            </if>
            <if test="permissionStatus != null">
                permission_status,
            </if>
            <if test="functionStatus != null">
                function_status,
            </if>
            <if test="isDelete != null">
                is_delete,
            </if>
            <if test="resourceUrl != null">
                resource_url,
            </if>
            <if test="resourceIcon != null">
                resource_icon,
            </if>
            <if test="remark != null">
                remark,
            </if>
            <if test="creator != null">
                creator,
            </if>
            <if test="modifier != null">
                modifier,
            </if>
            <if test="gmtCreate != null">
                gmt_create,
            </if>
            <if test="gmtModified != null">
                gmt_modified,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="companyId != null">
                #{companyId,jdbcType=BIGINT},
            </if>
            <if test="permissionName != null">
                #{permissionName,jdbcType=VARCHAR},
            </if>
            <if test="permissionCode != null">
                #{permissionCode,jdbcType=VARCHAR},
            </if>
            <if test="permissionType != null">
                #{permissionType,jdbcType=VARCHAR},
            </if>
            <if test="parentId != null">
                #{parentId,jdbcType=BIGINT},
            </if>
            <if test="level != null">
                #{level,jdbcType=TINYINT},
            </if>
            <if test="sortIndex != null">
                #{sortIndex,jdbcType=TINYINT},
            </if>
            <if test="permission != null">
                #{permission,jdbcType=VARCHAR},
            </if>
            <if test="permissionStatus != null">
                #{permissionStatus,jdbcType=TINYINT},
            </if>
            <if test="functionStatus != null">
                #{functionStatus,jdbcType=TINYINT},
            </if>
            <if test="isDelete != null">
                #{isDelete,jdbcType=TINYINT},
            </if>
            <if test="resourceUrl != null">
                #{resourceUrl,jdbcType=VARCHAR},
            </if>
            <if test="resourceIcon != null">
                #{resourceIcon,jdbcType=VARCHAR},
            </if>
            <if test="remark != null">
                #{remark,jdbcType=VARCHAR},
            </if>
            <if test="creator != null">
                #{creator,jdbcType=BIGINT},
            </if>
            <if test="modifier != null">
                #{modifier,jdbcType=BIGINT},
            </if>
            <if test="gmtCreate != null">
                #{gmtCreate,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtModified != null">
                #{gmtModified,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.das.province.infr.dataobject.AccPermissionDO">
        update acc_permission
        <set>
            <if test="companyId != null">
                company_id = #{companyId,jdbcType=BIGINT},
            </if>
            <if test="permissionName != null">
                permission_name = #{permissionName,jdbcType=VARCHAR},
            </if>
            <if test="permissionCode != null">
                permission_code = #{permissionCode,jdbcType=VARCHAR},
            </if>
            <if test="permissionType != null">
                permission_type = #{permissionType,jdbcType=VARCHAR},
            </if>
            <if test="parentId != null">
                parent_id = #{parentId,jdbcType=BIGINT},
            </if>
            <if test="level != null">
                `level` = #{level,jdbcType=TINYINT},
            </if>
            <if test="sortIndex != null">
                sort_index = #{sortIndex,jdbcType=TINYINT},
            </if>
            <if test="permission != null">
                permission = #{permission,jdbcType=VARCHAR},
            </if>
            <if test="permissionStatus != null">
                permission_status = #{permissionStatus,jdbcType=TINYINT},
            </if>
            <if test="functionStatus != null">
                function_status = #{functionStatus,jdbcType=TINYINT},
            </if>
            <if test="isDelete != null">
                is_delete = #{isDelete,jdbcType=TINYINT},
            </if>
            <if test="resourceUrl != null">
                resource_url = #{resourceUrl,jdbcType=VARCHAR},
            </if>
            <if test="resourceIcon != null">
                resource_icon = #{resourceIcon,jdbcType=VARCHAR},
            </if>
            <if test="remark != null">
                remark = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="creator != null">
                creator = #{creator,jdbcType=BIGINT},
            </if>
            <if test="modifier != null">
                modifier = #{modifier,jdbcType=BIGINT},
            </if>
            <if test="gmtCreate != null">
                gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtModified != null">
                gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.das.province.infr.dataobject.AccPermissionDO">
        update acc_permission
        set company_id        = #{companyId,jdbcType=BIGINT},
            permission_name   = #{permissionName,jdbcType=VARCHAR},
            permission_code   = #{permissionCode,jdbcType=VARCHAR},
            permission_type   = #{permissionType,jdbcType=VARCHAR},
            parent_id         = #{parentId,jdbcType=BIGINT},
            `level`           = #{level,jdbcType=TINYINT},
            sort_index        = #{sortIndex,jdbcType=TINYINT},
            permission        = #{permission,jdbcType=VARCHAR},
            permission_status = #{permissionStatus,jdbcType=TINYINT},
            function_status   = #{functionStatus,jdbcType=TINYINT},
            is_delete         = #{isDelete,jdbcType=TINYINT},
            resource_url      = #{resourceUrl,jdbcType=VARCHAR},
            resource_icon     = #{resourceIcon,jdbcType=VARCHAR},
            remark            = #{remark,jdbcType=VARCHAR},
            creator           = #{creator,jdbcType=BIGINT},
            modifier          = #{modifier,jdbcType=BIGINT},
            gmt_create        = #{gmtCreate,jdbcType=TIMESTAMP},
            gmt_modified      = #{gmtModified,jdbcType=TIMESTAMP}
        where id = #{id,jdbcType=BIGINT}
    </update>

    <!--  ===========================>自定义方法===========================  -->

    <select id="selectByPermissionCode" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from acc_permission
        where company_id = #{companyId,jdbcType=BIGINT} and permission_code = #{permissionCode,jdbcType=VARCHAR}
        and is_delete = #{isDelete,jdbcType=TINYINT}
    </select>

    <select id="listByPermissionCodes" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from acc_permission
        where company_id = #{companyId,jdbcType=BIGINT} and is_delete = #{isDelete,jdbcType=TINYINT} and permission_code in
        <foreach collection="permissionCodes" open="(" close=")" separator="," item="id" index="index">
            #{id}
        </foreach>
        order by `level`,sort_index
    </select>

    <select id="listDirectChildByPermissionId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from acc_permission
        where company_id = #{companyId,jdbcType=BIGINT} and parent_id = #{permissionId,jdbcType=BIGINT}
        and is_delete = #{isDelete,jdbcType=TINYINT}
        <if test="permissionType != null">
            and permission_type = #{permissionType,jdbcType=VARCHAR}
        </if>
        order by sort_index
    </select>

    <select id="selectByPermissionId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from acc_permission
        where company_id = #{companyId,jdbcType=BIGINT} and id = #{permissionId,jdbcType=BIGINT}
        and is_delete = #{isDelete,jdbcType=TINYINT}
    </select>

    <select id="listByPermissionIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from acc_permission
        where company_id = #{companyId,jdbcType=BIGINT} and is_delete = #{isDelete,jdbcType=TINYINT} and id in
        <foreach collection="permissionIds" open="(" close=")" separator="," item="id" index="index">
            #{id}
        </foreach>
    </select>

    <select id="listIncludeChildByPermissionId" resultMap="BaseResultMap">
        select
        c.*
        from acc_permission as c join acc_permission_item_rel as r on c.id = r.descendant_id
        where c.company_id = #{companyId,jdbcType=BIGINT} and r.ancestor_id= #{permissionId,jdbcType=INTEGER} and
        r.distance <![CDATA[ >= ]]> 0 and c.is_delete = #{isDelete,jdbcType=TINYINT}
    </select>

    <select id="listIncludeChildByPermissionIds" resultMap="BaseResultMap">
        select
            c.*
        from acc_permission as c join acc_permission_item_rel as r on c.id = r.descendant_id
        where c.company_id = #{companyId,jdbcType=BIGINT} and r.ancestor_id in
        <foreach collection="permissionIds" open="(" close=")" separator="," item="id" index="index">
            #{id}
        </foreach>
        and r.distance <![CDATA[ >= ]]> 0 and c.is_delete = #{isDelete,jdbcType=TINYINT}
    </select>

    <select id="listChildByPermissionId" resultMap="BaseResultMap">
        select
        c.*
        from acc_permission as c join acc_permission_item_rel as r on c.id = r.descendant_id
        where c.company_id = #{companyId,jdbcType=BIGINT} and r.ancestor_id= #{permissionId,jdbcType=INTEGER}
        and r.distance <![CDATA[ > ]]> 0 and c.is_delete = #{isDelete,jdbcType=TINYINT}
    </select>

    <select id="listByCondition" resultMap="BaseResultMap">
        select
        c.*
        from acc_permission as c join acc_permission_item_rel as r on c.id = r.descendant_id
        where c.company_id = #{companyId,jdbcType=BIGINT} and r.ancestor_id= #{permissionId,jdbcType=INTEGER}
        and r.distance <![CDATA[ > ]]> 0 and c.is_delete = #{isDelete,jdbcType=TINYINT}
        <if test="permissionName != null and permissionName != '' ">
            and c.permission_name like concat('%', #{permissionName,jdbcType=VARCHAR}, '%')
        </if>
        <if test="level != null">
            and c.`level` = #{level,jdbcType=TINYINT}
        </if>
        <if test="permissionType != null">
            and c.permission_type = #{permissionType,jdbcType=VARCHAR}
        </if>
        order by c.${sortBy}
    </select>

    <select id="selectRootByCompanyId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from acc_permission
        where company_id = #{companyId,jdbcType=BIGINT} and parent_id is null and is_delete = #{isDelete,jdbcType=TINYINT}
    </select>

    <delete id="deleteFunctionByMenuId">
        delete
        from acc_permission
        where company_id = #{companyId,jdbcType=BIGINT} and parent_id = #{menuPermissionId,jdbcType=BIGINT}
        and permission_type = 'FCN'
    </delete>

    <update id="updateByPermissionIds" parameterType="com.das.province.infr.dataobject.AccPermissionDO">
        update acc_permission
        <set>
            <if test="record.permissionName != null">
                permission_name = #{record.permissionName,jdbcType=VARCHAR},
            </if>
            <if test="record.permissionCode != null">
                permission_code = #{record.permissionCode,jdbcType=VARCHAR},
            </if>
            <if test="record.permissionType != null">
                permission_type = #{record.permissionType,jdbcType=VARCHAR},
            </if>
            <if test="record.parentId != null">
                parent_id = #{record.parentId,jdbcType=BIGINT},
            </if>
            <if test="record.level != null">
                `level` = #{record.level,jdbcType=TINYINT},
            </if>
            <if test="record.sortIndex != null">
                sort_index = #{record.sortIndex,jdbcType=TINYINT},
            </if>
            <if test="record.permission != null">
                permission = #{record.permission,jdbcType=VARCHAR},
            </if>
            <if test="record.permissionStatus != null">
                permission_status = #{record.permissionStatus,jdbcType=TINYINT},
            </if>
            <if test="record.functionStatus != null">
                function_status = #{record.functionStatus,jdbcType=TINYINT},
            </if>
            <if test="record.isDelete != null">
                is_delete = #{record.isDelete,jdbcType=TINYINT},
            </if>
            <if test="record.resourceUrl != null">
                resource_url = #{record.resourceUrl,jdbcType=VARCHAR},
            </if>
            <if test="record.resourceIcon != null">
                resource_icon = #{record.resourceIcon,jdbcType=VARCHAR},
            </if>
            <if test="record.remark != null">
                remark = #{record.remark,jdbcType=VARCHAR},
            </if>
            <if test="record.creator != null">
                creator = #{record.creator,jdbcType=BIGINT},
            </if>
            <if test="record.modifier != null">
                modifier = #{record.modifier,jdbcType=BIGINT},
            </if>
            <if test="record.gmtCreate != null">
                gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
            </if>
            <if test="record.gmtModified != null">
                gmt_modified = #{record.gmtModified,jdbcType=TIMESTAMP},
            </if>
        </set>
        where company_id = #{companyId,jdbcType=BIGINT} and id in
        <foreach collection="permissionIds" open="(" close=")" separator="," item="id" index="index">
            #{id}
        </foreach>
    </update>
</mapper>