<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.das.province.infr.mapper.StatisticVisitorDayMapper">
  <resultMap id="BaseResultMap" type="com.das.province.infr.dataobject.StatisticVisitorDayDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="statistic_day" jdbcType="VARCHAR" property="statisticDay" />
    <result column="museum_id" jdbcType="VARCHAR" property="museumId" />
    <result column="museum_name" jdbcType="VARCHAR" property="museumName" />
    <result column="region_code" jdbcType="VARCHAR" property="regionCode" />
    <result column="city_name" jdbcType="VARCHAR" property="cityName" />
    <result column="total_num" jdbcType="INTEGER" property="totalNum" />
    <result column="online_num" jdbcType="INTEGER" property="onlineNum" />
    <result column="holographic_num" jdbcType="INTEGER" property="holographicNum" />
    <result column="overall_view_num" jdbcType="INTEGER" property="overallViewNum" />
    <result column="order_num" jdbcType="INTEGER" property="orderNum" />
    <result column="offline_num" jdbcType="INTEGER" property="offlineNum" />
    <result column="personal_num" jdbcType="INTEGER" property="personalNum" />
    <result column="team_man_num" jdbcType="INTEGER" property="teamManNum" />
    <result column="team_num" jdbcType="INTEGER" property="teamNum" />
    <result column="adult_num" jdbcType="INTEGER" property="adultNum" />
    <result column="children_num" jdbcType="INTEGER" property="childrenNum" />
    <result column="province_num" jdbcType="INTEGER" property="provinceNum" />
    <result column="outside_num" jdbcType="INTEGER" property="outsideNum" />
    <result column="abroad_num" jdbcType="INTEGER" property="abroadNum" />
    <result column="man_num" jdbcType="INTEGER" property="manNum" />
    <result column="woman_num" jdbcType="INTEGER" property="womanNum" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
  </resultMap>
  <sql id="Base_Column_List">
    id, statistic_day, museum_id, museum_name, region_code, city_name, total_num, online_num, 
    holographic_num, overall_view_num, order_num, offline_num, personal_num, team_man_num,
    team_num, adult_num, children_num, province_num, outside_num, abroad_num, man_num, woman_num,
    gmt_create, gmt_modified
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from statistic_visitor_day
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from statistic_visitor_day
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.das.province.infr.dataobject.StatisticVisitorDayDO" useGeneratedKeys="true">
    insert into statistic_visitor_day (statistic_day, museum_id, museum_name, 
      region_code, city_name, total_num, 
      online_num, holographic_num, overall_view_num, 
      order_num, offline_num, personal_num, 
      team_man_num, team_num, adult_num, children_num,
      province_num, outside_num, abroad_num, man_num, woman_num,
      gmt_create, gmt_modified
      )
    values (#{statisticDay,jdbcType=VARCHAR}, #{museumId,jdbcType=VARCHAR}, #{museumName,jdbcType=VARCHAR},
      #{regionCode,jdbcType=VARCHAR}, #{cityName,jdbcType=VARCHAR}, #{totalNum,jdbcType=INTEGER}, 
      #{onlineNum,jdbcType=INTEGER}, #{holographicNum,jdbcType=INTEGER}, #{overallViewNum,jdbcType=INTEGER}, 
      #{orderNum,jdbcType=INTEGER}, #{offlineNum,jdbcType=INTEGER}, #{personalNum,jdbcType=INTEGER}, 
      #{teamManNum,jdbcType=INTEGER}, #{teamNum,jdbcType=INTEGER}, #{adultNum,jdbcType=INTEGER},
      #{childrenNum,jdbcType=INTEGER},#{provinceNum,jdbcType=INTEGER},#{outsideNum,jdbcType=INTEGER},
      #{abroadNum,jdbcType=INTEGER},#{manNum,jdbcType=INTEGER},#{womanNum,jdbcType=INTEGER},
      #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtModified,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.das.province.infr.dataobject.StatisticVisitorDayDO" useGeneratedKeys="true">
    insert into statistic_visitor_day
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="statisticDay != null">
        statistic_day,
      </if>
      <if test="museumId != null">
        museum_id,
      </if>
      <if test="museumName != null">
        museum_name,
      </if>
      <if test="regionCode != null">
        region_code,
      </if>
      <if test="cityName != null">
        city_name,
      </if>
      <if test="totalNum != null">
        total_num,
      </if>
      <if test="onlineNum != null">
        online_num,
      </if>
      <if test="holographicNum != null">
        holographic_num,
      </if>
      <if test="overallViewNum != null">
        overall_view_num,
      </if>
      <if test="orderNum != null">
        order_num,
      </if>
      <if test="offlineNum != null">
        offline_num,
      </if>
      <if test="personalNum != null">
        personal_num,
      </if>
      <if test="teamManNum != null">
        team_man_num,
      </if>
      <if test="teamNum != null">
        team_num,
      </if>
      <if test="adultNum != null">
        adult_num,
      </if>
      <if test="childrenNum != null">
        children_num,
      </if>
      <if test="provinceNum != null">
        province_num,
      </if>
      <if test="outsideNum != null">
        outside_num,
      </if>
      <if test="abroadNum != null">
        abroad_num,
      </if>
      <if test="manNum != null">
        man_num,
      </if>
      <if test="womanNum != null">
        woman_num,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="statisticDay != null">
        #{statisticDay,jdbcType=VARCHAR},
      </if>
      <if test="museumId != null">
        #{museumId,jdbcType=VARCHAR},
      </if>
      <if test="museumName != null">
        #{museumName,jdbcType=VARCHAR},
      </if>
      <if test="regionCode != null">
        #{regionCode,jdbcType=VARCHAR},
      </if>
      <if test="cityName != null">
        #{cityName,jdbcType=VARCHAR},
      </if>
      <if test="totalNum != null">
        #{totalNum,jdbcType=INTEGER},
      </if>
      <if test="onlineNum != null">
        #{onlineNum,jdbcType=INTEGER},
      </if>
      <if test="holographicNum != null">
        #{holographicNum,jdbcType=INTEGER},
      </if>
      <if test="overallViewNum != null">
        #{overallViewNum,jdbcType=INTEGER},
      </if>
      <if test="orderNum != null">
        #{orderNum,jdbcType=INTEGER},
      </if>
      <if test="offlineNum != null">
        #{offlineNum,jdbcType=INTEGER},
      </if>
      <if test="personalNum != null">
        #{personalNum,jdbcType=INTEGER},
      </if>
      <if test="teamManNum != null">
        #{teamManNum,jdbcType=INTEGER},
      </if>
      <if test="teamNum != null">
        #{teamNum,jdbcType=INTEGER},
      </if>
      <if test="adultNum != null">
        #{adultNum,jdbcType=INTEGER},
      </if>
      <if test="childrenNum != null">
        #{childrenNum,jdbcType=INTEGER},
      </if>
      <if test="provinceNum != null">
        #{provinceNum,jdbcType=INTEGER},
      </if>
      <if test="outsideNum != null">
        #{outsideNum,jdbcType=INTEGER},
      </if>
      <if test="abroadNum != null">
        #{abroadNum,jdbcType=INTEGER},
      </if>
      <if test="manNum != null">
        #{manNum,jdbcType=INTEGER},
      </if>
      <if test="womanNum != null">
        #{womanNum,jdbcType=INTEGER},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.das.province.infr.dataobject.StatisticVisitorDayDO">
    update statistic_visitor_day
    <set>
      <if test="statisticDay != null">
        statistic_day = #{statisticDay,jdbcType=VARCHAR},
      </if>
      <if test="museumId != null">
        museum_id = #{museumId,jdbcType=VARCHAR},
      </if>
      <if test="museumName != null">
        museum_name = #{museumName,jdbcType=VARCHAR},
      </if>
      <if test="regionCode != null">
        region_code = #{regionCode,jdbcType=VARCHAR},
      </if>
      <if test="cityName != null">
        city_name = #{cityName,jdbcType=VARCHAR},
      </if>
      <if test="totalNum != null">
        total_num = #{totalNum,jdbcType=INTEGER},
      </if>
      <if test="onlineNum != null">
        online_num = #{onlineNum,jdbcType=INTEGER},
      </if>
      <if test="holographicNum != null">
        holographic_num = #{holographicNum,jdbcType=INTEGER},
      </if>
      <if test="overallViewNum != null">
        overall_view_num = #{overallViewNum,jdbcType=INTEGER},
      </if>
      <if test="orderNum != null">
        order_num = #{orderNum,jdbcType=INTEGER},
      </if>
      <if test="offlineNum != null">
        offline_num = #{offlineNum,jdbcType=INTEGER},
      </if>
      <if test="personalNum != null">
        personal_num = #{personalNum,jdbcType=INTEGER},
      </if>
      <if test="teamManNum != null">
        team_man_num = #{teamManNum,jdbcType=INTEGER},
      </if>
      <if test="teamNum != null">
        team_num = #{teamNum,jdbcType=INTEGER},
      </if>
      <if test="adultNum != null">
        adult_num = #{adultNum,jdbcType=INTEGER},
      </if>
      <if test="childrenNum != null">
        children_num = #{childrenNum,jdbcType=INTEGER},
      </if>
      <if test="provinceNum != null">
        province_num = #{provinceNum,jdbcType=INTEGER},
      </if>
      <if test="outsideNum != null">
        outside_num = #{outsideNum,jdbcType=INTEGER},
      </if>
      <if test="abroadNum != null">
        abroad_num = #{abroadNum,jdbcType=INTEGER},
      </if>
      <if test="manNum != null">
        man_num = #{manNum,jdbcType=INTEGER},
      </if>
      <if test="womanNum != null">
        woman_num = #{womanNum,jdbcType=INTEGER},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.das.province.infr.dataobject.StatisticVisitorDayDO">
    update statistic_visitor_day
    set statistic_day = #{statisticDay,jdbcType=VARCHAR},
      museum_id = #{museumId,jdbcType=VARCHAR},
      museum_name = #{museumName,jdbcType=VARCHAR},
      region_code = #{regionCode,jdbcType=VARCHAR},
      city_name = #{cityName,jdbcType=VARCHAR},
      total_num = #{totalNum,jdbcType=INTEGER},
      online_num = #{onlineNum,jdbcType=INTEGER},
      holographic_num = #{holographicNum,jdbcType=INTEGER},
      overall_view_num = #{overallViewNum,jdbcType=INTEGER},
      order_num = #{orderNum,jdbcType=INTEGER},
      offline_num = #{offlineNum,jdbcType=INTEGER},
      personal_num = #{personalNum,jdbcType=INTEGER},
      team_man_num = #{teamManNum,jdbcType=INTEGER},
      team_num = #{teamNum,jdbcType=INTEGER},
      adult_num = #{adultNum,jdbcType=INTEGER},
      children_num = #{childrenNum,jdbcType=INTEGER},
      province_num = #{provinceNum,jdbcType=INTEGER},
      outside_num = #{outsideNum,jdbcType=INTEGER},
      abroad_num = #{abroadNum,jdbcType=INTEGER},
      man_num = #{manNum,jdbcType=INTEGER},
      woman_num = #{womanNum,jdbcType=INTEGER},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_modified = #{gmtModified,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="listByStatisticDay" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from statistic_visitor_day
    where
      statistic_day = #{statisticDay,jdbcType=VARCHAR}
    <if test="regionCode != null and regionCode != '' ">
      and region_code like CONCAT(#{regionCode,jdbcType=VARCHAR},'%')
    </if>
  </select>

  <select id="listByMuseumIds" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from statistic_visitor_day
    where
    statistic_day = #{statisticDay,jdbcType=VARCHAR}
    <if test="museumIds != null and museumIds.size() > 0 ">
      and museum_id in
      <foreach collection="museumIds" open="(" close=")" separator="," item="museumId" index="index">
        #{museumId}
      </foreach>
    </if>
  </select>

  <select id="listByTimes" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from statistic_visitor_day
    where 1 = 1
    <if test="startDay != null and startDay != '' ">
        and statistic_day >= #{startDay,jdbcType=VARCHAR}
    </if>
    <if test="endDay != null and endDay != '' ">
        and statistic_day &lt;= #{endDay,jdbcType=VARCHAR}
    </if>
    <if test="museumIds != null and museumIds.size() > 0 ">
      and museum_id in
      <foreach collection="museumIds" open="(" close=")" separator="," item="museumId" index="index">
        #{museumId}
      </foreach>
    </if>
  </select>

  <select id="selectList" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from statistic_visitor_day
  </select>

  <insert id="batchInsert">
    insert into statistic_visitor_day (statistic_day, museum_id, museum_name,
    region_code, city_name, total_num,
    online_num, holographic_num, overall_view_num,
    order_num, offline_num, personal_num,
    team_man_num, team_num, adult_num, children_num,
    province_num, outside_num, abroad_num, man_num, woman_num) values
    <foreach collection="visitorDayDOList" item="t" index="index" separator=",">
      (
      #{t.statisticDay,jdbcType=VARCHAR}, #{t.museumId,jdbcType=VARCHAR}, #{t.museumName,jdbcType=VARCHAR},
      #{t.regionCode,jdbcType=VARCHAR}, #{t.cityName,jdbcType=VARCHAR}, #{t.totalNum,jdbcType=INTEGER},
      #{t.onlineNum,jdbcType=INTEGER}, #{t.holographicNum,jdbcType=INTEGER}, #{t.overallViewNum,jdbcType=INTEGER},
      #{t.orderNum,jdbcType=INTEGER}, #{t.offlineNum,jdbcType=INTEGER}, #{t.personalNum,jdbcType=INTEGER},
      #{t.teamManNum,jdbcType=INTEGER}, #{t.teamNum,jdbcType=INTEGER}, #{t.adultNum,jdbcType=INTEGER},
      #{t.childrenNum,jdbcType=INTEGER}, #{t.provinceNum,jdbcType=INTEGER}, #{t.outsideNum,jdbcType=INTEGER},
      #{t.abroadNum,jdbcType=INTEGER}, #{t.manNum,jdbcType=INTEGER}, #{t.womanNum,jdbcType=INTEGER}
      )
    </foreach>
  </insert>

  <delete id="deleteByMuseumIdAndStatisticDay">
    delete from statistic_visitor_day
    where 1 = 1
    <if test="museumId != null and museumId != '' ">
    and museum_id = #{museumId,jdbcType=VARCHAR}
    </if>
    <if test="statisticDay != null and statisticDay != '' ">
        and statistic_day = #{statisticDay,jdbcType=VARCHAR}
    </if>
  </delete>

  <select id="groupVisitorMonthData" resultType="com.das.province.infr.dataobjectexpand.GroupVisitorMonthDataDO">
    select
      museum_id museumId,
      DATE_FORMAT(statistic_day,'%Y-%m') statisticMonth,
      sum(total_num) totalNum,
      sum(online_num) onlineNum,
      sum(offline_num) offlineNum
    from statistic_visitor_day
    group by museum_id,DATE_FORMAT(statistic_day,'%Y-%m')
  </select>

  <select id="groupVisitorYearData" resultType="com.das.province.infr.dataobjectexpand.GroupVisitorYearDataDO">
    select
      museum_id museumId,
      sum(total_num) totalNum,
      sum(online_num) onlineNum,
      sum(offline_num) offlineNum
    from statistic_visitor_day
    where statistic_day >= #{startDay,jdbcType=VARCHAR}
    group by museum_id
  </select>

  <select id="groupMuseumByHoliday" resultType="com.das.province.infr.dataobjectexpand.GroupMuseumByHolidayDO">
    select
      museum_id museumId,
      sum(total_num) totalNum
    from statistic_visitor_day
    where statistic_day >= #{startDay,jdbcType=VARCHAR}
    and statistic_day &lt;= #{endDay,jdbcType=VARCHAR}
    group by museum_id
  </select>

  <select id="groupVisitorByTime" resultType="com.das.province.infr.dataobjectexpand.GroupVisitorByTimeDO">
    select
        LEFT (region_code,4) regionCode,
        sum(total_num) totalVisitorNum,
        sum(online_num) onlineNum,
        sum(offline_num) offlineNum,
        sum(personal_num) personalNum,
        sum(team_num) teamNum,
        sum(team_man_num) teamPeopleNum,
        sum(order_num) reserveNum,
        sum(adult_num) adultNum,
        sum(children_num) childrenNum,
        sum(province_num) provinceNum,
        sum(outside_num) outsideNum,
        sum(abroad_num) abroadNum,
        sum(man_num) manNum,
        sum(woman_num) womanNum
    from statistic_visitor_day
    where 1 = 1
    <if test="startDay != null and startDay != '' ">
      and statistic_day >= #{startDay,jdbcType=VARCHAR}
    </if>
    <if test="endDay != null and endDay != '' ">
      and statistic_day &lt;= #{endDay,jdbcType=VARCHAR}
    </if>
    group by LEFT ( region_code, 4 )
  </select>

  <select id="groupVisitorByMuseum" resultType="com.das.province.infr.dataobjectexpand.GroupVisitorByMuseumDO">
    select
      museum_id museumId,
      museum_name museumName,
      sum(total_num) totalVisitorNum,
      sum(online_num) onlineNum,
      sum(offline_num) offlineNum,
      sum(personal_num) personalNum,
      sum(team_num) teamNum,
      sum(team_man_num) teamPeopleNum,
      sum(order_num) reserveNum,
      sum(adult_num) adultNum,
      sum(children_num) childrenNum,
      sum(province_num) provinceNum,
      sum(outside_num) outsideNum,
      sum(abroad_num) abroadNum,
      sum(man_num) manNum,
      sum(woman_num) womanNum
      from statistic_visitor_day
    where 1 = 1
    <if test="startDay != null and startDay != '' ">
      and statistic_day >= #{startDay,jdbcType=VARCHAR}
    </if>
    <if test="endDay != null and endDay != '' ">
      and statistic_day &lt;= #{endDay,jdbcType=VARCHAR}
    </if>
    <if test="museumIds != null and museumIds.size() > 0 ">
      and museum_id in
      <foreach collection="museumIds" open="(" close=")" separator="," item="museumId" index="index">
        #{museumId}
      </foreach>
    </if>
    group by museum_id,museum_name
  </select>

  <select id="groupVisitorByCity" resultType="com.das.province.infr.dataobjectexpand.GroupVisitorByCityDO">
    select
    left(region_code,4) regionCode,
    city_name cityName,
    sum(offline_num) offlineNum
    from statistic_visitor_day
    where 1 = 1
    <if test="startDay != null and startDay != '' ">
      and statistic_day >= #{startDay,jdbcType=VARCHAR}
    </if>
    <if test="endDay != null and endDay != '' ">
      and statistic_day &lt;= #{endDay,jdbcType=VARCHAR}
    </if>
    group by left(region_code,4),city_name
  </select>

  <select id="groupVisitorByDateType" resultType="com.das.province.infr.dataobjectexpand.GroupVisitorByDateTypeDO">
    select
    LEFT (statistic_day,#{dateLength,jdbcType=INTEGER}) dateStr,
    sum(offline_num) visitorNum
    from statistic_visitor_day
    where 1 = 1
    <if test="startDay != null and startDay != '' ">
      and statistic_day >= #{startDay,jdbcType=VARCHAR}
    </if>
    <if test="endDay != null and endDay != '' ">
      and statistic_day &lt;= #{endDay,jdbcType=VARCHAR}
    </if>
    and museum_id in
    <foreach collection="museumIds" open="(" close=")" separator="," item="museumId" index="index">
      #{museumId}
    </foreach>
    group by LEFT (statistic_day,#{dateLength,jdbcType=INTEGER})
    order by dateStr
  </select>

</mapper>