<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.das.province.infr.mapper.AccRoleMapper">
    <resultMap id="BaseResultMap" type="com.das.province.infr.dataobject.AccRoleDO">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="company_id" jdbcType="BIGINT" property="companyId"/>
        <result column="role_name" jdbcType="VARCHAR" property="roleName"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="status" jdbcType="TINYINT" property="status"/>
        <result column="is_delete" jdbcType="TINYINT" property="isDelete"/>
        <result column="creator" jdbcType="BIGINT" property="creator"/>
        <result column="modifier" jdbcType="BIGINT" property="modifier"/>
        <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate"/>
        <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified"/>
    </resultMap>
    <sql id="Base_Column_List">
        id
        , company_id, role_name, remark, `status`, is_delete, creator, modifier, gmt_create,
    gmt_modified
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from acc_role
        where id = #{id,jdbcType=BIGINT}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from acc_role
        where id = #{id,jdbcType=BIGINT}
    </delete>
    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.das.province.infr.dataobject.AccRoleDO"
            useGeneratedKeys="true">
        insert into acc_role (company_id, role_name, remark,
                              `status`, is_delete, creator,
                              modifier, gmt_create, gmt_modified)
        values (#{companyId,jdbcType=BIGINT}, #{roleName,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR},
                #{status,jdbcType=TINYINT}, #{isDelete,jdbcType=TINYINT}, #{creator,jdbcType=BIGINT},
                #{modifier,jdbcType=BIGINT}, #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtModified,jdbcType=TIMESTAMP})
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id"
            parameterType="com.das.province.infr.dataobject.AccRoleDO" useGeneratedKeys="true">
        insert into acc_role
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="companyId != null">
                company_id,
            </if>
            <if test="roleName != null">
                role_name,
            </if>
            <if test="remark != null">
                remark,
            </if>
            <if test="status != null">
                `status`,
            </if>
            <if test="isDelete != null">
                is_delete,
            </if>
            <if test="creator != null">
                creator,
            </if>
            <if test="modifier != null">
                modifier,
            </if>
            <if test="gmtCreate != null">
                gmt_create,
            </if>
            <if test="gmtModified != null">
                gmt_modified,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="companyId != null">
                #{companyId,jdbcType=BIGINT},
            </if>
            <if test="roleName != null">
                #{roleName,jdbcType=VARCHAR},
            </if>
            <if test="remark != null">
                #{remark,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                #{status,jdbcType=TINYINT},
            </if>
            <if test="isDelete != null">
                #{isDelete,jdbcType=TINYINT},
            </if>
            <if test="creator != null">
                #{creator,jdbcType=BIGINT},
            </if>
            <if test="modifier != null">
                #{modifier,jdbcType=BIGINT},
            </if>
            <if test="gmtCreate != null">
                #{gmtCreate,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtModified != null">
                #{gmtModified,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.das.province.infr.dataobject.AccRoleDO">
        update acc_role
        <set>
            <if test="companyId != null">
                company_id = #{companyId,jdbcType=BIGINT},
            </if>
            <if test="roleName != null">
                role_name = #{roleName,jdbcType=VARCHAR},
            </if>
            <if test="remark != null">
                remark = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                `status` = #{status,jdbcType=TINYINT},
            </if>
            <if test="isDelete != null">
                is_delete = #{isDelete,jdbcType=TINYINT},
            </if>
            <if test="creator != null">
                creator = #{creator,jdbcType=BIGINT},
            </if>
            <if test="modifier != null">
                modifier = #{modifier,jdbcType=BIGINT},
            </if>
            <if test="gmtCreate != null">
                gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtModified != null">
                gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.das.province.infr.dataobject.AccRoleDO">
        update acc_role
        set company_id   = #{companyId,jdbcType=BIGINT},
            role_name    = #{roleName,jdbcType=VARCHAR},
            remark       = #{remark,jdbcType=VARCHAR},
            `status`     = #{status,jdbcType=TINYINT},
            is_delete    = #{isDelete,jdbcType=TINYINT},
            creator      = #{creator,jdbcType=BIGINT},
            modifier     = #{modifier,jdbcType=BIGINT},
            gmt_create   = #{gmtCreate,jdbcType=TIMESTAMP},
            gmt_modified = #{gmtModified,jdbcType=TIMESTAMP}
        where id = #{id,jdbcType=BIGINT}
    </update>

    <!--  ===========================>自定义方法===========================  -->
    <select id="selectByRoleName" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from acc_role
        where company_id = #{companyId,jdbcType=BIGINT} and role_name = #{roleName,jdbcType=VARCHAR} and is_delete = #{isDelete,jdbcType=TINYINT}
    </select>

    <select id="listByCondition" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from acc_role
        where company_id = #{companyId,jdbcType=BIGINT} and is_delete = #{isDelete,jdbcType=TINYINT}
        <if test="roleName != null and roleName != '' ">
            and role_name like concat('%', #{roleName,jdbcType=VARCHAR}, '%')
        </if>
        order by gmt_create
        desc
    </select>

    <select id="selectByRoleId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from acc_role
        where company_id = #{companyId,jdbcType=BIGINT} and id = #{roleId,jdbcType=BIGINT} and is_delete = #{isDelete,jdbcType=TINYINT}
    </select>

    <select id="listByRoleIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from acc_role
        where
        is_delete = #{isDelete,jdbcType=TINYINT}
        <if test="companyId != null ">
            and company_id = #{companyId,jdbcType=BIGINT}
        </if>
        and id in
        <foreach collection="roleIds" open="(" close=")" separator="," item="id" index="index">
            #{id}
        </foreach>
    </select>

    <update id="updateByRoleIds">
        update acc_role
        <set>
            <if test="record.roleName != null">
                role_name = #{record.roleName,jdbcType=VARCHAR},
            </if>
            <if test="record.remark != null">
                remark = #{record.remark,jdbcType=VARCHAR},
            </if>
            <if test="record.status != null">
                `status` = #{record.status,jdbcType=TINYINT},
            </if>
            <if test="record.isDelete != null">
                is_delete = #{record.isDelete,jdbcType=TINYINT},
            </if>
            <if test="record.creator != null">
                creator = #{record.creator,jdbcType=BIGINT},
            </if>
            <if test="record.modifier != null">
                modifier = #{record.modifier,jdbcType=BIGINT},
            </if>
            <if test="record.gmtCreate != null">
                gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
            </if>
            <if test="record.gmtModified != null">
                gmt_modified = #{record.gmtModified,jdbcType=TIMESTAMP},
            </if>
        </set>
        where company_id = #{companyId,jdbcType=BIGINT} and id in
        <foreach collection="roleIds" open="(" close=")" separator="," item="id" index="index">
            #{id}
        </foreach>
    </update>

    <select id="selectRoleList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from acc_role
        where company_id = #{companyId,jdbcType=BIGINT} and is_delete = 0 and `status` = 1
    </select>
</mapper>