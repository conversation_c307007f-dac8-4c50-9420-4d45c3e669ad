<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.das.province.infr.mapper.SyncOrderBaseMapper">
  <resultMap id="BaseResultMap" type="com.das.province.infr.dataobject.SyncOrderBaseDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="unique_code" jdbcType="VARCHAR" property="uniqueCode" />
    <result column="venue_name" jdbcType="VARCHAR" property="venueName" />
    <result column="order_no" jdbcType="VARCHAR" property="orderNo" />
    <result column="phone" jdbcType="VARCHAR" property="phone" />
    <result column="total_ticket_count" jdbcType="BIGINT" property="totalTicketCount" />
    <result column="total_people_count" jdbcType="BIGINT" property="totalPeopleCount" />
    <result column="order_type" jdbcType="TINYINT" property="orderType" />
    <result column="reserve_type" jdbcType="TINYINT" property="reserveType" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="reserve_date" jdbcType="TIMESTAMP" property="reserveDate" />
    <result column="time_period_start" jdbcType="TIME" property="timePeriodStart" />
    <result column="time_period_end" jdbcType="TIME" property="timePeriodEnd" />
    <result column="tourist_name" jdbcType="VARCHAR" property="touristName" />
    <result column="tourist_certificate_type" jdbcType="TINYINT" property="touristCertificateType" />
    <result column="tourist_certificate_no" jdbcType="VARCHAR" property="touristCertificateNo" />
    <result column="write_off_date" jdbcType="TIMESTAMP" property="writeOffDate" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="statistics_date" jdbcType="TIMESTAMP" property="statisticsDate" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
  </resultMap>
  <sql id="Base_Column_List">
    id, unique_code, venue_name, order_no, phone, total_ticket_count, total_people_count, 
    order_type, reserve_type, `status`, reserve_date, time_period_start, time_period_end, 
    tourist_name, tourist_certificate_type, tourist_certificate_no, write_off_date, remark, 
    statistics_date, gmt_create, gmt_modified
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from sync_order_base
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from sync_order_base
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.das.province.infr.dataobject.SyncOrderBaseDO" useGeneratedKeys="true">
    insert into sync_order_base (unique_code, venue_name, order_no, 
      phone, total_ticket_count, total_people_count, 
      order_type, reserve_type, `status`, 
      reserve_date, time_period_start, time_period_end, 
      tourist_name, tourist_certificate_type, tourist_certificate_no, 
      write_off_date, remark, statistics_date, 
      gmt_create, gmt_modified)
    values (#{uniqueCode,jdbcType=VARCHAR}, #{venueName,jdbcType=VARCHAR}, #{orderNo,jdbcType=VARCHAR}, 
      #{phone,jdbcType=VARCHAR}, #{totalTicketCount,jdbcType=BIGINT}, #{totalPeopleCount,jdbcType=BIGINT}, 
      #{orderType,jdbcType=TINYINT}, #{reserveType,jdbcType=TINYINT}, #{status,jdbcType=TINYINT}, 
      #{reserveDate,jdbcType=TIMESTAMP}, #{timePeriodStart,jdbcType=TIME}, #{timePeriodEnd,jdbcType=TIME}, 
      #{touristName,jdbcType=VARCHAR}, #{touristCertificateType,jdbcType=TINYINT}, #{touristCertificateNo,jdbcType=VARCHAR}, 
      #{writeOffDate,jdbcType=TIMESTAMP}, #{remark,jdbcType=VARCHAR}, #{statisticsDate,jdbcType=TIMESTAMP}, 
      #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtModified,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.das.province.infr.dataobject.SyncOrderBaseDO" useGeneratedKeys="true">
    insert into sync_order_base
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="uniqueCode != null">
        unique_code,
      </if>
      <if test="venueName != null">
        venue_name,
      </if>
      <if test="orderNo != null">
        order_no,
      </if>
      <if test="phone != null">
        phone,
      </if>
      <if test="totalTicketCount != null">
        total_ticket_count,
      </if>
      <if test="totalPeopleCount != null">
        total_people_count,
      </if>
      <if test="orderType != null">
        order_type,
      </if>
      <if test="reserveType != null">
        reserve_type,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="reserveDate != null">
        reserve_date,
      </if>
      <if test="timePeriodStart != null">
        time_period_start,
      </if>
      <if test="timePeriodEnd != null">
        time_period_end,
      </if>
      <if test="touristName != null">
        tourist_name,
      </if>
      <if test="touristCertificateType != null">
        tourist_certificate_type,
      </if>
      <if test="touristCertificateNo != null">
        tourist_certificate_no,
      </if>
      <if test="writeOffDate != null">
        write_off_date,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="statisticsDate != null">
        statistics_date,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="uniqueCode != null">
        #{uniqueCode,jdbcType=VARCHAR},
      </if>
      <if test="venueName != null">
        #{venueName,jdbcType=VARCHAR},
      </if>
      <if test="orderNo != null">
        #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="phone != null">
        #{phone,jdbcType=VARCHAR},
      </if>
      <if test="totalTicketCount != null">
        #{totalTicketCount,jdbcType=BIGINT},
      </if>
      <if test="totalPeopleCount != null">
        #{totalPeopleCount,jdbcType=BIGINT},
      </if>
      <if test="orderType != null">
        #{orderType,jdbcType=TINYINT},
      </if>
      <if test="reserveType != null">
        #{reserveType,jdbcType=TINYINT},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="reserveDate != null">
        #{reserveDate,jdbcType=TIMESTAMP},
      </if>
      <if test="timePeriodStart != null">
        #{timePeriodStart,jdbcType=TIME},
      </if>
      <if test="timePeriodEnd != null">
        #{timePeriodEnd,jdbcType=TIME},
      </if>
      <if test="touristName != null">
        #{touristName,jdbcType=VARCHAR},
      </if>
      <if test="touristCertificateType != null">
        #{touristCertificateType,jdbcType=TINYINT},
      </if>
      <if test="touristCertificateNo != null">
        #{touristCertificateNo,jdbcType=VARCHAR},
      </if>
      <if test="writeOffDate != null">
        #{writeOffDate,jdbcType=TIMESTAMP},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="statisticsDate != null">
        #{statisticsDate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>

  <insert id="batchInsert">
    insert into sync_order_base (unique_code, venue_name, order_no,
    phone, total_ticket_count, total_people_count,
    order_type, reserve_type, `status`,
    reserve_date, time_period_start, time_period_end,
    tourist_name, tourist_certificate_type, tourist_certificate_no,
    write_off_date, remark, statistics_date,
    gmt_create, gmt_modified) values
    <foreach collection="orderList" item="t" index="index" separator=",">
      (#{t.uniqueCode,jdbcType=VARCHAR}, #{t.venueName,jdbcType=VARCHAR}, #{t.orderNo,jdbcType=VARCHAR},
      #{t.phone,jdbcType=VARCHAR}, #{t.totalTicketCount,jdbcType=BIGINT}, #{t.totalPeopleCount,jdbcType=BIGINT},
      #{t.orderType,jdbcType=TINYINT}, #{t.reserveType,jdbcType=TINYINT}, #{t.status,jdbcType=TINYINT},
      #{t.reserveDate,jdbcType=TIMESTAMP}, #{t.timePeriodStart,jdbcType=TIME}, #{t.timePeriodEnd,jdbcType=TIME},
      #{t.touristName,jdbcType=VARCHAR}, #{t.touristCertificateType,jdbcType=TINYINT}, #{t.touristCertificateNo,jdbcType=VARCHAR},
      #{t.writeOffDate,jdbcType=TIMESTAMP}, #{t.remark,jdbcType=VARCHAR}, #{t.statisticsDate,jdbcType=TIMESTAMP},
      #{t.gmtCreate,jdbcType=TIMESTAMP}, #{t.gmtModified,jdbcType=TIMESTAMP})
    </foreach>
  </insert>

  <update id="updateByPrimaryKeySelective" parameterType="com.das.province.infr.dataobject.SyncOrderBaseDO">
    update sync_order_base
    <set>
      <if test="uniqueCode != null">
        unique_code = #{uniqueCode,jdbcType=VARCHAR},
      </if>
      <if test="venueName != null">
        venue_name = #{venueName,jdbcType=VARCHAR},
      </if>
      <if test="orderNo != null">
        order_no = #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="phone != null">
        phone = #{phone,jdbcType=VARCHAR},
      </if>
      <if test="totalTicketCount != null">
        total_ticket_count = #{totalTicketCount,jdbcType=BIGINT},
      </if>
      <if test="totalPeopleCount != null">
        total_people_count = #{totalPeopleCount,jdbcType=BIGINT},
      </if>
      <if test="orderType != null">
        order_type = #{orderType,jdbcType=TINYINT},
      </if>
      <if test="reserveType != null">
        reserve_type = #{reserveType,jdbcType=TINYINT},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=TINYINT},
      </if>
      <if test="reserveDate != null">
        reserve_date = #{reserveDate,jdbcType=TIMESTAMP},
      </if>
      <if test="timePeriodStart != null">
        time_period_start = #{timePeriodStart,jdbcType=TIME},
      </if>
      <if test="timePeriodEnd != null">
        time_period_end = #{timePeriodEnd,jdbcType=TIME},
      </if>
      <if test="touristName != null">
        tourist_name = #{touristName,jdbcType=VARCHAR},
      </if>
      <if test="touristCertificateType != null">
        tourist_certificate_type = #{touristCertificateType,jdbcType=TINYINT},
      </if>
      <if test="touristCertificateNo != null">
        tourist_certificate_no = #{touristCertificateNo,jdbcType=VARCHAR},
      </if>
      <if test="writeOffDate != null">
        write_off_date = #{writeOffDate,jdbcType=TIMESTAMP},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="statisticsDate != null">
        statistics_date = #{statisticsDate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.das.province.infr.dataobject.SyncOrderBaseDO">
    update sync_order_base
    set unique_code = #{uniqueCode,jdbcType=VARCHAR},
      venue_name = #{venueName,jdbcType=VARCHAR},
      order_no = #{orderNo,jdbcType=VARCHAR},
      phone = #{phone,jdbcType=VARCHAR},
      total_ticket_count = #{totalTicketCount,jdbcType=BIGINT},
      total_people_count = #{totalPeopleCount,jdbcType=BIGINT},
      order_type = #{orderType,jdbcType=TINYINT},
      reserve_type = #{reserveType,jdbcType=TINYINT},
      `status` = #{status,jdbcType=TINYINT},
      reserve_date = #{reserveDate,jdbcType=TIMESTAMP},
      time_period_start = #{timePeriodStart,jdbcType=TIME},
      time_period_end = #{timePeriodEnd,jdbcType=TIME},
      tourist_name = #{touristName,jdbcType=VARCHAR},
      tourist_certificate_type = #{touristCertificateType,jdbcType=TINYINT},
      tourist_certificate_no = #{touristCertificateNo,jdbcType=VARCHAR},
      write_off_date = #{writeOffDate,jdbcType=TIMESTAMP},
      remark = #{remark,jdbcType=VARCHAR},
      statistics_date = #{statisticsDate,jdbcType=TIMESTAMP},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_modified = #{gmtModified,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <select id="selectUniqueCodeAndOrderTypeAndStatisticsDate" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from sync_order_base
    where unique_code = #{uniqueCode,jdbcType=VARCHAR} and order_type = #{orderType,jdbcType=TINYINT}
    and statistics_date = #{statisticsDate,jdbcType=TIMESTAMP}
  </select>

  <select id="selectList" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from sync_order_base
  </select>
  <select id="listUniqueCode" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from sync_order_base
    where unique_code = #{uniqueCode,jdbcType=VARCHAR}
  </select>

  <select id="listOrderNoUniqueCode" resultType="java.lang.String">
    select
      order_no
    from sync_order_base
    where unique_code = #{uniqueCode,jdbcType=VARCHAR}
  </select>


  <select id="listByReserveDate" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from sync_order_base
    where 1=1
    <if test="status != null">
      and `status` = #{status,jdbcType=TINYINT}
    </if>
    <if test="orderType != null">
      and order_type = #{orderType,jdbcType=TINYINT}
    </if>
    <if test="reserveDateStart != null and reserveDateEnd != null">
      and reserve_date &gt;= #{reserveDateStart,jdbcType=TIMESTAMP} and reserve_date &lt;= #{reserveDateEnd,jdbcType=TIMESTAMP}
    </if>
    <if test="uniqueCodes != null and uniqueCodes.size()>0">
      and unique_code in
      <foreach collection="uniqueCodes" open="(" close=")" separator="," item="id" index="index">
        #{id}
      </foreach>
    </if>
  </select>

  <select id="countByReserveDate" resultType="java.lang.Integer">
    select
    IFNULL(sum(total_people_count),0)  num
    from sync_order_base
    where 1=1
    <if test="status != null">
      and `status` = #{status,jdbcType=TINYINT}
    </if>
    <if test="orderType != null">
      and order_type = #{orderType,jdbcType=TINYINT}
    </if>
    <if test="reserveDateStart != null and reserveDateEnd != null">
      and reserve_date &gt;= #{reserveDateStart,jdbcType=TIMESTAMP} and reserve_date &lt;= #{reserveDateEnd,jdbcType=TIMESTAMP}
    </if>
    <if test="uniqueCodes != null and uniqueCodes.size()>0">
      and unique_code in
      <foreach collection="uniqueCodes" open="(" close=")" separator="," item="id" index="index">
        #{id}
      </foreach>
    </if>
  </select>

  <select id="listGroupByProvince" resultType="com.das.province.infr.dataobject.ListGroupByProvinceDO">
    select
    left(tourist_certificate_no,3) provinceCode,
    count(tourist_certificate_no) visitorNum
    from sync_order_base
    where 1=1
    <if test="status != null">
      and `status` = #{status,jdbcType=TINYINT}
    </if>
    <if test="reserveDateStart != null ">
      and reserve_date &gt;= #{reserveDateStart,jdbcType=TIMESTAMP}
    </if>
    <if test="reserveDateEnd != null ">
      and reserve_date &lt;= #{reserveDateEnd,jdbcType=TIMESTAMP}
    </if>
    <if test="uniqueCodes != null and uniqueCodes.size()>0">
      and unique_code in
      <foreach collection="uniqueCodes" open="(" close=")" separator="," item="id" index="index">
        #{id}
      </foreach>
    </if>
    group by left(tourist_certificate_no,3)
  </select>


  <select id="groupOrderBase" resultType="com.das.province.infr.dataobjectexpand.GroupOrderBaseDO">
    SELECT
      unique_code uniqueCode,
      STR_TO_DATE( reserve_date, '%Y-%m-%d' ) reserveDate,
      sum( total_people_count ) totalPeopleCount
    FROM
      sync_order_base
    GROUP BY
      unique_code,
      STR_TO_DATE(
              reserve_date,
              '%Y-%m-%d')
  </select>

  <delete id="deleteWmByTime">
    delete from sync_order_base
    where unique_code in ('1684468846498549760','1678320746423783424')
    and reserve_date &gt;= #{startTime,jdbcType=TIMESTAMP}
    and reserve_date &lt;= #{endTime,jdbcType=TIMESTAMP}
  </delete>

  <delete id="deleteLxByTime">
    delete from sync_order_base
    where unique_code = '1702249354724773888'
      and reserve_date &gt;= #{startTime,jdbcType=TIMESTAMP}
      and reserve_date &lt;= #{endTime,jdbcType=TIMESTAMP}
  </delete>

  <delete id="deleteByUniqueCode">
    delete from sync_order_base
    where unique_code = #{uniqueCode,jdbcType=VARCHAR}
    <if test="writeOffDateStart != null ">
      and write_off_date &gt;= #{writeOffDateStart,jdbcType=TIMESTAMP}
    </if>
    <if test="writeOffDateEnd != null ">
      and write_off_date &lt;= #{writeOffDateEnd,jdbcType=TIMESTAMP}
    </if>
  </delete>
</mapper>