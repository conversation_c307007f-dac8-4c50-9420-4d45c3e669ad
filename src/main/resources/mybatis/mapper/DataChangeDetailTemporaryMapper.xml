<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.das.province.infr.mapper.DataChangeDetailTemporaryMapper">
  <resultMap id="BaseResultMap" type="com.das.province.infr.dataobject.DataChangeDetailTemporaryDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="museum_id" jdbcType="VARCHAR" property="museumId" />
    <result column="change_data" jdbcType="INTEGER" property="changeData" />
    <result column="up_down" jdbcType="TINYINT" property="upDown" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
  </resultMap>
  <sql id="Base_Column_List">
    id, museum_id, change_data, up_down, gmt_create, gmt_modified
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from data_change_detail_temporary
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from data_change_detail_temporary
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.das.province.infr.dataobject.DataChangeDetailTemporaryDO" useGeneratedKeys="true">
    insert into data_change_detail_temporary (museum_id, change_data, up_down, 
      gmt_create, gmt_modified)
    values (#{museumId,jdbcType=VARCHAR}, #{changeData,jdbcType=INTEGER}, #{upDown,jdbcType=TINYINT},
      #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtModified,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.das.province.infr.dataobject.DataChangeDetailTemporaryDO" useGeneratedKeys="true">
    insert into data_change_detail_temporary
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="museumId != null">
        museum_id,
      </if>
      <if test="changeData != null">
        change_data,
      </if>
      <if test="upDown != null">
        up_down,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="museumId != null">
        #{museumId,jdbcType=VARCHAR},
      </if>
      <if test="changeData != null">
        #{changeData,jdbcType=INTEGER},
      </if>
      <if test="upDown != null">
        #{upDown,jdbcType=TINYINT},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.das.province.infr.dataobject.DataChangeDetailTemporaryDO">
    update data_change_detail_temporary
    <set>
      <if test="museumId != null">
        museum_id = #{museumId,jdbcType=VARCHAR},
      </if>
      <if test="changeData != null">
        change_data = #{changeData,jdbcType=INTEGER},
      </if>
      <if test="upDown != null">
        up_down = #{upDown,jdbcType=TINYINT},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.das.province.infr.dataobject.DataChangeDetailTemporaryDO">
    update data_change_detail_temporary
    set museum_id = #{museumId,jdbcType=VARCHAR},
      change_data = #{changeData,jdbcType=INTEGER},
      up_down = #{upDown,jdbcType=TINYINT},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_modified = #{gmtModified,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="selectList" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from data_change_detail_temporary
  </select>

  <select id="deleteAll">
    delete from data_change_detail_temporary
  </select>

</mapper>