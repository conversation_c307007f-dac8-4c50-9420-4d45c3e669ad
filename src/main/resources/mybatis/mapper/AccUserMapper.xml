<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.das.province.infr.mapper.AccUserMapper">
    <resultMap id="BaseResultMap" type="com.das.province.infr.dataobject.AccUserDO">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="company_id" jdbcType="BIGINT" property="companyId"/>
        <result column="user_name" jdbcType="VARCHAR" property="userName"/>
        <result column="real_name" jdbcType="VARCHAR" property="realName"/>
        <result column="login_account" jdbcType="VARCHAR" property="loginAccount"/>
        <result column="password" jdbcType="VARCHAR" property="password"/>
        <result column="source_password" jdbcType="VARCHAR" property="sourcePassword"/>
        <result column="avatar" jdbcType="BIGINT" property="avatar"/>
        <result column="phone" jdbcType="VARCHAR" property="phone"/>
        <result column="email" jdbcType="VARCHAR" property="email"/>
        <result column="department_id" jdbcType="BIGINT" property="departmentId"/>
        <result column="position_id" jdbcType="BIGINT" property="positionId"/>
        <result column="position_name" jdbcType="VARCHAR" property="positionName"/>
        <result column="post_code" jdbcType="VARCHAR" property="postCode"/>
        <result column="leader_id" jdbcType="BIGINT" property="leaderId"/>
        <result column="leader_level" jdbcType="TINYINT" property="leaderLevel"/>
        <result column="user_type" jdbcType="VARCHAR" property="userType"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="account_status" jdbcType="TINYINT" property="accountStatus"/>
        <result column="is_delete" jdbcType="TINYINT" property="isDelete"/>
        <result column="creator" jdbcType="BIGINT" property="creator"/>
        <result column="modifier" jdbcType="BIGINT" property="modifier"/>
        <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate"/>
        <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified"/>
    </resultMap>
    <sql id="Base_Column_List">
        id
        , company_id, user_name, real_name, login_account, `password`, source_password,
    avatar, phone, email, department_id, position_id, position_name, post_code, leader_id, 
    leader_level, user_type, remark, account_status, is_delete, creator, modifier, gmt_create, 
    gmt_modified
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from acc_user
        where id = #{id,jdbcType=BIGINT}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from acc_user
        where id = #{id,jdbcType=BIGINT}
    </delete>
    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.das.province.infr.dataobject.AccUserDO"
            useGeneratedKeys="true">
        insert into acc_user (company_id, user_name, real_name,
                              login_account, `password`, source_password,
                              avatar, phone, email,
                              department_id, position_id, position_name,
                              post_code, leader_id, leader_level,
                              user_type, remark, account_status,
                              is_delete, creator, modifier,
                              gmt_create, gmt_modified)
        values (#{companyId,jdbcType=BIGINT}, #{userName,jdbcType=VARCHAR}, #{realName,jdbcType=VARCHAR},
                #{loginAccount,jdbcType=VARCHAR}, #{password,jdbcType=VARCHAR}, #{sourcePassword,jdbcType=VARCHAR},
                #{avatar,jdbcType=BIGINT}, #{phone,jdbcType=VARCHAR}, #{email,jdbcType=VARCHAR},
                #{departmentId,jdbcType=BIGINT}, #{positionId,jdbcType=BIGINT}, #{positionName,jdbcType=VARCHAR},
                #{postCode,jdbcType=VARCHAR}, #{leaderId,jdbcType=BIGINT}, #{leaderLevel,jdbcType=TINYINT},
                #{userType,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR}, #{accountStatus,jdbcType=TINYINT},
                #{isDelete,jdbcType=TINYINT}, #{creator,jdbcType=BIGINT}, #{modifier,jdbcType=BIGINT},
                #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtModified,jdbcType=TIMESTAMP})
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id"
            parameterType="com.das.province.infr.dataobject.AccUserDO" useGeneratedKeys="true">
        insert into acc_user
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="companyId != null">
                company_id,
            </if>
            <if test="userName != null">
                user_name,
            </if>
            <if test="realName != null">
                real_name,
            </if>
            <if test="loginAccount != null">
                login_account,
            </if>
            <if test="password != null">
                `password`,
            </if>
            <if test="sourcePassword != null">
                source_password,
            </if>
            <if test="avatar != null">
                avatar,
            </if>
            <if test="phone != null">
                phone,
            </if>
            <if test="email != null">
                email,
            </if>
            <if test="departmentId != null">
                department_id,
            </if>
            <if test="positionId != null">
                position_id,
            </if>
            <if test="positionName != null">
                position_name,
            </if>
            <if test="postCode != null">
                post_code,
            </if>
            <if test="leaderId != null">
                leader_id,
            </if>
            <if test="leaderLevel != null">
                leader_level,
            </if>
            <if test="userType != null">
                user_type,
            </if>
            <if test="remark != null">
                remark,
            </if>
            <if test="accountStatus != null">
                account_status,
            </if>
            <if test="isDelete != null">
                is_delete,
            </if>
            <if test="creator != null">
                creator,
            </if>
            <if test="modifier != null">
                modifier,
            </if>
            <if test="gmtCreate != null">
                gmt_create,
            </if>
            <if test="gmtModified != null">
                gmt_modified,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="companyId != null">
                #{companyId,jdbcType=BIGINT},
            </if>
            <if test="userName != null">
                #{userName,jdbcType=VARCHAR},
            </if>
            <if test="realName != null">
                #{realName,jdbcType=VARCHAR},
            </if>
            <if test="loginAccount != null">
                #{loginAccount,jdbcType=VARCHAR},
            </if>
            <if test="password != null">
                #{password,jdbcType=VARCHAR},
            </if>
            <if test="sourcePassword != null">
                #{sourcePassword,jdbcType=VARCHAR},
            </if>
            <if test="avatar != null">
                #{avatar,jdbcType=BIGINT},
            </if>
            <if test="phone != null">
                #{phone,jdbcType=VARCHAR},
            </if>
            <if test="email != null">
                #{email,jdbcType=VARCHAR},
            </if>
            <if test="departmentId != null">
                #{departmentId,jdbcType=BIGINT},
            </if>
            <if test="positionId != null">
                #{positionId,jdbcType=BIGINT},
            </if>
            <if test="positionName != null">
                #{positionName,jdbcType=VARCHAR},
            </if>
            <if test="postCode != null">
                #{postCode,jdbcType=VARCHAR},
            </if>
            <if test="leaderId != null">
                #{leaderId,jdbcType=BIGINT},
            </if>
            <if test="leaderLevel != null">
                #{leaderLevel,jdbcType=TINYINT},
            </if>
            <if test="userType != null">
                #{userType,jdbcType=VARCHAR},
            </if>
            <if test="remark != null">
                #{remark,jdbcType=VARCHAR},
            </if>
            <if test="accountStatus != null">
                #{accountStatus,jdbcType=TINYINT},
            </if>
            <if test="isDelete != null">
                #{isDelete,jdbcType=TINYINT},
            </if>
            <if test="creator != null">
                #{creator,jdbcType=BIGINT},
            </if>
            <if test="modifier != null">
                #{modifier,jdbcType=BIGINT},
            </if>
            <if test="gmtCreate != null">
                #{gmtCreate,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtModified != null">
                #{gmtModified,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.das.province.infr.dataobject.AccUserDO">
        update acc_user
        <set>
            <if test="companyId != null">
                company_id = #{companyId,jdbcType=BIGINT},
            </if>
            <if test="userName != null">
                user_name = #{userName,jdbcType=VARCHAR},
            </if>
            <if test="realName != null">
                real_name = #{realName,jdbcType=VARCHAR},
            </if>
            <if test="loginAccount != null">
                login_account = #{loginAccount,jdbcType=VARCHAR},
            </if>
            <if test="password != null">
                `password` = #{password,jdbcType=VARCHAR},
            </if>
            <if test="sourcePassword != null">
                source_password = #{sourcePassword,jdbcType=VARCHAR},
            </if>
            <if test="avatar != null">
                avatar = #{avatar,jdbcType=BIGINT},
            </if>
            <if test="phone != null">
                phone = #{phone,jdbcType=VARCHAR},
            </if>
            <if test="email != null">
                email = #{email,jdbcType=VARCHAR},
            </if>
            <if test="departmentId != null">
                department_id = #{departmentId,jdbcType=BIGINT},
            </if>
            <if test="positionId != null">
                position_id = #{positionId,jdbcType=BIGINT},
            </if>
            <if test="positionName != null">
                position_name = #{positionName,jdbcType=VARCHAR},
            </if>
            <if test="postCode != null">
                post_code = #{postCode,jdbcType=VARCHAR},
            </if>
            <if test="leaderId != null">
                leader_id = #{leaderId,jdbcType=BIGINT},
            </if>
            <if test="leaderLevel != null">
                leader_level = #{leaderLevel,jdbcType=TINYINT},
            </if>
            <if test="userType != null">
                user_type = #{userType,jdbcType=VARCHAR},
            </if>
            <if test="remark != null">
                remark = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="accountStatus != null">
                account_status = #{accountStatus,jdbcType=TINYINT},
            </if>
            <if test="isDelete != null">
                is_delete = #{isDelete,jdbcType=TINYINT},
            </if>
            <if test="creator != null">
                creator = #{creator,jdbcType=BIGINT},
            </if>
            <if test="modifier != null">
                modifier = #{modifier,jdbcType=BIGINT},
            </if>
            <if test="gmtCreate != null">
                gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtModified != null">
                gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.das.province.infr.dataobject.AccUserDO">
        update acc_user
        set company_id      = #{companyId,jdbcType=BIGINT},
            user_name       = #{userName,jdbcType=VARCHAR},
            real_name       = #{realName,jdbcType=VARCHAR},
            login_account   = #{loginAccount,jdbcType=VARCHAR},
            `password`      = #{password,jdbcType=VARCHAR},
            source_password = #{sourcePassword,jdbcType=VARCHAR},
            avatar          = #{avatar,jdbcType=BIGINT},
            phone           = #{phone,jdbcType=VARCHAR},
            email           = #{email,jdbcType=VARCHAR},
            department_id   = #{departmentId,jdbcType=BIGINT},
            position_id     = #{positionId,jdbcType=BIGINT},
            position_name   = #{positionName,jdbcType=VARCHAR},
            post_code       = #{postCode,jdbcType=VARCHAR},
            leader_id       = #{leaderId,jdbcType=BIGINT},
            leader_level    = #{leaderLevel,jdbcType=TINYINT},
            user_type       = #{userType,jdbcType=VARCHAR},
            remark          = #{remark,jdbcType=VARCHAR},
            account_status  = #{accountStatus,jdbcType=TINYINT},
            is_delete       = #{isDelete,jdbcType=TINYINT},
            creator         = #{creator,jdbcType=BIGINT},
            modifier        = #{modifier,jdbcType=BIGINT},
            gmt_create      = #{gmtCreate,jdbcType=TIMESTAMP},
            gmt_modified    = #{gmtModified,jdbcType=TIMESTAMP}
        where id = #{id,jdbcType=BIGINT}
        and company_id = #{companyId,jdbcType=BIGINT}
    </update>

    <update id="updateByIdAndCompanyId" parameterType="com.das.province.infr.dataobject.AccUserDO">
        update acc_user
        <set>
            <if test="password != null and password != '' ">
                `password` = #{password,jdbcType=VARCHAR},
            </if>
            <if test="sourcePassword != null and sourcePassword != '' ">
                source_password = #{sourcePassword,jdbcType=VARCHAR},
            </if>
            <if test="avatar != null">
                avatar = #{avatar,jdbcType=BIGINT},
            </if>
            <if test="modifier != null">
                modifier = #{modifier,jdbcType=BIGINT},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
        and company_id = #{companyId,jdbcType=BIGINT}
    </update>

    <!--  ===========================>自定义方法===========================  -->
    <delete id="deleteByUserId">
      delete
      from acc_user
      where company_id = #{companyId,jdbcType=BIGINT} and id = #{id,jdbcType=BIGINT}
    </delete>

    <select id="selectByUserIdAndIsDelete" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from acc_user
        where id = #{userId,jdbcType=BIGINT} and is_delete = #{isDelete,jdbcType=TINYINT}
    </select>

    <select id="selectByLoginAccountAndIsDelete" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from acc_user
        where login_account = #{loginAccount,jdbcType=VARCHAR} and is_delete = #{isDelete,jdbcType=TINYINT}
    </select>

    <select id="selectByUserId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from acc_user
        where company_id = #{companyId,jdbcType=BIGINT} and id = #{userId,jdbcType=BIGINT}
    </select>

    <select id="listByUserId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from acc_user
        where company_id = #{companyId,jdbcType=BIGINT} and id in
        <foreach collection="userIds" open="(" close=")" separator="," item="id" index="index">
            #{id}
        </foreach>
    </select>

    <select id="listNotInUserIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from acc_user
        where company_id = #{companyId,jdbcType=BIGINT}
        and is_delete = #{isDelete,jdbcType=TINYINT}
        <if test="userIds != null and userIds.size > 0 ">
            and id not in
            <foreach collection="userIds" open="(" close=")" separator="," item="id" index="index">
                #{id}
            </foreach>
        </if>
    </select>

    <select id="selectByLoginAccount" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from acc_user
        where company_id = #{companyId,jdbcType=BIGINT} and login_account = #{loginAccount,jdbcType=VARCHAR}
        and user_type = #{userType,jdbcType=VARCHAR}
    </select>

    <select id="selectByLoginAccountAndPassword" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from acc_user
        where login_account = #{loginAccount,jdbcType=VARCHAR} and `password` = #{password,jdbcType=VARCHAR}
        and account_status = #{accountStatus,jdbcType=TINYINT} and is_delete = #{isDelete,jdbcType=TINYINT}
        and user_type = #{userType,jdbcType=VARCHAR}
    </select>

    <select id="listByCondition" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from acc_user
        where company_id = #{companyId,jdbcType=BIGINT} and is_delete = #{isDelete,jdbcType=TINYINT}
        <if test="departmentIds != null">
            and department_id in
            <foreach collection="departmentIds" open="(" close=")" separator="," item="departmentId" index="index">
                #{departmentId}
            </foreach>
        </if>
        <if test="accountStatus != null">
            and account_status = #{accountStatus,jdbcType=TINYINT}
        </if>
        <if test="queryContent != null">
            and (user_name like concat('%', #{queryContent,jdbcType=VARCHAR}, '%') or phone like concat('%', #{queryContent,jdbcType=VARCHAR}, '%'))
        </if>
        <if test="sortBy != null and sortBy != '' ">
            order by ${sortBy}
        </if>
    </select>

    <select id="listByConditionNoPage" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from acc_user
        where company_id = #{companyId,jdbcType=BIGINT}
        and is_delete = #{isDelete,jdbcType=TINYINT}
        <if test="realName != null and realName != '' ">
            and real_name like concat('%', #{realName,jdbcType=VARCHAR}, '%')
        </if>
        order by department_id
    </select>

    <select id="listByPositionId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from acc_user
        where company_id = #{companyId,jdbcType=BIGINT} and position_id = #{positionId,jdbcType=BIGINT} and is_delete = #{isDelete,jdbcType=TINYINT}
    </select>

    <select id="listByPositionIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from acc_user
        where company_id = #{companyId,jdbcType=BIGINT} and position_id in
        <foreach collection="positionIds" open="(" close=")" separator="," item="id" index="index">
            #{id}
        </foreach>
        and is_delete = #{isDelete,jdbcType=TINYINT}
    </select>

    <update id="updateByUserIds">
        update acc_user
        <set>
            <if test="record.userName != null">
                user_name = #{record.userName,jdbcType=VARCHAR},
            </if>
            <if test="record.realName != null">
                real_name = #{record.realName,jdbcType=VARCHAR},
            </if>
            <if test="record.loginAccount != null">
                login_account = #{record.loginAccount,jdbcType=VARCHAR},
            </if>
            <if test="record.password != null">
                `password` = #{record.password,jdbcType=VARCHAR},
            </if>
            <if test="record.sourcePassword != null">
                source_password = #{record.sourcePassword,jdbcType=VARCHAR},
            </if>
            <if test="record.avatar != null">
                avatar = #{record.avatar,jdbcType=BIGINT},
            </if>
            <if test="record.phone != null">
                phone = #{record.phone,jdbcType=VARCHAR},
            </if>
            <if test="record.email != null">
                email = #{record.email,jdbcType=VARCHAR},
            </if>
            <if test="record.departmentId != null">
                department_id = #{record.departmentId,jdbcType=BIGINT},
            </if>
            <if test="record.positionId != null">
                position_id = #{record.positionId,jdbcType=BIGINT},
            </if>
            <if test="record.postCode != null">
                post_code = #{record.postCode,jdbcType=VARCHAR},
            </if>
            <if test="record.remark != null">
                remark = #{record.remark,jdbcType=VARCHAR},
            </if>
            <if test="record.accountStatus != null">
                account_status = #{record.accountStatus,jdbcType=TINYINT},
            </if>
            <if test="record.isDelete != null">
                is_delete = #{record.isDelete,jdbcType=TINYINT},
            </if>
            <if test="record.creator != null">
                creator = #{record.creator,jdbcType=BIGINT},
            </if>
            <if test="record.modifier != null">
                modifier = #{record.modifier,jdbcType=BIGINT},
            </if>
            <if test="record.gmtCreate != null">
                gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
            </if>
            <if test="record.gmtModified != null">
                gmt_modified = #{record.gmtModified,jdbcType=TIMESTAMP},
            </if>
        </set>
        where company_id = #{record.companyId,jdbcType=BIGINT} and id in
        <foreach collection="userIds" open="(" close=")" separator="," item="id" index="index">
            #{id}
        </foreach>
    </update>

    <select id="listByIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from acc_user
        where company_id = #{companyId,jdbcType=BIGINT} and id in
        <foreach collection="ids" open="(" close=")" separator="," item="id" index="index">
            #{id}
        </foreach>
    </select>

    <select id="listByDepartmentIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from acc_user
        where company_id = #{companyId,jdbcType=BIGINT} and department_id in
        <foreach collection="departmentIds" open="(" close=")" separator="," item="id" index="index">
            #{id}
        </foreach>
        and is_delete = #{isDelete,jdbcType=TINYINT}
    </select>

    <select id="listByIsDelete" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from acc_user
        where is_delete = #{isDelete,jdbcType=TINYINT}
        <if test="userName != null">
            and user_name like concat('%', #{userName,jdbcType=VARCHAR}, '%')
        </if>
    </select>
</mapper>