<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.das.province.infr.mapper.DataChangeInfoMapper">
  <resultMap id="BaseResultMap" type="com.das.province.infr.dataobject.DataChangeInfoDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="change_type" jdbcType="TINYINT" property="changeType" />
    <result column="change_description" jdbcType="VARCHAR" property="changeDescription" />
    <result column="happen_time" jdbcType="TIMESTAMP" property="happenTime" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
  </resultMap>
  <sql id="Base_Column_List">
    id, change_type, change_description, happen_time, gmt_create, gmt_modified
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from data_change_info
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from data_change_info
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.das.province.infr.dataobject.DataChangeInfoDO" useGeneratedKeys="true">
    insert into data_change_info (change_type, change_description, happen_time, 
      gmt_create, gmt_modified)
    values (#{changeType,jdbcType=TINYINT}, #{changeDescription,jdbcType=VARCHAR}, #{happenTime,jdbcType=TIMESTAMP}, 
      #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtModified,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.das.province.infr.dataobject.DataChangeInfoDO" useGeneratedKeys="true">
    insert into data_change_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="changeType != null">
        change_type,
      </if>
      <if test="changeDescription != null">
        change_description,
      </if>
      <if test="happenTime != null">
        happen_time,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="changeType != null">
        #{changeType,jdbcType=TINYINT},
      </if>
      <if test="changeDescription != null">
        #{changeDescription,jdbcType=VARCHAR},
      </if>
      <if test="happenTime != null">
        #{happenTime,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.das.province.infr.dataobject.DataChangeInfoDO">
    update data_change_info
    <set>
      <if test="changeType != null">
        change_type = #{changeType,jdbcType=TINYINT},
      </if>
      <if test="changeDescription != null">
        change_description = #{changeDescription,jdbcType=VARCHAR},
      </if>
      <if test="happenTime != null">
        happen_time = #{happenTime,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.das.province.infr.dataobject.DataChangeInfoDO">
    update data_change_info
    set change_type = #{changeType,jdbcType=TINYINT},
      change_description = #{changeDescription,jdbcType=VARCHAR},
      happen_time = #{happenTime,jdbcType=TIMESTAMP},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_modified = #{gmtModified,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="listByCondition" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from data_change_info
    where 1 = 1
    <if test="changeType != null ">
      and change_type = #{changeType,jdbcType=TINYINT}
    </if>
    <if test="startTime != null and startTime != '' ">
      and happen_time >= #{startTime,jdbcType=TIMESTAMP}
    </if>
    <if test="endTime != null and endTime != '' ">
      and happen_time <![CDATA[ <= ]]> #{endTime,jdbcType=TIMESTAMP}
    </if>
    order by ${sortBy}
  </select>
</mapper>