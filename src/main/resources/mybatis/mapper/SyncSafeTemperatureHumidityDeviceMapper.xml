<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.das.province.infr.mapper.SyncSafeTemperatureHumidityDeviceMapper">
  <resultMap id="BaseResultMap" type="com.das.province.infr.dataobject.SyncSafeTemperatureHumidityDeviceDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="unique_code" jdbcType="VARCHAR" property="uniqueCode" />
    <result column="device_code" jdbcType="VARCHAR" property="deviceCode" />
    <result column="region_name" jdbcType="TINYINT" property="regionName" />
    <result column="device_address" jdbcType="VARCHAR" property="deviceAddress" />
    <result column="min_temperature" jdbcType="VARCHAR" property="minTemperature" />
    <result column="max_temperature" jdbcType="VARCHAR" property="maxTemperature" />
    <result column="min_humidity" jdbcType="VARCHAR" property="minHumidity" />
    <result column="max_humidity" jdbcType="VARCHAR" property="maxHumidity" />
    <result column="current_temperature" jdbcType="VARCHAR" property="currentTemperature" />
    <result column="current_humidity" jdbcType="VARCHAR" property="currentHumidity" />
    <result column="report_time" jdbcType="TIMESTAMP" property="reportTime" />
    <result column="creator" jdbcType="BIGINT" property="creator" />
    <result column="modifier" jdbcType="BIGINT" property="modifier" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
  </resultMap>
  <sql id="Base_Column_List">
    id, unique_code, device_code, region_name, device_address, min_temperature, max_temperature, 
    min_humidity, max_humidity, current_temperature, current_humidity, report_time, creator, 
    modifier, gmt_create, gmt_modified
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from sync_safe_temperature_humidity_device
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from sync_safe_temperature_humidity_device
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.das.province.infr.dataobject.SyncSafeTemperatureHumidityDeviceDO" useGeneratedKeys="true">
    insert into sync_safe_temperature_humidity_device (unique_code, device_code, region_name, 
      device_address, min_temperature, max_temperature, 
      min_humidity, max_humidity, current_temperature, 
      current_humidity, report_time, creator, 
      modifier, gmt_create, gmt_modified
      )
    values (#{uniqueCode,jdbcType=VARCHAR}, #{deviceCode,jdbcType=VARCHAR}, #{regionName,jdbcType=TINYINT}, 
      #{deviceAddress,jdbcType=VARCHAR}, #{minTemperature,jdbcType=VARCHAR}, #{maxTemperature,jdbcType=VARCHAR}, 
      #{minHumidity,jdbcType=VARCHAR}, #{maxHumidity,jdbcType=VARCHAR}, #{currentTemperature,jdbcType=VARCHAR}, 
      #{currentHumidity,jdbcType=VARCHAR}, #{reportTime,jdbcType=TIMESTAMP}, #{creator,jdbcType=BIGINT}, 
      #{modifier,jdbcType=BIGINT}, #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtModified,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.das.province.infr.dataobject.SyncSafeTemperatureHumidityDeviceDO" useGeneratedKeys="true">
    insert into sync_safe_temperature_humidity_device
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="uniqueCode != null">
        unique_code,
      </if>
      <if test="deviceCode != null">
        device_code,
      </if>
      <if test="regionName != null">
        region_name,
      </if>
      <if test="deviceAddress != null">
        device_address,
      </if>
      <if test="minTemperature != null">
        min_temperature,
      </if>
      <if test="maxTemperature != null">
        max_temperature,
      </if>
      <if test="minHumidity != null">
        min_humidity,
      </if>
      <if test="maxHumidity != null">
        max_humidity,
      </if>
      <if test="currentTemperature != null">
        current_temperature,
      </if>
      <if test="currentHumidity != null">
        current_humidity,
      </if>
      <if test="reportTime != null">
        report_time,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="modifier != null">
        modifier,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="uniqueCode != null">
        #{uniqueCode,jdbcType=VARCHAR},
      </if>
      <if test="deviceCode != null">
        #{deviceCode,jdbcType=VARCHAR},
      </if>
      <if test="regionName != null">
        #{regionName,jdbcType=TINYINT},
      </if>
      <if test="deviceAddress != null">
        #{deviceAddress,jdbcType=VARCHAR},
      </if>
      <if test="minTemperature != null">
        #{minTemperature,jdbcType=VARCHAR},
      </if>
      <if test="maxTemperature != null">
        #{maxTemperature,jdbcType=VARCHAR},
      </if>
      <if test="minHumidity != null">
        #{minHumidity,jdbcType=VARCHAR},
      </if>
      <if test="maxHumidity != null">
        #{maxHumidity,jdbcType=VARCHAR},
      </if>
      <if test="currentTemperature != null">
        #{currentTemperature,jdbcType=VARCHAR},
      </if>
      <if test="currentHumidity != null">
        #{currentHumidity,jdbcType=VARCHAR},
      </if>
      <if test="reportTime != null">
        #{reportTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=BIGINT},
      </if>
      <if test="modifier != null">
        #{modifier,jdbcType=BIGINT},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.das.province.infr.dataobject.SyncSafeTemperatureHumidityDeviceDO">
    update sync_safe_temperature_humidity_device
    <set>
      <if test="uniqueCode != null">
        unique_code = #{uniqueCode,jdbcType=VARCHAR},
      </if>
      <if test="deviceCode != null">
        device_code = #{deviceCode,jdbcType=VARCHAR},
      </if>
      <if test="regionName != null">
        region_name = #{regionName,jdbcType=TINYINT},
      </if>
      <if test="deviceAddress != null">
        device_address = #{deviceAddress,jdbcType=VARCHAR},
      </if>
      <if test="minTemperature != null">
        min_temperature = #{minTemperature,jdbcType=VARCHAR},
      </if>
      <if test="maxTemperature != null">
        max_temperature = #{maxTemperature,jdbcType=VARCHAR},
      </if>
      <if test="minHumidity != null">
        min_humidity = #{minHumidity,jdbcType=VARCHAR},
      </if>
      <if test="maxHumidity != null">
        max_humidity = #{maxHumidity,jdbcType=VARCHAR},
      </if>
      <if test="currentTemperature != null">
        current_temperature = #{currentTemperature,jdbcType=VARCHAR},
      </if>
      <if test="currentHumidity != null">
        current_humidity = #{currentHumidity,jdbcType=VARCHAR},
      </if>
      <if test="reportTime != null">
        report_time = #{reportTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=BIGINT},
      </if>
      <if test="modifier != null">
        modifier = #{modifier,jdbcType=BIGINT},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.das.province.infr.dataobject.SyncSafeTemperatureHumidityDeviceDO">
    update sync_safe_temperature_humidity_device
    set unique_code = #{uniqueCode,jdbcType=VARCHAR},
      device_code = #{deviceCode,jdbcType=VARCHAR},
      region_name = #{regionName,jdbcType=TINYINT},
      device_address = #{deviceAddress,jdbcType=VARCHAR},
      min_temperature = #{minTemperature,jdbcType=VARCHAR},
      max_temperature = #{maxTemperature,jdbcType=VARCHAR},
      min_humidity = #{minHumidity,jdbcType=VARCHAR},
      max_humidity = #{maxHumidity,jdbcType=VARCHAR},
      current_temperature = #{currentTemperature,jdbcType=VARCHAR},
      current_humidity = #{currentHumidity,jdbcType=VARCHAR},
      report_time = #{reportTime,jdbcType=TIMESTAMP},
      creator = #{creator,jdbcType=BIGINT},
      modifier = #{modifier,jdbcType=BIGINT},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_modified = #{gmtModified,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="selectByUniqueCode" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from sync_safe_temperature_humidity_device
    where unique_code = #{uniqueCode,jdbcType=VARCHAR}
  </select>

  <select id="selectByDeviceCode" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from sync_safe_temperature_humidity_device
    where device_code = #{deviceCode,jdbcType=VARCHAR}
  </select>

  <delete id="deleteByUniqueCode">
    delete from sync_safe_temperature_humidity_device
    where unique_code = #{uniqueCode,jdbcType=VARCHAR}
  </delete>

  <update id="updateByDeviceCode" parameterType="com.das.province.infr.dataobject.SyncSafeTemperatureHumidityDeviceDO">
    update sync_safe_temperature_humidity_device
    <set>
      <if test="regionName != null">
        region_name = #{regionName,jdbcType=TINYINT},
      </if>
      <if test="deviceAddress != null">
        device_address = #{deviceAddress,jdbcType=VARCHAR},
      </if>
      <if test="minTemperature != null">
        min_temperature = #{minTemperature,jdbcType=VARCHAR},
      </if>
      <if test="maxTemperature != null">
        max_temperature = #{maxTemperature,jdbcType=VARCHAR},
      </if>
      <if test="minHumidity != null">
        min_humidity = #{minHumidity,jdbcType=VARCHAR},
      </if>
      <if test="maxHumidity != null">
        max_humidity = #{maxHumidity,jdbcType=VARCHAR},
      </if>
      <if test="currentTemperature != null">
        current_temperature = #{currentTemperature,jdbcType=VARCHAR},
      </if>
      <if test="currentHumidity != null">
        current_humidity = #{currentHumidity,jdbcType=VARCHAR},
      </if>
      <if test="reportTime != null">
        report_time = #{reportTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=BIGINT},
      </if>
      <if test="modifier != null">
        modifier = #{modifier,jdbcType=BIGINT},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      </if>
    </set>
    where unique_code = #{uniqueCode,jdbcType=VARCHAR} and device_code = #{deviceCode,jdbcType=VARCHAR}
  </update>

  <select id="selectList" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from sync_safe_temperature_humidity_device
  </select>
</mapper>