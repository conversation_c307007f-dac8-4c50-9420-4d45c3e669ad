<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.das.province.infr.mapper.StatisticCollectionMapper">
  <resultMap id="BaseResultMap" type="com.das.province.infr.dataobject.StatisticCollectionDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="museum_id" jdbcType="VARCHAR" property="museumId" />
    <result column="museum_name" jdbcType="VARCHAR" property="museumName" />
    <result column="region_code" jdbcType="VARCHAR" property="regionCode" />
    <result column="city_name" jdbcType="VARCHAR" property="cityName" />
    <result column="collection_num" jdbcType="INTEGER" property="collectionNum" />
    <result column="valuable_num" jdbcType="INTEGER" property="valuableNum" />
    <result column="exhibition_num" jdbcType="INTEGER" property="exhibitionNum" />
    <result column="holographic_num" jdbcType="INTEGER" property="holographicNum" />
    <result column="two_model_num" jdbcType="INTEGER" property="twoModelNum" />
    <result column="three_model_num" jdbcType="INTEGER" property="threeModelNum" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
  </resultMap>
  <sql id="Base_Column_List">
    id, museum_id, museum_name, region_code, city_name, collection_num, valuable_num, 
    exhibition_num, holographic_num, two_model_num, three_model_num, gmt_create, gmt_modified
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from statistic_collection
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from statistic_collection
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.das.province.infr.dataobject.StatisticCollectionDO" useGeneratedKeys="true">
    insert into statistic_collection (museum_id, museum_name, region_code, 
      city_name, collection_num, valuable_num, 
      exhibition_num, holographic_num, two_model_num, three_model_num, gmt_create, gmt_modified
      )
    values (#{museumId,jdbcType=VARCHAR}, #{museumName,jdbcType=VARCHAR}, #{regionCode,jdbcType=VARCHAR}, 
      #{cityName,jdbcType=VARCHAR}, #{collectionNum,jdbcType=INTEGER}, #{valuableNum,jdbcType=INTEGER}, 
      #{exhibitionNum,jdbcType=INTEGER},#{holographicNum,jdbcType=INTEGER},
      #{twoModelNum,jdbcType=INTEGER},#{threeModelNum,jdbcType=INTEGER},
      #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtModified,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.das.province.infr.dataobject.StatisticCollectionDO" useGeneratedKeys="true">
    insert into statistic_collection
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="museumId != null">
        museum_id,
      </if>
      <if test="museumName != null">
        museum_name,
      </if>
      <if test="regionCode != null">
        region_code,
      </if>
      <if test="cityName != null">
        city_name,
      </if>
      <if test="collectionNum != null">
        collection_num,
      </if>
      <if test="valuableNum != null">
        valuable_num,
      </if>
      <if test="exhibitionNum != null">
        exhibition_num,
      </if>
      <if test="holographicNum != null">
        holographic_num,
      </if>
      <if test="twoModelNum != null">
        two_model_num,
      </if>
      <if test="threeModelNum != null">
        three_model_num,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="museumId != null">
        #{museumId,jdbcType=VARCHAR},
      </if>
      <if test="museumName != null">
        #{museumName,jdbcType=VARCHAR},
      </if>
      <if test="regionCode != null">
        #{regionCode,jdbcType=VARCHAR},
      </if>
      <if test="cityName != null">
        #{cityName,jdbcType=VARCHAR},
      </if>
      <if test="collectionNum != null">
        #{collectionNum,jdbcType=INTEGER},
      </if>
      <if test="valuableNum != null">
        #{valuableNum,jdbcType=INTEGER},
      </if>
      <if test="exhibitionNum != null">
        #{exhibitionNum,jdbcType=INTEGER},
      </if>
      <if test="holographicNum != null">
        #{holographicNum,jdbcType=INTEGER},
      </if>
      <if test="twoModelNum != null">
        #{twoModelNum,jdbcType=INTEGER},
      </if>
      <if test="threeModelNum != null">
        #{threeModelNum,jdbcType=INTEGER},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.das.province.infr.dataobject.StatisticCollectionDO">
    update statistic_collection
    <set>
      <if test="museumId != null">
        museum_id = #{museumId,jdbcType=VARCHAR},
      </if>
      <if test="museumName != null">
        museum_name = #{museumName,jdbcType=VARCHAR},
      </if>
      <if test="regionCode != null">
        region_code = #{regionCode,jdbcType=VARCHAR},
      </if>
      <if test="cityName != null">
        city_name = #{cityName,jdbcType=VARCHAR},
      </if>
      <if test="collectionNum != null">
        collection_num = #{collectionNum,jdbcType=INTEGER},
      </if>
      <if test="valuableNum != null">
        valuable_num = #{valuableNum,jdbcType=INTEGER},
      </if>
      <if test="exhibitionNum != null">
        exhibition_num = #{exhibitionNum,jdbcType=INTEGER},
      </if>
      <if test="holographicNum != null">
        holographic_num = #{holographicNum,jdbcType=INTEGER},
      </if>
      <if test="twoModelNum != null">
        two_model_num = #{twoModelNum,jdbcType=INTEGER},
      </if>
      <if test="threeModelNum != null">
        three_model_num = #{threeModelNum,jdbcType=INTEGER},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.das.province.infr.dataobject.StatisticCollectionDO">
    update statistic_collection
    set museum_id = #{museumId,jdbcType=VARCHAR},
      museum_name = #{museumName,jdbcType=VARCHAR},
      region_code = #{regionCode,jdbcType=VARCHAR},
      city_name = #{cityName,jdbcType=VARCHAR},
      collection_num = #{collectionNum,jdbcType=INTEGER},
      valuable_num = #{valuableNum,jdbcType=INTEGER},
      exhibition_num = #{exhibitionNum,jdbcType=INTEGER},
      holographic_num = #{holographicNum,jdbcType=INTEGER},
      two_model_num = #{twoModelNum,jdbcType=INTEGER},
      three_model_num = #{threeModelNum,jdbcType=INTEGER},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_modified = #{gmtModified,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="selectList" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from statistic_collection
  </select>

  <select id="groupCollectionByCity" resultType="com.das.province.infr.dataobjectexpand.GroupCollectionByCityDO">
    SELECT LEFT
      ( region_code, 4 ) regionCode,
      city_name cityName,
      sum(collection_num) collectionNum,
      sum(valuable_num) valuableNum,
      sum(exhibition_num) exhibitionNum,
      sum(holographic_num) holographicNum,
      sum(two_model_num) twoModelNum,
      sum(three_model_num) threeModelNum
    FROM
      statistic_collection
    GROUP BY
      LEFT (region_code,4),
      city_name
  </select>

  <select id="selectListByMuseumIds" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from statistic_collection
    where museum_id in
    <foreach collection="museumIds" open="(" close=")" separator="," item="museumId" index="index">
      #{museumId}
    </foreach>
  </select>

  <select id="selectListByCity" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from statistic_collection
    where region_code like CONCAT(#{regionCode,jdbcType=VARCHAR},'%')
  </select>

  <delete id="deleteAll">
    delete from statistic_collection
  </delete>

  <insert id="batchInsert">
    insert into statistic_collection (museum_id, museum_name, region_code,
    city_name, collection_num, valuable_num,exhibition_num,holographic_num, two_model_num, three_model_num) values
    <foreach collection="statisticList" item="t" index="index" separator=",">
      (
      #{t.museumId,jdbcType=VARCHAR}, #{t.museumName,jdbcType=VARCHAR}, #{t.regionCode,jdbcType=VARCHAR},
      #{t.cityName,jdbcType=VARCHAR}, #{t.collectionNum,jdbcType=INTEGER}, #{t.valuableNum,jdbcType=INTEGER},
      #{t.exhibitionNum,jdbcType=INTEGER},#{t.holographicNum,jdbcType=INTEGER},
      #{t.twoModelNum,jdbcType=INTEGER},#{t.threeModelNum,jdbcType=INTEGER}
      )
    </foreach>
  </insert>
</mapper>