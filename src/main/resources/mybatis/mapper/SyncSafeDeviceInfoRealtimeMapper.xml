<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.das.province.infr.mapper.SyncSafeDeviceInfoRealtimeMapper">
  <resultMap id="BaseResultMap" type="com.das.province.infr.dataobject.SyncSafeDeviceInfoRealtimeDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="unique_code" jdbcType="VARCHAR" property="uniqueCode" />
    <result column="region_code" jdbcType="VARCHAR" property="regionCode" />
    <result column="province_code" jdbcType="VARCHAR" property="provinceCode" />
    <result column="city_code" jdbcType="VARCHAR" property="cityCode" />
    <result column="area_code" jdbcType="VARCHAR" property="areaCode" />
    <result column="device_code" jdbcType="VARCHAR" property="deviceCode" />
    <result column="region_name" jdbcType="TINYINT" property="regionName" />
    <result column="device_address" jdbcType="VARCHAR" property="deviceAddress" />
    <result column="min_temperature" jdbcType="VARCHAR" property="minTemperature" />
    <result column="max_temperature" jdbcType="VARCHAR" property="maxTemperature" />
    <result column="min_humidity" jdbcType="VARCHAR" property="minHumidity" />
    <result column="max_humidity" jdbcType="VARCHAR" property="maxHumidity" />
    <result column="temperature" jdbcType="VARCHAR" property="temperature" />
    <result column="temperature_exception_degree" jdbcType="VARCHAR" property="temperatureExceptionDegree" />
    <result column="humidity" jdbcType="VARCHAR" property="humidity" />
    <result column="humidity_exception_degree" jdbcType="VARCHAR" property="humidityExceptionDegree" />
    <result column="report_time" jdbcType="TIMESTAMP" property="reportTime" />
    <result column="device_status" jdbcType="TINYINT" property="deviceStatus" />
    <result column="creator" jdbcType="BIGINT" property="creator" />
    <result column="modifier" jdbcType="BIGINT" property="modifier" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
  </resultMap>
  <sql id="Base_Column_List">
    id, unique_code, region_code, province_code, city_code, area_code, device_code, region_name, 
    device_address, min_temperature, max_temperature, min_humidity, max_humidity, temperature, 
    temperature_exception_degree, humidity, humidity_exception_degree, report_time, device_status, 
    creator, modifier, gmt_create, gmt_modified
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from sync_safe_device_info_realtime
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from sync_safe_device_info_realtime
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.das.province.infr.dataobject.SyncSafeDeviceInfoRealtimeDO" useGeneratedKeys="true">
    insert into sync_safe_device_info_realtime (unique_code, region_code, province_code, 
      city_code, area_code, device_code, 
      region_name, device_address, min_temperature, 
      max_temperature, min_humidity, max_humidity, 
      temperature, temperature_exception_degree, 
      humidity, humidity_exception_degree, report_time, 
      device_status, creator, modifier, 
      gmt_create, gmt_modified)
    values (#{uniqueCode,jdbcType=VARCHAR}, #{regionCode,jdbcType=VARCHAR}, #{provinceCode,jdbcType=VARCHAR}, 
      #{cityCode,jdbcType=VARCHAR}, #{areaCode,jdbcType=VARCHAR}, #{deviceCode,jdbcType=VARCHAR}, 
      #{regionName,jdbcType=TINYINT}, #{deviceAddress,jdbcType=VARCHAR}, #{minTemperature,jdbcType=VARCHAR}, 
      #{maxTemperature,jdbcType=VARCHAR}, #{minHumidity,jdbcType=VARCHAR}, #{maxHumidity,jdbcType=VARCHAR}, 
      #{temperature,jdbcType=VARCHAR}, #{temperatureExceptionDegree,jdbcType=VARCHAR}, 
      #{humidity,jdbcType=VARCHAR}, #{humidityExceptionDegree,jdbcType=VARCHAR}, #{reportTime,jdbcType=TIMESTAMP}, 
      #{deviceStatus,jdbcType=TINYINT}, #{creator,jdbcType=BIGINT}, #{modifier,jdbcType=BIGINT}, 
      #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtModified,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.das.province.infr.dataobject.SyncSafeDeviceInfoRealtimeDO" useGeneratedKeys="true">
    insert into sync_safe_device_info_realtime
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="uniqueCode != null">
        unique_code,
      </if>
      <if test="regionCode != null">
        region_code,
      </if>
      <if test="provinceCode != null">
        province_code,
      </if>
      <if test="cityCode != null">
        city_code,
      </if>
      <if test="areaCode != null">
        area_code,
      </if>
      <if test="deviceCode != null">
        device_code,
      </if>
      <if test="regionName != null">
        region_name,
      </if>
      <if test="deviceAddress != null">
        device_address,
      </if>
      <if test="minTemperature != null">
        min_temperature,
      </if>
      <if test="maxTemperature != null">
        max_temperature,
      </if>
      <if test="minHumidity != null">
        min_humidity,
      </if>
      <if test="maxHumidity != null">
        max_humidity,
      </if>
      <if test="temperature != null">
        temperature,
      </if>
      <if test="temperatureExceptionDegree != null">
        temperature_exception_degree,
      </if>
      <if test="humidity != null">
        humidity,
      </if>
      <if test="humidityExceptionDegree != null">
        humidity_exception_degree,
      </if>
      <if test="reportTime != null">
        report_time,
      </if>
      <if test="deviceStatus != null">
        device_status,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="modifier != null">
        modifier,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="uniqueCode != null">
        #{uniqueCode,jdbcType=VARCHAR},
      </if>
      <if test="regionCode != null">
        #{regionCode,jdbcType=VARCHAR},
      </if>
      <if test="provinceCode != null">
        #{provinceCode,jdbcType=VARCHAR},
      </if>
      <if test="cityCode != null">
        #{cityCode,jdbcType=VARCHAR},
      </if>
      <if test="areaCode != null">
        #{areaCode,jdbcType=VARCHAR},
      </if>
      <if test="deviceCode != null">
        #{deviceCode,jdbcType=VARCHAR},
      </if>
      <if test="regionName != null">
        #{regionName,jdbcType=TINYINT},
      </if>
      <if test="deviceAddress != null">
        #{deviceAddress,jdbcType=VARCHAR},
      </if>
      <if test="minTemperature != null">
        #{minTemperature,jdbcType=VARCHAR},
      </if>
      <if test="maxTemperature != null">
        #{maxTemperature,jdbcType=VARCHAR},
      </if>
      <if test="minHumidity != null">
        #{minHumidity,jdbcType=VARCHAR},
      </if>
      <if test="maxHumidity != null">
        #{maxHumidity,jdbcType=VARCHAR},
      </if>
      <if test="temperature != null">
        #{temperature,jdbcType=VARCHAR},
      </if>
      <if test="temperatureExceptionDegree != null">
        #{temperatureExceptionDegree,jdbcType=VARCHAR},
      </if>
      <if test="humidity != null">
        #{humidity,jdbcType=VARCHAR},
      </if>
      <if test="humidityExceptionDegree != null">
        #{humidityExceptionDegree,jdbcType=VARCHAR},
      </if>
      <if test="reportTime != null">
        #{reportTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deviceStatus != null">
        #{deviceStatus,jdbcType=TINYINT},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=BIGINT},
      </if>
      <if test="modifier != null">
        #{modifier,jdbcType=BIGINT},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.das.province.infr.dataobject.SyncSafeDeviceInfoRealtimeDO">
    update sync_safe_device_info_realtime
    <set>
      <if test="uniqueCode != null">
        unique_code = #{uniqueCode,jdbcType=VARCHAR},
      </if>
      <if test="regionCode != null">
        region_code = #{regionCode,jdbcType=VARCHAR},
      </if>
      <if test="provinceCode != null">
        province_code = #{provinceCode,jdbcType=VARCHAR},
      </if>
      <if test="cityCode != null">
        city_code = #{cityCode,jdbcType=VARCHAR},
      </if>
      <if test="areaCode != null">
        area_code = #{areaCode,jdbcType=VARCHAR},
      </if>
      <if test="deviceCode != null">
        device_code = #{deviceCode,jdbcType=VARCHAR},
      </if>
      <if test="regionName != null">
        region_name = #{regionName,jdbcType=TINYINT},
      </if>
      <if test="deviceAddress != null">
        device_address = #{deviceAddress,jdbcType=VARCHAR},
      </if>
      <if test="minTemperature != null">
        min_temperature = #{minTemperature,jdbcType=VARCHAR},
      </if>
      <if test="maxTemperature != null">
        max_temperature = #{maxTemperature,jdbcType=VARCHAR},
      </if>
      <if test="minHumidity != null">
        min_humidity = #{minHumidity,jdbcType=VARCHAR},
      </if>
      <if test="maxHumidity != null">
        max_humidity = #{maxHumidity,jdbcType=VARCHAR},
      </if>
      <if test="temperature != null">
        temperature = #{temperature,jdbcType=VARCHAR},
      </if>
      <if test="temperatureExceptionDegree != null">
        temperature_exception_degree = #{temperatureExceptionDegree,jdbcType=VARCHAR},
      </if>
      <if test="humidity != null">
        humidity = #{humidity,jdbcType=VARCHAR},
      </if>
      <if test="humidityExceptionDegree != null">
        humidity_exception_degree = #{humidityExceptionDegree,jdbcType=VARCHAR},
      </if>
      <if test="reportTime != null">
        report_time = #{reportTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deviceStatus != null">
        device_status = #{deviceStatus,jdbcType=TINYINT},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=BIGINT},
      </if>
      <if test="modifier != null">
        modifier = #{modifier,jdbcType=BIGINT},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.das.province.infr.dataobject.SyncSafeDeviceInfoRealtimeDO">
    update sync_safe_device_info_realtime
    set unique_code = #{uniqueCode,jdbcType=VARCHAR},
      region_code = #{regionCode,jdbcType=VARCHAR},
      province_code = #{provinceCode,jdbcType=VARCHAR},
      city_code = #{cityCode,jdbcType=VARCHAR},
      area_code = #{areaCode,jdbcType=VARCHAR},
      device_code = #{deviceCode,jdbcType=VARCHAR},
      region_name = #{regionName,jdbcType=TINYINT},
      device_address = #{deviceAddress,jdbcType=VARCHAR},
      min_temperature = #{minTemperature,jdbcType=VARCHAR},
      max_temperature = #{maxTemperature,jdbcType=VARCHAR},
      min_humidity = #{minHumidity,jdbcType=VARCHAR},
      max_humidity = #{maxHumidity,jdbcType=VARCHAR},
      temperature = #{temperature,jdbcType=VARCHAR},
      temperature_exception_degree = #{temperatureExceptionDegree,jdbcType=VARCHAR},
      humidity = #{humidity,jdbcType=VARCHAR},
      humidity_exception_degree = #{humidityExceptionDegree,jdbcType=VARCHAR},
      report_time = #{reportTime,jdbcType=TIMESTAMP},
      device_status = #{deviceStatus,jdbcType=TINYINT},
      creator = #{creator,jdbcType=BIGINT},
      modifier = #{modifier,jdbcType=BIGINT},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_modified = #{gmtModified,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="selectByDeviceCode"  resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from sync_safe_device_info_realtime
    where device_code = #{deviceCode,jdbcType=VARCHAR}
  </select>

  <update id="updateByDeviceCode" parameterType="com.das.province.infr.dataobject.SyncSafeDeviceInfoRealtimeDO">
    update sync_safe_device_info_realtime
    set unique_code = #{uniqueCode,jdbcType=VARCHAR},
        region_code = #{regionCode,jdbcType=VARCHAR},
        province_code = #{provinceCode,jdbcType=VARCHAR},
        city_code = #{cityCode,jdbcType=VARCHAR},
        area_code = #{areaCode,jdbcType=VARCHAR},
        region_name = #{regionName,jdbcType=TINYINT},
        device_address = #{deviceAddress,jdbcType=VARCHAR},
        min_temperature = #{minTemperature,jdbcType=VARCHAR},
        max_temperature = #{maxTemperature,jdbcType=VARCHAR},
        min_humidity = #{minHumidity,jdbcType=VARCHAR},
        max_humidity = #{maxHumidity,jdbcType=VARCHAR},
        temperature = #{temperature,jdbcType=VARCHAR},
        temperature_exception_degree = #{temperatureExceptionDegree,jdbcType=VARCHAR},
        humidity = #{humidity,jdbcType=VARCHAR},
        humidity_exception_degree = #{humidityExceptionDegree,jdbcType=VARCHAR},
        report_time = #{reportTime,jdbcType=TIMESTAMP},
        device_status = #{deviceStatus,jdbcType=TINYINT},
        creator = #{creator,jdbcType=BIGINT},
        modifier = #{modifier,jdbcType=BIGINT},
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
        gmt_modified = #{gmtModified,jdbcType=TIMESTAMP}
    where device_code = #{deviceCode,jdbcType=VARCHAR}
  </update>

  <select id="selectList"  resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from sync_safe_device_info_realtime
  </select>
</mapper>