<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.das.province.infr.mapper.SyncUserInfoMapper">
  <resultMap id="BaseResultMap" type="com.das.province.infr.dataobject.SyncUserInfoDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="unique_code" jdbcType="VARCHAR" property="uniqueCode" />
    <result column="post_code" jdbcType="VARCHAR" property="postCode" />
    <result column="count" jdbcType="BIGINT" property="count" />
    <result column="statistics_date" jdbcType="TIMESTAMP" property="statisticsDate" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
  </resultMap>
  <sql id="Base_Column_List">
    id, unique_code, post_code, `count`, statistics_date, gmt_create, gmt_modified
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from sync_user_info
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from sync_user_info
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.das.province.infr.dataobject.SyncUserInfoDO" useGeneratedKeys="true">
    insert into sync_user_info (unique_code, post_code, `count`, 
      statistics_date, gmt_create, gmt_modified
      )
    values (#{uniqueCode,jdbcType=VARCHAR}, #{postCode,jdbcType=VARCHAR}, #{count,jdbcType=BIGINT}, 
      #{statisticsDate,jdbcType=TIMESTAMP}, #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtModified,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.das.province.infr.dataobject.SyncUserInfoDO" useGeneratedKeys="true">
    insert into sync_user_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="uniqueCode != null">
        unique_code,
      </if>
      <if test="postCode != null">
        post_code,
      </if>
      <if test="count != null">
        `count`,
      </if>
      <if test="statisticsDate != null">
        statistics_date,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="uniqueCode != null">
        #{uniqueCode,jdbcType=VARCHAR},
      </if>
      <if test="postCode != null">
        #{postCode,jdbcType=VARCHAR},
      </if>
      <if test="count != null">
        #{count,jdbcType=BIGINT},
      </if>
      <if test="statisticsDate != null">
        #{statisticsDate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.das.province.infr.dataobject.SyncUserInfoDO">
    update sync_user_info
    <set>
      <if test="uniqueCode != null">
        unique_code = #{uniqueCode,jdbcType=VARCHAR},
      </if>
      <if test="postCode != null">
        post_code = #{postCode,jdbcType=VARCHAR},
      </if>
      <if test="count != null">
        `count` = #{count,jdbcType=BIGINT},
      </if>
      <if test="statisticsDate != null">
        statistics_date = #{statisticsDate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.das.province.infr.dataobject.SyncUserInfoDO">
    update sync_user_info
    set unique_code = #{uniqueCode,jdbcType=VARCHAR},
      post_code = #{postCode,jdbcType=VARCHAR},
      `count` = #{count,jdbcType=BIGINT},
      statistics_date = #{statisticsDate,jdbcType=TIMESTAMP},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_modified = #{gmtModified,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="selectList" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from sync_user_info
  </select>
  <select id="selectUniqueCodeAndPostCodeAndstatisticsDate" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from sync_user_info
    where unique_code = #{uniqueCode,jdbcType=VARCHAR} and post_code = #{postCode,jdbcType=VARCHAR}
    and statistics_date = #{statisticsDate,jdbcType=TIMESTAMP}
  </select>
  <select id="listUniqueCode" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from sync_user_info
    where unique_code = #{uniqueCode,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByUniqueCode">
    delete from sync_user_info
    where unique_code = #{uniqueCode,jdbcType=VARCHAR}
  </delete>
</mapper>