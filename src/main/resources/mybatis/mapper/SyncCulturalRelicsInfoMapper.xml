<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.das.province.infr.mapper.SyncCulturalRelicsInfoMapper">
  <resultMap id="BaseResultMap" type="com.das.province.infr.dataobject.SyncCulturalRelicsInfoDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="source_cultural_id" jdbcType="BIGINT" property="sourceCulturalId" />
    <result column="unique_code" jdbcType="VARCHAR" property="uniqueCode" />
    <result column="number" jdbcType="VARCHAR" property="number" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="level_id" jdbcType="BIGINT" property="levelId" />
    <result column="level_name" jdbcType="VARCHAR" property="levelName" />
    <result column="complete_degree_id" jdbcType="BIGINT" property="completeDegreeId" />
    <result column="complete_degree_name" jdbcType="VARCHAR" property="completeDegreeName" />
    <result column="dynasty_id" jdbcType="BIGINT" property="dynastyId" />
    <result column="dynasty_name" jdbcType="VARCHAR" property="dynastyName" />
    <result column="ad_year_id" jdbcType="BIGINT" property="adYearId" />
    <result column="ad_year_name" jdbcType="VARCHAR" property="adYearName" />
    <result column="specific_age" jdbcType="VARCHAR" property="specificAge" />
    <result column="texture_id" jdbcType="BIGINT" property="textureId" />
    <result column="texture_name" jdbcType="VARCHAR" property="textureName" />
    <result column="source_id" jdbcType="BIGINT" property="sourceId" />
    <result column="source_name" jdbcType="VARCHAR" property="sourceName" />
    <result column="size" jdbcType="VARCHAR" property="size" />
    <result column="keyword" jdbcType="VARCHAR" property="keyword" />
    <result column="cover_id" jdbcType="VARCHAR" property="coverId" />
    <result column="cover_url" jdbcType="VARCHAR" property="coverUrl" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="three_model_url" jdbcType="VARCHAR" property="threeModelUrl" />
    <result column="three_model_status" jdbcType="TINYINT" property="threeModelStatus" />
    <result column="two_model_id" jdbcType="VARCHAR" property="twoModelId" />
    <result column="compared_cultural_relics_id" jdbcType="VARCHAR" property="comparedCulturalRelicsId" />
    <result column="similar_cultural_relics_id" jdbcType="VARCHAR" property="similarCulturalRelicsId" />
    <result column="audio_id" jdbcType="VARCHAR" property="audioId" />
    <result column="video_id" jdbcType="VARCHAR" property="videoId" />
    <result column="academic_literature_id" jdbcType="VARCHAR" property="academicLiteratureId" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="data_status" jdbcType="TINYINT" property="dataStatus" />
    <result column="domain_name" jdbcType="VARCHAR" property="domainName" />
    <result column="panoramic_url" jdbcType="VARCHAR" property="panoramicUrl" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
  </resultMap>
  <sql id="Base_Column_List">
    id, source_cultural_id, unique_code, `number`, `name`, level_id, level_name, complete_degree_id, 
    complete_degree_name, dynasty_id, dynasty_name, ad_year_id, ad_year_name, specific_age, 
    texture_id, texture_name, source_id, source_name, `size`, keyword, cover_id, cover_url, 
    remark, three_model_url, three_model_status, two_model_id, compared_cultural_relics_id, 
    similar_cultural_relics_id, audio_id, video_id, academic_literature_id, `status`, 
    data_status, domain_name, panoramic_url, gmt_create, gmt_modified
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from sync_cultural_relics_info
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from sync_cultural_relics_info
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.das.province.infr.dataobject.SyncCulturalRelicsInfoDO" useGeneratedKeys="true">
    insert into sync_cultural_relics_info (source_cultural_id, unique_code, `number`, 
      `name`, level_id, level_name, 
      complete_degree_id, complete_degree_name, dynasty_id, 
      dynasty_name, ad_year_id, ad_year_name, 
      specific_age, texture_id, texture_name, 
      source_id, source_name, `size`, 
      keyword, cover_id, cover_url, 
      remark, three_model_url, three_model_status, 
      two_model_id, compared_cultural_relics_id, similar_cultural_relics_id, 
      audio_id, video_id, academic_literature_id, 
      `status`, data_status, domain_name, 
      panoramic_url, gmt_create, gmt_modified
      )
    values (#{sourceCulturalId,jdbcType=BIGINT}, #{uniqueCode,jdbcType=VARCHAR}, #{number,jdbcType=VARCHAR}, 
      #{name,jdbcType=VARCHAR}, #{levelId,jdbcType=BIGINT}, #{levelName,jdbcType=VARCHAR}, 
      #{completeDegreeId,jdbcType=BIGINT}, #{completeDegreeName,jdbcType=VARCHAR}, #{dynastyId,jdbcType=BIGINT}, 
      #{dynastyName,jdbcType=VARCHAR}, #{adYearId,jdbcType=BIGINT}, #{adYearName,jdbcType=VARCHAR}, 
      #{specificAge,jdbcType=VARCHAR}, #{textureId,jdbcType=BIGINT}, #{textureName,jdbcType=VARCHAR}, 
      #{sourceId,jdbcType=BIGINT}, #{sourceName,jdbcType=VARCHAR}, #{size,jdbcType=VARCHAR}, 
      #{keyword,jdbcType=VARCHAR}, #{coverId,jdbcType=VARCHAR}, #{coverUrl,jdbcType=VARCHAR}, 
      #{remark,jdbcType=VARCHAR}, #{threeModelUrl,jdbcType=VARCHAR}, #{threeModelStatus,jdbcType=TINYINT}, 
      #{twoModelId,jdbcType=VARCHAR}, #{comparedCulturalRelicsId,jdbcType=VARCHAR}, #{similarCulturalRelicsId,jdbcType=VARCHAR}, 
      #{audioId,jdbcType=VARCHAR}, #{videoId,jdbcType=VARCHAR}, #{academicLiteratureId,jdbcType=VARCHAR}, 
      #{status,jdbcType=TINYINT}, #{dataStatus,jdbcType=TINYINT}, #{domainName,jdbcType=VARCHAR}, 
      #{panoramicUrl,jdbcType=VARCHAR}, #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtModified,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.das.province.infr.dataobject.SyncCulturalRelicsInfoDO" useGeneratedKeys="true">
    insert into sync_cultural_relics_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="sourceCulturalId != null">
        source_cultural_id,
      </if>
      <if test="uniqueCode != null">
        unique_code,
      </if>
      <if test="number != null">
        `number`,
      </if>
      <if test="name != null">
        `name`,
      </if>
      <if test="levelId != null">
        level_id,
      </if>
      <if test="levelName != null">
        level_name,
      </if>
      <if test="completeDegreeId != null">
        complete_degree_id,
      </if>
      <if test="completeDegreeName != null">
        complete_degree_name,
      </if>
      <if test="dynastyId != null">
        dynasty_id,
      </if>
      <if test="dynastyName != null">
        dynasty_name,
      </if>
      <if test="adYearId != null">
        ad_year_id,
      </if>
      <if test="adYearName != null">
        ad_year_name,
      </if>
      <if test="specificAge != null">
        specific_age,
      </if>
      <if test="textureId != null">
        texture_id,
      </if>
      <if test="textureName != null">
        texture_name,
      </if>
      <if test="sourceId != null">
        source_id,
      </if>
      <if test="sourceName != null">
        source_name,
      </if>
      <if test="size != null">
        `size`,
      </if>
      <if test="keyword != null">
        keyword,
      </if>
      <if test="coverId != null">
        cover_id,
      </if>
      <if test="coverUrl != null">
        cover_url,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="threeModelUrl != null">
        three_model_url,
      </if>
      <if test="threeModelStatus != null">
        three_model_status,
      </if>
      <if test="twoModelId != null">
        two_model_id,
      </if>
      <if test="comparedCulturalRelicsId != null">
        compared_cultural_relics_id,
      </if>
      <if test="similarCulturalRelicsId != null">
        similar_cultural_relics_id,
      </if>
      <if test="audioId != null">
        audio_id,
      </if>
      <if test="videoId != null">
        video_id,
      </if>
      <if test="academicLiteratureId != null">
        academic_literature_id,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="dataStatus != null">
        data_status,
      </if>
      <if test="domainName != null">
        domain_name,
      </if>
      <if test="panoramicUrl != null">
        panoramic_url,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="sourceCulturalId != null">
        #{sourceCulturalId,jdbcType=BIGINT},
      </if>
      <if test="uniqueCode != null">
        #{uniqueCode,jdbcType=VARCHAR},
      </if>
      <if test="number != null">
        #{number,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="levelId != null">
        #{levelId,jdbcType=BIGINT},
      </if>
      <if test="levelName != null">
        #{levelName,jdbcType=VARCHAR},
      </if>
      <if test="completeDegreeId != null">
        #{completeDegreeId,jdbcType=BIGINT},
      </if>
      <if test="completeDegreeName != null">
        #{completeDegreeName,jdbcType=VARCHAR},
      </if>
      <if test="dynastyId != null">
        #{dynastyId,jdbcType=BIGINT},
      </if>
      <if test="dynastyName != null">
        #{dynastyName,jdbcType=VARCHAR},
      </if>
      <if test="adYearId != null">
        #{adYearId,jdbcType=BIGINT},
      </if>
      <if test="adYearName != null">
        #{adYearName,jdbcType=VARCHAR},
      </if>
      <if test="specificAge != null">
        #{specificAge,jdbcType=VARCHAR},
      </if>
      <if test="textureId != null">
        #{textureId,jdbcType=BIGINT},
      </if>
      <if test="textureName != null">
        #{textureName,jdbcType=VARCHAR},
      </if>
      <if test="sourceId != null">
        #{sourceId,jdbcType=BIGINT},
      </if>
      <if test="sourceName != null">
        #{sourceName,jdbcType=VARCHAR},
      </if>
      <if test="size != null">
        #{size,jdbcType=VARCHAR},
      </if>
      <if test="keyword != null">
        #{keyword,jdbcType=VARCHAR},
      </if>
      <if test="coverId != null">
        #{coverId,jdbcType=VARCHAR},
      </if>
      <if test="coverUrl != null">
        #{coverUrl,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="threeModelUrl != null">
        #{threeModelUrl,jdbcType=VARCHAR},
      </if>
      <if test="threeModelStatus != null">
        #{threeModelStatus,jdbcType=TINYINT},
      </if>
      <if test="twoModelId != null">
        #{twoModelId,jdbcType=VARCHAR},
      </if>
      <if test="comparedCulturalRelicsId != null">
        #{comparedCulturalRelicsId,jdbcType=VARCHAR},
      </if>
      <if test="similarCulturalRelicsId != null">
        #{similarCulturalRelicsId,jdbcType=VARCHAR},
      </if>
      <if test="audioId != null">
        #{audioId,jdbcType=VARCHAR},
      </if>
      <if test="videoId != null">
        #{videoId,jdbcType=VARCHAR},
      </if>
      <if test="academicLiteratureId != null">
        #{academicLiteratureId,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="dataStatus != null">
        #{dataStatus,jdbcType=TINYINT},
      </if>
      <if test="domainName != null">
        #{domainName,jdbcType=VARCHAR},
      </if>
      <if test="panoramicUrl != null">
        #{panoramicUrl,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.das.province.infr.dataobject.SyncCulturalRelicsInfoDO">
    update sync_cultural_relics_info
    <set>
      <if test="sourceCulturalId != null">
        source_cultural_id = #{sourceCulturalId,jdbcType=BIGINT},
      </if>
      <if test="uniqueCode != null">
        unique_code = #{uniqueCode,jdbcType=VARCHAR},
      </if>
      <if test="number != null">
        `number` = #{number,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        `name` = #{name,jdbcType=VARCHAR},
      </if>
      <if test="levelId != null">
        level_id = #{levelId,jdbcType=BIGINT},
      </if>
      <if test="levelName != null">
        level_name = #{levelName,jdbcType=VARCHAR},
      </if>
      <if test="completeDegreeId != null">
        complete_degree_id = #{completeDegreeId,jdbcType=BIGINT},
      </if>
      <if test="completeDegreeName != null">
        complete_degree_name = #{completeDegreeName,jdbcType=VARCHAR},
      </if>
      <if test="dynastyId != null">
        dynasty_id = #{dynastyId,jdbcType=BIGINT},
      </if>
      <if test="dynastyName != null">
        dynasty_name = #{dynastyName,jdbcType=VARCHAR},
      </if>
      <if test="adYearId != null">
        ad_year_id = #{adYearId,jdbcType=BIGINT},
      </if>
      <if test="adYearName != null">
        ad_year_name = #{adYearName,jdbcType=VARCHAR},
      </if>
      <if test="specificAge != null">
        specific_age = #{specificAge,jdbcType=VARCHAR},
      </if>
      <if test="textureId != null">
        texture_id = #{textureId,jdbcType=BIGINT},
      </if>
      <if test="textureName != null">
        texture_name = #{textureName,jdbcType=VARCHAR},
      </if>
      <if test="sourceId != null">
        source_id = #{sourceId,jdbcType=BIGINT},
      </if>
      <if test="sourceName != null">
        source_name = #{sourceName,jdbcType=VARCHAR},
      </if>
      <if test="size != null">
        `size` = #{size,jdbcType=VARCHAR},
      </if>
      <if test="keyword != null">
        keyword = #{keyword,jdbcType=VARCHAR},
      </if>
      <if test="coverId != null">
        cover_id = #{coverId,jdbcType=VARCHAR},
      </if>
      <if test="coverUrl != null">
        cover_url = #{coverUrl,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="threeModelUrl != null">
        three_model_url = #{threeModelUrl,jdbcType=VARCHAR},
      </if>
      <if test="threeModelStatus != null">
        three_model_status = #{threeModelStatus,jdbcType=TINYINT},
      </if>
      <if test="twoModelId != null">
        two_model_id = #{twoModelId,jdbcType=VARCHAR},
      </if>
      <if test="comparedCulturalRelicsId != null">
        compared_cultural_relics_id = #{comparedCulturalRelicsId,jdbcType=VARCHAR},
      </if>
      <if test="similarCulturalRelicsId != null">
        similar_cultural_relics_id = #{similarCulturalRelicsId,jdbcType=VARCHAR},
      </if>
      <if test="audioId != null">
        audio_id = #{audioId,jdbcType=VARCHAR},
      </if>
      <if test="videoId != null">
        video_id = #{videoId,jdbcType=VARCHAR},
      </if>
      <if test="academicLiteratureId != null">
        academic_literature_id = #{academicLiteratureId,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=TINYINT},
      </if>
      <if test="dataStatus != null">
        data_status = #{dataStatus,jdbcType=TINYINT},
      </if>
      <if test="domainName != null">
        domain_name = #{domainName,jdbcType=VARCHAR},
      </if>
      <if test="panoramicUrl != null">
        panoramic_url = #{panoramicUrl,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.das.province.infr.dataobject.SyncCulturalRelicsInfoDO">
    update sync_cultural_relics_info
    set source_cultural_id = #{sourceCulturalId,jdbcType=BIGINT},
      unique_code = #{uniqueCode,jdbcType=VARCHAR},
      `number` = #{number,jdbcType=VARCHAR},
      `name` = #{name,jdbcType=VARCHAR},
      level_id = #{levelId,jdbcType=BIGINT},
      level_name = #{levelName,jdbcType=VARCHAR},
      complete_degree_id = #{completeDegreeId,jdbcType=BIGINT},
      complete_degree_name = #{completeDegreeName,jdbcType=VARCHAR},
      dynasty_id = #{dynastyId,jdbcType=BIGINT},
      dynasty_name = #{dynastyName,jdbcType=VARCHAR},
      ad_year_id = #{adYearId,jdbcType=BIGINT},
      ad_year_name = #{adYearName,jdbcType=VARCHAR},
      specific_age = #{specificAge,jdbcType=VARCHAR},
      texture_id = #{textureId,jdbcType=BIGINT},
      texture_name = #{textureName,jdbcType=VARCHAR},
      source_id = #{sourceId,jdbcType=BIGINT},
      source_name = #{sourceName,jdbcType=VARCHAR},
      `size` = #{size,jdbcType=VARCHAR},
      keyword = #{keyword,jdbcType=VARCHAR},
      cover_id = #{coverId,jdbcType=VARCHAR},
      cover_url = #{coverUrl,jdbcType=VARCHAR},
      remark = #{remark,jdbcType=VARCHAR},
      three_model_url = #{threeModelUrl,jdbcType=VARCHAR},
      three_model_status = #{threeModelStatus,jdbcType=TINYINT},
      two_model_id = #{twoModelId,jdbcType=VARCHAR},
      compared_cultural_relics_id = #{comparedCulturalRelicsId,jdbcType=VARCHAR},
      similar_cultural_relics_id = #{similarCulturalRelicsId,jdbcType=VARCHAR},
      audio_id = #{audioId,jdbcType=VARCHAR},
      video_id = #{videoId,jdbcType=VARCHAR},
      academic_literature_id = #{academicLiteratureId,jdbcType=VARCHAR},
      `status` = #{status,jdbcType=TINYINT},
      data_status = #{dataStatus,jdbcType=TINYINT},
      domain_name = #{domainName,jdbcType=VARCHAR},
      panoramic_url = #{panoramicUrl,jdbcType=VARCHAR},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_modified = #{gmtModified,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="groupCulturalRelicsByCode" resultType="com.das.province.infr.dataobjectexpand.GroupCulturalRelicsByCodeDO">
    select
      unique_code museumId,
      count(unique_code) holographicNum
    from sync_cultural_relics_info
    where `status` = 1
    group by unique_code
  </select>

  <select id="queryByCondition" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from sync_cultural_relics_info
    where `status` = 1
    <if test="levelName != null">
      and level_name = #{levelName,jdbcType=VARCHAR}
    </if>
    <if test="completeDegreeName != null">
      and complete_degree_name = #{completeDegreeName,jdbcType=VARCHAR}
    </if>
    <if test="dynastyName != null">
      and dynasty_name = #{dynastyName,jdbcType=VARCHAR}
    </if>
    <if test="textureName != null">
      and texture_name = #{textureName,jdbcType=VARCHAR}
    </if>
    <if test="sourceName != null">
      and source_name = #{sourceName,jdbcType=VARCHAR}
    </if>
    <if test="uniqueCodes != null and uniqueCodes.size()>0">
      and unique_code in
      <foreach collection="uniqueCodes" open="(" close=")" separator="," item="id" index="index">
        #{id}
      </foreach>
    </if>
    order by ${sortBy}
  </select>

  <select id="listUniqueCode" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from sync_cultural_relics_info
    where unique_code = #{uniqueCode,jdbcType=VARCHAR}
  </select>

  <delete id="deleteByUniqueCode">
    delete from sync_cultural_relics_info
    where unique_code = #{uniqueCode,jdbcType=VARCHAR}
  </delete>

  <delete id="deleteByUniqueCodeAndNumbers">
    delete from sync_cultural_relics_info
    where unique_code = #{uniqueCode,jdbcType=VARCHAR}
      <if test="numbers != null and numbers.size()>0">
        and `number` not in
        <foreach collection="numbers" open="(" close=")" separator="," item="id" index="index">
          #{id}
        </foreach>
      </if>
  </delete>

  <insert id="batchInsert">
    insert into sync_cultural_relics_info
    (
      source_cultural_id, unique_code, `number`,
      `name`, level_id, level_name,
      complete_degree_id, complete_degree_name, dynasty_id,
      dynasty_name, ad_year_id, ad_year_name,
      specific_age, texture_id, texture_name,
      source_id, source_name, `size`,
      keyword, cover_id, cover_url,
      remark, three_model_url, three_model_status,
      two_model_id, compared_cultural_relics_id, similar_cultural_relics_id,
      audio_id, video_id, academic_literature_id,
      `status`, data_status, domain_name,
      panoramic_url, gmt_create, gmt_modified
    )
    values
    <foreach collection="culturalRelicsInfoList"  item="item" index="index" separator=",">
      (
        #{item.sourceCulturalId,jdbcType=BIGINT}, #{item.uniqueCode,jdbcType=VARCHAR}, #{item.number,jdbcType=VARCHAR},
        #{item.name,jdbcType=VARCHAR}, #{item.levelId,jdbcType=BIGINT}, #{item.levelName,jdbcType=VARCHAR},
        #{item.completeDegreeId,jdbcType=BIGINT}, #{item.completeDegreeName,jdbcType=VARCHAR}, #{item.dynastyId,jdbcType=BIGINT},
        #{item.dynastyName,jdbcType=VARCHAR}, #{item.adYearId,jdbcType=BIGINT}, #{item.adYearName,jdbcType=VARCHAR},
        #{item.specificAge,jdbcType=VARCHAR}, #{item.textureId,jdbcType=BIGINT}, #{item.textureName,jdbcType=VARCHAR},
        #{item.sourceId,jdbcType=BIGINT}, #{item.sourceName,jdbcType=VARCHAR}, #{item.size,jdbcType=VARCHAR},
        #{item.keyword,jdbcType=VARCHAR}, #{item.coverId,jdbcType=VARCHAR}, #{item.coverUrl,jdbcType=VARCHAR},
        #{item.remark,jdbcType=VARCHAR}, #{item.threeModelUrl,jdbcType=VARCHAR}, #{item.threeModelStatus,jdbcType=TINYINT},
        #{item.twoModelId,jdbcType=VARCHAR}, #{item.comparedCulturalRelicsId,jdbcType=VARCHAR}, #{item.similarCulturalRelicsId,jdbcType=VARCHAR},
        #{item.audioId,jdbcType=VARCHAR}, #{item.videoId,jdbcType=VARCHAR}, #{item.academicLiteratureId,jdbcType=VARCHAR},
        #{item.status,jdbcType=TINYINT}, #{item.dataStatus,jdbcType=TINYINT}, #{item.domainName,jdbcType=VARCHAR},
        #{item.panoramicUrl,jdbcType=VARCHAR}, #{item.gmtCreate,jdbcType=TIMESTAMP}, #{item.gmtModified,jdbcType=TIMESTAMP}
      )
    </foreach>
  </insert>
</mapper>