<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.das.province.infr.mapper.StatisticVisitorMonthMapper">
  <resultMap id="BaseResultMap" type="com.das.province.infr.dataobject.StatisticVisitorMonthDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="statistic_month" jdbcType="VARCHAR" property="statisticMonth" />
    <result column="museum_id" jdbcType="VARCHAR" property="museumId" />
    <result column="museum_name" jdbcType="VARCHAR" property="museumName" />
    <result column="region_code" jdbcType="VARCHAR" property="regionCode" />
    <result column="city_name" jdbcType="VARCHAR" property="cityName" />
    <result column="total_num" jdbcType="INTEGER" property="totalNum" />
    <result column="online_num" jdbcType="INTEGER" property="onlineNum" />
    <result column="offline_num" jdbcType="INTEGER" property="offlineNum" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
  </resultMap>
  <sql id="Base_Column_List">
    id, statistic_month, museum_id, museum_name, region_code, city_name, total_num, online_num, 
    offline_num, gmt_create, gmt_modified
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from statistic_visitor_month
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from statistic_visitor_month
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.das.province.infr.dataobject.StatisticVisitorMonthDO" useGeneratedKeys="true">
    insert into statistic_visitor_month (statistic_month, museum_id, museum_name, 
      region_code, city_name, total_num, 
      online_num, offline_num, gmt_create, 
      gmt_modified)
    values (#{statisticMonth,jdbcType=VARCHAR}, #{museumId,jdbcType=VARCHAR}, #{museumName,jdbcType=VARCHAR},
      #{regionCode,jdbcType=VARCHAR}, #{cityName,jdbcType=VARCHAR}, #{totalNum,jdbcType=INTEGER}, 
      #{onlineNum,jdbcType=INTEGER}, #{offlineNum,jdbcType=INTEGER}, #{gmtCreate,jdbcType=TIMESTAMP}, 
      #{gmtModified,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.das.province.infr.dataobject.StatisticVisitorMonthDO" useGeneratedKeys="true">
    insert into statistic_visitor_month
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="statisticMonth != null">
        statistic_month,
      </if>
      <if test="museumId != null">
        museum_id,
      </if>
      <if test="museumName != null">
        museum_name,
      </if>
      <if test="regionCode != null">
        region_code,
      </if>
      <if test="cityName != null">
        city_name,
      </if>
      <if test="totalNum != null">
        total_num,
      </if>
      <if test="onlineNum != null">
        online_num,
      </if>
      <if test="offlineNum != null">
        offline_num,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="statisticMonth != null">
        #{statisticMonth,jdbcType=VARCHAR},
      </if>
      <if test="museumId != null">
        #{museumId,jdbcType=VARCHAR},
      </if>
      <if test="museumName != null">
        #{museumName,jdbcType=VARCHAR},
      </if>
      <if test="regionCode != null">
        #{regionCode,jdbcType=VARCHAR},
      </if>
      <if test="cityName != null">
        #{cityName,jdbcType=VARCHAR},
      </if>
      <if test="totalNum != null">
        #{totalNum,jdbcType=INTEGER},
      </if>
      <if test="onlineNum != null">
        #{onlineNum,jdbcType=INTEGER},
      </if>
      <if test="offlineNum != null">
        #{offlineNum,jdbcType=INTEGER},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.das.province.infr.dataobject.StatisticVisitorMonthDO">
    update statistic_visitor_month
    <set>
      <if test="statisticMonth != null">
        statistic_month = #{statisticMonth,jdbcType=VARCHAR},
      </if>
      <if test="museumId != null">
        museum_id = #{museumId,jdbcType=VARCHAR},
      </if>
      <if test="museumName != null">
        museum_name = #{museumName,jdbcType=VARCHAR},
      </if>
      <if test="regionCode != null">
        region_code = #{regionCode,jdbcType=VARCHAR},
      </if>
      <if test="cityName != null">
        city_name = #{cityName,jdbcType=VARCHAR},
      </if>
      <if test="totalNum != null">
        total_num = #{totalNum,jdbcType=INTEGER},
      </if>
      <if test="onlineNum != null">
        online_num = #{onlineNum,jdbcType=INTEGER},
      </if>
      <if test="offlineNum != null">
        offline_num = #{offlineNum,jdbcType=INTEGER},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.das.province.infr.dataobject.StatisticVisitorMonthDO">
    update statistic_visitor_month
    set statistic_month = #{statisticMonth,jdbcType=VARCHAR},
      museum_id = #{museumId,jdbcType=VARCHAR},
      museum_name = #{museumName,jdbcType=VARCHAR},
      region_code = #{regionCode,jdbcType=VARCHAR},
      city_name = #{cityName,jdbcType=VARCHAR},
      total_num = #{totalNum,jdbcType=INTEGER},
      online_num = #{onlineNum,jdbcType=INTEGER},
      offline_num = #{offlineNum,jdbcType=INTEGER},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_modified = #{gmtModified,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="selectListByMonths" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from statistic_visitor_month
    where statistic_month in
    <foreach collection="months" open="(" close=")" separator="," item="month" index="index">
      #{month}
    </foreach>
    <if test="regionCode != null and regionCode != '' ">
      and region_code like CONCAT(#{regionCode,jdbcType=VARCHAR},'%')
    </if>
  </select>

  <select id="selectListByMonth" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from statistic_visitor_month
    where statistic_month = #{statisticMonth,jdbcType=VARCHAR}
  </select>

  <delete id="deleteAll">
    delete from statistic_visitor_month
  </delete>

  <insert id="batchInsert">
    insert into statistic_visitor_month (statistic_month, museum_id, museum_name,
    region_code, city_name, total_num,
    online_num, offline_num) values
    <foreach collection="visitorMonthDOList" item="t" index="index" separator=",">
      (
      #{t.statisticMonth,jdbcType=VARCHAR}, #{t.museumId,jdbcType=VARCHAR}, #{t.museumName,jdbcType=VARCHAR},
      #{t.regionCode,jdbcType=VARCHAR}, #{t.cityName,jdbcType=VARCHAR}, #{t.totalNum,jdbcType=INTEGER},
      #{t.onlineNum,jdbcType=INTEGER}, #{t.offlineNum,jdbcType=INTEGER}
      )
    </foreach>
  </insert>

  <select id="groupMuseumByYear" resultType="com.das.province.infr.dataobjectexpand.GroupMuseumByYearDO">
    select
      museum_id museumId,
      sum(total_num) totalNum
    from statistic_visitor_month
    where statistic_month like CONCAT(#{year,jdbcType=VARCHAR},'%')
    group by museum_id
  </select>
</mapper>