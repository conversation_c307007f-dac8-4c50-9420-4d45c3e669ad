<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.das.province.infr.mapper.SyncCulturalRelicsMapper">
  <resultMap id="BaseResultMap" type="com.das.province.infr.dataobject.SyncCulturalRelicsDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="unique_code" jdbcType="VARCHAR" property="uniqueCode" />
    <result column="number" jdbcType="VARCHAR" property="number" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="level_name" jdbcType="VARCHAR" property="levelName" />
    <result column="complete_degree_name" jdbcType="VARCHAR" property="completeDegreeName" />
    <result column="dynasty_name" jdbcType="VARCHAR" property="dynastyName" />
    <result column="ad_year_name" jdbcType="VARCHAR" property="adYearName" />
    <result column="specific_age" jdbcType="VARCHAR" property="specificAge" />
    <result column="texture_name" jdbcType="VARCHAR" property="textureName" />
    <result column="source_name" jdbcType="VARCHAR" property="sourceName" />
    <result column="size" jdbcType="VARCHAR" property="size" />
    <result column="keyword" jdbcType="VARCHAR" property="keyword" />
    <result column="cover_id" jdbcType="VARCHAR" property="coverId" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="data_status" jdbcType="TINYINT" property="dataStatus" />
    <result column="holographic_access_count" jdbcType="BIGINT" property="holographicAccessCount" />
    <result column="overall_access_count" jdbcType="BIGINT" property="overallAccessCount" />
    <result column="statistics_date" jdbcType="TIMESTAMP" property="statisticsDate" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
  </resultMap>
  <sql id="Base_Column_List">
    id, unique_code, `number`, `name`, level_name, complete_degree_name, dynasty_name, 
    ad_year_name, specific_age, texture_name, source_name, `size`, keyword, cover_id, 
    remark, `status`, data_status, holographic_access_count, overall_access_count, statistics_date, 
    gmt_create, gmt_modified
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from sync_cultural_relics
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from sync_cultural_relics
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.das.province.infr.dataobject.SyncCulturalRelicsDO" useGeneratedKeys="true">
    insert into sync_cultural_relics (unique_code, `number`, `name`, 
      level_name, complete_degree_name, dynasty_name, 
      ad_year_name, specific_age, texture_name, 
      source_name, `size`, keyword, 
      cover_id, remark, `status`, 
      data_status, holographic_access_count, overall_access_count, 
      statistics_date, gmt_create, gmt_modified
      )
    values (#{uniqueCode,jdbcType=VARCHAR}, #{number,jdbcType=VARCHAR}, #{name,jdbcType=VARCHAR}, 
      #{levelName,jdbcType=VARCHAR}, #{completeDegreeName,jdbcType=VARCHAR}, #{dynastyName,jdbcType=VARCHAR}, 
      #{adYearName,jdbcType=VARCHAR}, #{specificAge,jdbcType=VARCHAR}, #{textureName,jdbcType=VARCHAR}, 
      #{sourceName,jdbcType=VARCHAR}, #{size,jdbcType=VARCHAR}, #{keyword,jdbcType=VARCHAR}, 
      #{coverId,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR}, #{status,jdbcType=TINYINT}, 
      #{dataStatus,jdbcType=TINYINT}, #{holographicAccessCount,jdbcType=BIGINT}, #{overallAccessCount,jdbcType=BIGINT}, 
      #{statisticsDate,jdbcType=TIMESTAMP}, #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtModified,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.das.province.infr.dataobject.SyncCulturalRelicsDO" useGeneratedKeys="true">
    insert into sync_cultural_relics
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="uniqueCode != null">
        unique_code,
      </if>
      <if test="number != null">
        `number`,
      </if>
      <if test="name != null">
        `name`,
      </if>
      <if test="levelName != null">
        level_name,
      </if>
      <if test="completeDegreeName != null">
        complete_degree_name,
      </if>
      <if test="dynastyName != null">
        dynasty_name,
      </if>
      <if test="adYearName != null">
        ad_year_name,
      </if>
      <if test="specificAge != null">
        specific_age,
      </if>
      <if test="textureName != null">
        texture_name,
      </if>
      <if test="sourceName != null">
        source_name,
      </if>
      <if test="size != null">
        `size`,
      </if>
      <if test="keyword != null">
        keyword,
      </if>
      <if test="coverId != null">
        cover_id,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="dataStatus != null">
        data_status,
      </if>
      <if test="holographicAccessCount != null">
        holographic_access_count,
      </if>
      <if test="overallAccessCount != null">
        overall_access_count,
      </if>
      <if test="statisticsDate != null">
        statistics_date,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="uniqueCode != null">
        #{uniqueCode,jdbcType=VARCHAR},
      </if>
      <if test="number != null">
        #{number,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="levelName != null">
        #{levelName,jdbcType=VARCHAR},
      </if>
      <if test="completeDegreeName != null">
        #{completeDegreeName,jdbcType=VARCHAR},
      </if>
      <if test="dynastyName != null">
        #{dynastyName,jdbcType=VARCHAR},
      </if>
      <if test="adYearName != null">
        #{adYearName,jdbcType=VARCHAR},
      </if>
      <if test="specificAge != null">
        #{specificAge,jdbcType=VARCHAR},
      </if>
      <if test="textureName != null">
        #{textureName,jdbcType=VARCHAR},
      </if>
      <if test="sourceName != null">
        #{sourceName,jdbcType=VARCHAR},
      </if>
      <if test="size != null">
        #{size,jdbcType=VARCHAR},
      </if>
      <if test="keyword != null">
        #{keyword,jdbcType=VARCHAR},
      </if>
      <if test="coverId != null">
        #{coverId,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="dataStatus != null">
        #{dataStatus,jdbcType=TINYINT},
      </if>
      <if test="holographicAccessCount != null">
        #{holographicAccessCount,jdbcType=BIGINT},
      </if>
      <if test="overallAccessCount != null">
        #{overallAccessCount,jdbcType=BIGINT},
      </if>
      <if test="statisticsDate != null">
        #{statisticsDate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.das.province.infr.dataobject.SyncCulturalRelicsDO">
    update sync_cultural_relics
    <set>
      <if test="uniqueCode != null">
        unique_code = #{uniqueCode,jdbcType=VARCHAR},
      </if>
      <if test="number != null">
        `number` = #{number,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        `name` = #{name,jdbcType=VARCHAR},
      </if>
      <if test="levelName != null">
        level_name = #{levelName,jdbcType=VARCHAR},
      </if>
      <if test="completeDegreeName != null">
        complete_degree_name = #{completeDegreeName,jdbcType=VARCHAR},
      </if>
      <if test="dynastyName != null">
        dynasty_name = #{dynastyName,jdbcType=VARCHAR},
      </if>
      <if test="adYearName != null">
        ad_year_name = #{adYearName,jdbcType=VARCHAR},
      </if>
      <if test="specificAge != null">
        specific_age = #{specificAge,jdbcType=VARCHAR},
      </if>
      <if test="textureName != null">
        texture_name = #{textureName,jdbcType=VARCHAR},
      </if>
      <if test="sourceName != null">
        source_name = #{sourceName,jdbcType=VARCHAR},
      </if>
      <if test="size != null">
        `size` = #{size,jdbcType=VARCHAR},
      </if>
      <if test="keyword != null">
        keyword = #{keyword,jdbcType=VARCHAR},
      </if>
      <if test="coverId != null">
        cover_id = #{coverId,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=TINYINT},
      </if>
      <if test="dataStatus != null">
        data_status = #{dataStatus,jdbcType=TINYINT},
      </if>
      <if test="holographicAccessCount != null">
        holographic_access_count = #{holographicAccessCount,jdbcType=BIGINT},
      </if>
      <if test="overallAccessCount != null">
        overall_access_count = #{overallAccessCount,jdbcType=BIGINT},
      </if>
      <if test="statisticsDate != null">
        statistics_date = #{statisticsDate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.das.province.infr.dataobject.SyncCulturalRelicsDO">
    update sync_cultural_relics
    set unique_code = #{uniqueCode,jdbcType=VARCHAR},
      `number` = #{number,jdbcType=VARCHAR},
      `name` = #{name,jdbcType=VARCHAR},
      level_name = #{levelName,jdbcType=VARCHAR},
      complete_degree_name = #{completeDegreeName,jdbcType=VARCHAR},
      dynasty_name = #{dynastyName,jdbcType=VARCHAR},
      ad_year_name = #{adYearName,jdbcType=VARCHAR},
      specific_age = #{specificAge,jdbcType=VARCHAR},
      texture_name = #{textureName,jdbcType=VARCHAR},
      source_name = #{sourceName,jdbcType=VARCHAR},
      `size` = #{size,jdbcType=VARCHAR},
      keyword = #{keyword,jdbcType=VARCHAR},
      cover_id = #{coverId,jdbcType=VARCHAR},
      remark = #{remark,jdbcType=VARCHAR},
      `status` = #{status,jdbcType=TINYINT},
      data_status = #{dataStatus,jdbcType=TINYINT},
      holographic_access_count = #{holographicAccessCount,jdbcType=BIGINT},
      overall_access_count = #{overallAccessCount,jdbcType=BIGINT},
      statistics_date = #{statisticsDate,jdbcType=TIMESTAMP},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_modified = #{gmtModified,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <delete id="deleteByUniqueCode">
    delete from sync_cultural_relics
    where unique_code = #{uniqueCode,jdbcType=VARCHAR}
  </delete>

  <select id="selectUniqueCodeAndNumberAndStatisticsDate" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from sync_cultural_relics
    where unique_code = #{uniqueCode,jdbcType=VARCHAR} and `number` = #{number,jdbcType=VARCHAR}
    and statistics_date = #{statisticsDate,jdbcType=TIMESTAMP}
  </select>

  <select id="selectList" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from sync_cultural_relics
  </select>

  <select id="listUniqueCode" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from sync_cultural_relics
    where unique_code = #{uniqueCode,jdbcType=VARCHAR}
  </select>

  <select id="listByCondition" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from sync_cultural_relics
    where statistics_date &gt;= #{statisticsDateStart,jdbcType=TIMESTAMP} and statistics_date &lt;= #{statisticsDateEnd,jdbcType=TIMESTAMP}
    <if test="uniqueCodes != null and uniqueCodes.size()>0">
      and unique_code in
      <foreach collection="uniqueCodes" open="(" close=")" separator="," item="id" index="index">
        #{id}
      </foreach>
    </if>
  </select>
</mapper>