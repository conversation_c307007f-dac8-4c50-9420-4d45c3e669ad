<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.das.province.infr.mapper.StatisticVisitorYearMapper">
  <resultMap id="BaseResultMap" type="com.das.province.infr.dataobject.StatisticVisitorYearDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="museum_id" jdbcType="VARCHAR" property="museumId" />
    <result column="museum_name" jdbcType="VARCHAR" property="museumName" />
    <result column="region_code" jdbcType="VARCHAR" property="regionCode" />
    <result column="city_name" jdbcType="VARCHAR" property="cityName" />
    <result column="total_num" jdbcType="INTEGER" property="totalNum" />
    <result column="online_num" jdbcType="INTEGER" property="onlineNum" />
    <result column="offline_num" jdbcType="INTEGER" property="offlineNum" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
  </resultMap>
  <sql id="Base_Column_List">
    id, museum_id, museum_name, region_code, city_name, total_num, online_num, offline_num, 
    gmt_create, gmt_modified
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from statistic_visitor_year
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from statistic_visitor_year
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.das.province.infr.dataobject.StatisticVisitorYearDO" useGeneratedKeys="true">
    insert into statistic_visitor_year (museum_id, museum_name, region_code, 
      city_name, total_num, online_num, 
      offline_num, gmt_create, gmt_modified
      )
    values (#{museumId,jdbcType=VARCHAR}, #{museumName,jdbcType=VARCHAR}, #{regionCode,jdbcType=VARCHAR}, 
      #{cityName,jdbcType=VARCHAR}, #{totalNum,jdbcType=INTEGER}, #{onlineNum,jdbcType=INTEGER}, 
      #{offlineNum,jdbcType=INTEGER}, #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtModified,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.das.province.infr.dataobject.StatisticVisitorYearDO" useGeneratedKeys="true">
    insert into statistic_visitor_year
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="museumId != null">
        museum_id,
      </if>
      <if test="museumName != null">
        museum_name,
      </if>
      <if test="regionCode != null">
        region_code,
      </if>
      <if test="cityName != null">
        city_name,
      </if>
      <if test="totalNum != null">
        total_num,
      </if>
      <if test="onlineNum != null">
        online_num,
      </if>
      <if test="offlineNum != null">
        offline_num,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="museumId != null">
        #{museumId,jdbcType=VARCHAR},
      </if>
      <if test="museumName != null">
        #{museumName,jdbcType=VARCHAR},
      </if>
      <if test="regionCode != null">
        #{regionCode,jdbcType=VARCHAR},
      </if>
      <if test="cityName != null">
        #{cityName,jdbcType=VARCHAR},
      </if>
      <if test="totalNum != null">
        #{totalNum,jdbcType=INTEGER},
      </if>
      <if test="onlineNum != null">
        #{onlineNum,jdbcType=INTEGER},
      </if>
      <if test="offlineNum != null">
        #{offlineNum,jdbcType=INTEGER},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.das.province.infr.dataobject.StatisticVisitorYearDO">
    update statistic_visitor_year
    <set>
      <if test="museumId != null">
        museum_id = #{museumId,jdbcType=VARCHAR},
      </if>
      <if test="museumName != null">
        museum_name = #{museumName,jdbcType=VARCHAR},
      </if>
      <if test="regionCode != null">
        region_code = #{regionCode,jdbcType=VARCHAR},
      </if>
      <if test="cityName != null">
        city_name = #{cityName,jdbcType=VARCHAR},
      </if>
      <if test="totalNum != null">
        total_num = #{totalNum,jdbcType=INTEGER},
      </if>
      <if test="onlineNum != null">
        online_num = #{onlineNum,jdbcType=INTEGER},
      </if>
      <if test="offlineNum != null">
        offline_num = #{offlineNum,jdbcType=INTEGER},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.das.province.infr.dataobject.StatisticVisitorYearDO">
    update statistic_visitor_year
    set museum_id = #{museumId,jdbcType=VARCHAR},
      museum_name = #{museumName,jdbcType=VARCHAR},
      region_code = #{regionCode,jdbcType=VARCHAR},
      city_name = #{cityName,jdbcType=VARCHAR},
      total_num = #{totalNum,jdbcType=INTEGER},
      online_num = #{onlineNum,jdbcType=INTEGER},
      offline_num = #{offlineNum,jdbcType=INTEGER},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_modified = #{gmtModified,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="selectList" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from statistic_visitor_year
  </select>

  <select id="groupVisitorYearByCity" resultType="com.das.province.infr.dataobjectexpand.GroupVisitorYearByCityDO">
    SELECT LEFT
      ( region_code, 4 ) regionCode,
      city_name cityName,
      sum(total_num) totalNum,
      sum(offline_num) offlineNum,
      sum(online_num) onlineNum
    FROM
      statistic_visitor_year
    GROUP BY
      LEFT (region_code,4),
      city_name
  </select>

  <select id="selectTopList" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from statistic_visitor_year
    order by total_num desc
    limit 5;
  </select>

  <select id="selectListByRegionCode" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from statistic_visitor_year
    where region_code like CONCAT(#{regionCode,jdbcType=VARCHAR},'%')
  </select>

  <select id="selectListMuseumIds" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from statistic_visitor_year
    where 1 = 1
    <if test="museumIds != null and museumIds.size() > 0 ">
      and museum_id in
      <foreach collection="museumIds" open="(" close=")" separator="," item="museumId" index="index">
        #{museumId}
      </foreach>
    </if>
  </select>

  <delete id="deleteAll">
    delete from statistic_visitor_year
  </delete>

  <insert id="batchInsert">
    insert into statistic_visitor_year (museum_id, museum_name, region_code,
    city_name, total_num, online_num,offline_num) values
    <foreach collection="visitorYearDOList" item="t" index="index" separator=",">
      (
      #{t.museumId,jdbcType=VARCHAR}, #{t.museumName,jdbcType=VARCHAR}, #{t.regionCode,jdbcType=VARCHAR},
      #{t.cityName,jdbcType=VARCHAR}, #{t.totalNum,jdbcType=INTEGER}, #{t.onlineNum,jdbcType=INTEGER},
      #{t.offlineNum,jdbcType=INTEGER}
      )
    </foreach>
  </insert>
</mapper>