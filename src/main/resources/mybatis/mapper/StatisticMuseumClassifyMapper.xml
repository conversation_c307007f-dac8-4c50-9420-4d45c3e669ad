<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.das.province.infr.mapper.StatisticMuseumClassifyMapper">
  <resultMap id="BaseResultMap" type="com.das.province.infr.dataobject.StatisticMuseumClassifyDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="classify_type" jdbcType="TINYINT" property="classifyType" />
    <result column="statistic_id" jdbcType="BIGINT" property="statisticId" />
    <result column="statistic_name" jdbcType="VARCHAR" property="statisticName" />
    <result column="museum_num" jdbcType="INTEGER" property="museumNum" />
    <result column="museum_proportion" jdbcType="VARCHAR" property="museumProportion" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
  </resultMap>
  <sql id="Base_Column_List">
    id, classify_type, statistic_id, statistic_name, museum_num, museum_proportion, gmt_create, 
    gmt_modified
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from statistic_museum_classify
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from statistic_museum_classify
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.das.province.infr.dataobject.StatisticMuseumClassifyDO" useGeneratedKeys="true">
    insert into statistic_museum_classify (classify_type, statistic_id, statistic_name, 
      museum_num, museum_proportion, gmt_create, 
      gmt_modified)
    values (#{classifyType,jdbcType=TINYINT}, #{statisticId,jdbcType=BIGINT}, #{statisticName,jdbcType=VARCHAR}, 
      #{museumNum,jdbcType=INTEGER}, #{museumProportion,jdbcType=VARCHAR}, #{gmtCreate,jdbcType=TIMESTAMP}, 
      #{gmtModified,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.das.province.infr.dataobject.StatisticMuseumClassifyDO" useGeneratedKeys="true">
    insert into statistic_museum_classify
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="classifyType != null">
        classify_type,
      </if>
      <if test="statisticId != null">
        statistic_id,
      </if>
      <if test="statisticName != null">
        statistic_name,
      </if>
      <if test="museumNum != null">
        museum_num,
      </if>
      <if test="museumProportion != null">
        museum_proportion,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="classifyType != null">
        #{classifyType,jdbcType=TINYINT},
      </if>
      <if test="statisticId != null">
        #{statisticId,jdbcType=BIGINT},
      </if>
      <if test="statisticName != null">
        #{statisticName,jdbcType=VARCHAR},
      </if>
      <if test="museumNum != null">
        #{museumNum,jdbcType=INTEGER},
      </if>
      <if test="museumProportion != null">
        #{museumProportion,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.das.province.infr.dataobject.StatisticMuseumClassifyDO">
    update statistic_museum_classify
    <set>
      <if test="classifyType != null">
        classify_type = #{classifyType,jdbcType=TINYINT},
      </if>
      <if test="statisticId != null">
        statistic_id = #{statisticId,jdbcType=BIGINT},
      </if>
      <if test="statisticName != null">
        statistic_name = #{statisticName,jdbcType=VARCHAR},
      </if>
      <if test="museumNum != null">
        museum_num = #{museumNum,jdbcType=INTEGER},
      </if>
      <if test="museumProportion != null">
        museum_proportion = #{museumProportion,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.das.province.infr.dataobject.StatisticMuseumClassifyDO">
    update statistic_museum_classify
    set classify_type = #{classifyType,jdbcType=TINYINT},
      statistic_id = #{statisticId,jdbcType=BIGINT},
      statistic_name = #{statisticName,jdbcType=VARCHAR},
      museum_num = #{museumNum,jdbcType=INTEGER},
      museum_proportion = #{museumProportion,jdbcType=VARCHAR},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_modified = #{gmtModified,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="selectList" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from statistic_museum_classify
  </select>

  <delete id="deleteAll">
    delete from statistic_museum_classify
  </delete>
</mapper>