<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.das.province.infr.mapper.SyncCollectionDigitalMapper">
  <resultMap id="BaseResultMap" type="com.das.province.infr.dataobject.SyncCollectionDigitalDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="unique_code" jdbcType="VARCHAR" property="uniqueCode" />
    <result column="two_model_num" jdbcType="INTEGER" property="twoModelNum" />
    <result column="three_model_num" jdbcType="INTEGER" property="threeModelNum" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
  </resultMap>
  <sql id="Base_Column_List">
    id, unique_code, two_model_num, three_model_num, gmt_create, gmt_modified
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from sync_collection_digital
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from sync_collection_digital
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.das.province.infr.dataobject.SyncCollectionDigitalDO" useGeneratedKeys="true">
    insert into sync_collection_digital (unique_code, two_model_num, three_model_num, 
      gmt_create, gmt_modified)
    values (#{uniqueCode,jdbcType=VARCHAR}, #{twoModelNum,jdbcType=INTEGER}, #{threeModelNum,jdbcType=INTEGER}, 
      #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtModified,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.das.province.infr.dataobject.SyncCollectionDigitalDO" useGeneratedKeys="true">
    insert into sync_collection_digital
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="uniqueCode != null">
        unique_code,
      </if>
      <if test="twoModelNum != null">
        two_model_num,
      </if>
      <if test="threeModelNum != null">
        three_model_num,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="uniqueCode != null">
        #{uniqueCode,jdbcType=VARCHAR},
      </if>
      <if test="twoModelNum != null">
        #{twoModelNum,jdbcType=INTEGER},
      </if>
      <if test="threeModelNum != null">
        #{threeModelNum,jdbcType=INTEGER},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.das.province.infr.dataobject.SyncCollectionDigitalDO">
    update sync_collection_digital
    <set>
      <if test="uniqueCode != null">
        unique_code = #{uniqueCode,jdbcType=VARCHAR},
      </if>
      <if test="twoModelNum != null">
        two_model_num = #{twoModelNum,jdbcType=INTEGER},
      </if>
      <if test="threeModelNum != null">
        three_model_num = #{threeModelNum,jdbcType=INTEGER},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.das.province.infr.dataobject.SyncCollectionDigitalDO">
    update sync_collection_digital
    set unique_code = #{uniqueCode,jdbcType=VARCHAR},
      two_model_num = #{twoModelNum,jdbcType=INTEGER},
      three_model_num = #{threeModelNum,jdbcType=INTEGER},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_modified = #{gmtModified,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="selectList" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from sync_collection_digital
  </select>

  <delete id="deleteByUniqueCode">
    delete from sync_collection_digital
    where unique_code = #{uniqueCode,jdbcType=VARCHAR}
  </delete>

  <select id="selectByUniqueCode" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from sync_collection_digital
    where unique_code = #{uniqueCode,jdbcType=VARCHAR}
  </select>
</mapper>