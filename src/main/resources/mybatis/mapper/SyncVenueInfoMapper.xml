<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.das.province.infr.mapper.SyncVenueInfoMapper">
  <resultMap id="BaseResultMap" type="com.das.province.infr.dataobject.SyncVenueInfoDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="unique_code" jdbcType="VARCHAR" property="uniqueCode" />
    <result column="venue_close" jdbcType="TINYINT" property="venueClose" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
  </resultMap>
  <sql id="Base_Column_List">
    id, unique_code, venue_close, gmt_create, gmt_modified
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from sync_venue_info
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from sync_venue_info
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.das.province.infr.dataobject.SyncVenueInfoDO" useGeneratedKeys="true">
    insert into sync_venue_info (unique_code, venue_close, gmt_create, 
      gmt_modified)
    values (#{uniqueCode,jdbcType=VARCHAR}, #{venueClose,jdbcType=TINYINT}, #{gmtCreate,jdbcType=TIMESTAMP}, 
      #{gmtModified,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.das.province.infr.dataobject.SyncVenueInfoDO" useGeneratedKeys="true">
    insert into sync_venue_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="uniqueCode != null">
        unique_code,
      </if>
      <if test="venueClose != null">
        venue_close,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="uniqueCode != null">
        #{uniqueCode,jdbcType=VARCHAR},
      </if>
      <if test="venueClose != null">
        #{venueClose,jdbcType=TINYINT},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.das.province.infr.dataobject.SyncVenueInfoDO">
    update sync_venue_info
    <set>
      <if test="uniqueCode != null">
        unique_code = #{uniqueCode,jdbcType=VARCHAR},
      </if>
      <if test="venueClose != null">
        venue_close = #{venueClose,jdbcType=TINYINT},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.das.province.infr.dataobject.SyncVenueInfoDO">
    update sync_venue_info
    set unique_code = #{uniqueCode,jdbcType=VARCHAR},
      venue_close = #{venueClose,jdbcType=TINYINT},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_modified = #{gmtModified,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="selectByMuseumId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from sync_venue_info
    where unique_code = #{uniqueCode,jdbcType=VARCHAR}
  </select>

  <update id="updateByUniqueCode" parameterType="com.das.province.infr.dataobject.SyncVenueInfoDO">
    update sync_venue_info
    set venue_close = #{venueClose,jdbcType=TINYINT}
    where unique_code = #{uniqueCode,jdbcType=VARCHAR}
  </update>

  <select id="selectListByUniqueCodes" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from sync_venue_info
    where unique_code in
    <foreach collection="uniqueCodes" open="(" close=")" separator="," item="id" index="index">
      #{id}
    </foreach>
  </select>
</mapper>