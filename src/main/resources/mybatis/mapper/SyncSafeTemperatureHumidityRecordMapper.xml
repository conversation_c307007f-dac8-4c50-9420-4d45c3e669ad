<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.das.province.infr.mapper.SyncSafeTemperatureHumidityRecordMapper">
  <resultMap id="BaseResultMap" type="com.das.province.infr.dataobject.SyncSafeTemperatureHumidityRecordDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="unique_code" jdbcType="VARCHAR" property="uniqueCode" />
    <result column="device_code" jdbcType="VARCHAR" property="deviceCode" />
    <result column="current_temperature" jdbcType="VARCHAR" property="currentTemperature" />
    <result column="current_humidity" jdbcType="VARCHAR" property="currentHumidity" />
    <result column="report_time" jdbcType="TIMESTAMP" property="reportTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, unique_code, device_code, current_temperature, current_humidity, report_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from sync_safe_temperature_humidity_record
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from sync_safe_temperature_humidity_record
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.das.province.infr.dataobject.SyncSafeTemperatureHumidityRecordDO" useGeneratedKeys="true">
    insert into sync_safe_temperature_humidity_record (unique_code, device_code, current_temperature, 
      current_humidity, report_time)
    values (#{uniqueCode,jdbcType=VARCHAR}, #{deviceCode,jdbcType=VARCHAR}, #{currentTemperature,jdbcType=VARCHAR}, 
      #{currentHumidity,jdbcType=VARCHAR}, #{reportTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.das.province.infr.dataobject.SyncSafeTemperatureHumidityRecordDO" useGeneratedKeys="true">
    insert into sync_safe_temperature_humidity_record
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="uniqueCode != null">
        unique_code,
      </if>
      <if test="deviceCode != null">
        device_code,
      </if>
      <if test="currentTemperature != null">
        current_temperature,
      </if>
      <if test="currentHumidity != null">
        current_humidity,
      </if>
      <if test="reportTime != null">
        report_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="uniqueCode != null">
        #{uniqueCode,jdbcType=VARCHAR},
      </if>
      <if test="deviceCode != null">
        #{deviceCode,jdbcType=VARCHAR},
      </if>
      <if test="currentTemperature != null">
        #{currentTemperature,jdbcType=VARCHAR},
      </if>
      <if test="currentHumidity != null">
        #{currentHumidity,jdbcType=VARCHAR},
      </if>
      <if test="reportTime != null">
        #{reportTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.das.province.infr.dataobject.SyncSafeTemperatureHumidityRecordDO">
    update sync_safe_temperature_humidity_record
    <set>
      <if test="uniqueCode != null">
        unique_code = #{uniqueCode,jdbcType=VARCHAR},
      </if>
      <if test="deviceCode != null">
        device_code = #{deviceCode,jdbcType=VARCHAR},
      </if>
      <if test="currentTemperature != null">
        current_temperature = #{currentTemperature,jdbcType=VARCHAR},
      </if>
      <if test="currentHumidity != null">
        current_humidity = #{currentHumidity,jdbcType=VARCHAR},
      </if>
      <if test="reportTime != null">
        report_time = #{reportTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.das.province.infr.dataobject.SyncSafeTemperatureHumidityRecordDO">
    update sync_safe_temperature_humidity_record
    set unique_code = #{uniqueCode,jdbcType=VARCHAR},
      device_code = #{deviceCode,jdbcType=VARCHAR},
      current_temperature = #{currentTemperature,jdbcType=VARCHAR},
      current_humidity = #{currentHumidity,jdbcType=VARCHAR},
      report_time = #{reportTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>