<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.das.province.infr.mapper.StatisticMuseumGeneralMapper">
  <resultMap id="BaseResultMap" type="com.das.province.infr.dataobject.StatisticMuseumGeneralDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="total_num" jdbcType="INTEGER" property="totalNum" />
    <result column="settle_in_num" jdbcType="INTEGER" property="settleInNum" />
    <result column="under_preparation_num" jdbcType="INTEGER" property="underPreparationNum" />
    <result column="not_preparation_num" jdbcType="INTEGER" property="notPreparationNum" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
  </resultMap>
  <sql id="Base_Column_List">
    id, total_num, settle_in_num, under_preparation_num, not_preparation_num, gmt_create, 
    gmt_modified
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from statistic_museum_general
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from statistic_museum_general
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.das.province.infr.dataobject.StatisticMuseumGeneralDO" useGeneratedKeys="true">
    insert into statistic_museum_general (total_num, settle_in_num, under_preparation_num, 
      not_preparation_num, gmt_create, gmt_modified
      )
    values (#{totalNum,jdbcType=INTEGER}, #{settleInNum,jdbcType=INTEGER}, #{underPreparationNum,jdbcType=INTEGER}, 
      #{notPreparationNum,jdbcType=INTEGER}, #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtModified,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.das.province.infr.dataobject.StatisticMuseumGeneralDO" useGeneratedKeys="true">
    insert into statistic_museum_general
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="totalNum != null">
        total_num,
      </if>
      <if test="settleInNum != null">
        settle_in_num,
      </if>
      <if test="underPreparationNum != null">
        under_preparation_num,
      </if>
      <if test="notPreparationNum != null">
        not_preparation_num,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="totalNum != null">
        #{totalNum,jdbcType=INTEGER},
      </if>
      <if test="settleInNum != null">
        #{settleInNum,jdbcType=INTEGER},
      </if>
      <if test="underPreparationNum != null">
        #{underPreparationNum,jdbcType=INTEGER},
      </if>
      <if test="notPreparationNum != null">
        #{notPreparationNum,jdbcType=INTEGER},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.das.province.infr.dataobject.StatisticMuseumGeneralDO">
    update statistic_museum_general
    <set>
      <if test="totalNum != null">
        total_num = #{totalNum,jdbcType=INTEGER},
      </if>
      <if test="settleInNum != null">
        settle_in_num = #{settleInNum,jdbcType=INTEGER},
      </if>
      <if test="underPreparationNum != null">
        under_preparation_num = #{underPreparationNum,jdbcType=INTEGER},
      </if>
      <if test="notPreparationNum != null">
        not_preparation_num = #{notPreparationNum,jdbcType=INTEGER},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.das.province.infr.dataobject.StatisticMuseumGeneralDO">
    update statistic_museum_general
    set total_num = #{totalNum,jdbcType=INTEGER},
      settle_in_num = #{settleInNum,jdbcType=INTEGER},
      under_preparation_num = #{underPreparationNum,jdbcType=INTEGER},
      not_preparation_num = #{notPreparationNum,jdbcType=INTEGER},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_modified = #{gmtModified,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="selectInfo" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from statistic_museum_general
  </select>

  <delete id="deleteAll">
    delete from statistic_museum_general
  </delete>
</mapper>