<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.das.province.infr.mapper.SyncCommCollectionRegisterInfoMapper">
  <resultMap id="BaseResultMap" type="com.das.province.infr.dataobject.SyncCommCollectionRegisterInfoDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="unique_code" jdbcType="VARCHAR" property="uniqueCode" />
    <result column="collection_id" jdbcType="BIGINT" property="collectionId" />
    <result column="register_no" jdbcType="VARCHAR" property="registerNo" />
    <result column="rfid" jdbcType="VARCHAR" property="rfid" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="original_no" jdbcType="VARCHAR" property="originalNo" />
    <result column="original_name" jdbcType="VARCHAR" property="originalName" />
    <result column="classify_name" jdbcType="VARCHAR" property="classifyName" />
    <result column="classify_desc" jdbcType="VARCHAR" property="classifyDesc" />
    <result column="identify_level_name" jdbcType="VARCHAR" property="identifyLevelName" />
    <result column="category_name" jdbcType="VARCHAR" property="categoryName" />
    <result column="in_tibetan_age_range_name" jdbcType="VARCHAR" property="inTibetanAgeRangeName" />
    <result column="in_tibetan_date" jdbcType="TIMESTAMP" property="inTibetanDate" />
    <result column="address" jdbcType="VARCHAR" property="address" />
    <result column="specific_info" jdbcType="VARCHAR" property="specificInfo" />
    <result column="era" jdbcType="VARCHAR" property="era" />
    <result column="age" jdbcType="VARCHAR" property="age" />
    <result column="use" jdbcType="VARCHAR" property="use" />
    <result column="color" jdbcType="VARCHAR" property="color" />
    <result column="texture_name" jdbcType="VARCHAR" property="textureName" />
    <result column="complete_degree_name" jdbcType="VARCHAR" property="completeDegreeName" />
    <result column="source_name" jdbcType="VARCHAR" property="sourceName" />
    <result column="size" jdbcType="VARCHAR" property="size" />
    <result column="cover_url" jdbcType="VARCHAR" property="coverUrl" />
    <result column="two_model_url" jdbcType="VARCHAR" property="twoModelUrl" />
    <result column="three_model_url" jdbcType="VARCHAR" property="threeModelUrl" />
    <result column="register_status" jdbcType="VARCHAR" property="registerStatus" />
    <result column="cultural_status" jdbcType="TINYINT" property="culturalStatus" />
    <result column="login_date" jdbcType="TIMESTAMP" property="loginDate" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
  </resultMap>
  <sql id="Base_Column_List">
    id, unique_code, collection_id, register_no, rfid, `name`, original_no, original_name, 
    classify_name, classify_desc, identify_level_name, category_name, in_tibetan_age_range_name, 
    in_tibetan_date, address, specific_info, era, age, `use`, color, texture_name, complete_degree_name,
    source_name, `size`, cover_url, two_model_url, three_model_url, register_status,
    cultural_status, login_date, gmt_create, gmt_modified
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from sync_comm_collection_register_info
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from sync_comm_collection_register_info
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.das.province.infr.dataobject.SyncCommCollectionRegisterInfoDO" useGeneratedKeys="true">
    insert into sync_comm_collection_register_info (unique_code, collection_id, register_no,
                                                         rfid, `name`, original_no,
                                                         original_name, classify_name, classify_desc,
                                                         identify_level_name, category_name, in_tibetan_age_range_name,
                                                         in_tibetan_date, address, specific_info,
                                                         era, age, `use`, color,
                                                         texture_name, complete_degree_name, source_name,
                                                         `size`, cover_url, two_model_url,
                                                         three_model_url, register_status, cultural_status,
                                                         login_date, gmt_create, gmt_modified
    )
    values (#{uniqueCode,jdbcType=VARCHAR}, #{collectionId,jdbcType=BIGINT}, #{registerNo,jdbcType=VARCHAR},
            #{rfid,jdbcType=VARCHAR}, #{name,jdbcType=VARCHAR}, #{originalNo,jdbcType=VARCHAR},
            #{originalName,jdbcType=VARCHAR}, #{classifyName,jdbcType=VARCHAR}, #{classifyDesc,jdbcType=VARCHAR},
            #{identifyLevelName,jdbcType=VARCHAR}, #{categoryName,jdbcType=VARCHAR}, #{inTibetanAgeRangeName,jdbcType=VARCHAR},
            #{inTibetanDate,jdbcType=TIMESTAMP}, #{address,jdbcType=VARCHAR}, #{specificInfo,jdbcType=VARCHAR},
            #{era,jdbcType=VARCHAR}, #{age,jdbcType=VARCHAR}, #{use,jdbcType=VARCHAR}, #{color,jdbcType=VARCHAR},
            #{textureName,jdbcType=VARCHAR}, #{completeDegreeName,jdbcType=VARCHAR}, #{sourceName,jdbcType=VARCHAR},
            #{size,jdbcType=VARCHAR}, #{coverUrl,jdbcType=VARCHAR}, #{twoModelUrl,jdbcType=VARCHAR},
            #{threeModelUrl,jdbcType=VARCHAR}, #{registerStatus,jdbcType=VARCHAR}, #{culturalStatus,jdbcType=TINYINT},
            #{loginDate,jdbcType=TIMESTAMP}, #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtModified,jdbcType=TIMESTAMP}
           )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.das.province.infr.dataobject.SyncCommCollectionRegisterInfoDO" useGeneratedKeys="true">
    insert into sync_comm_collection_register_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="uniqueCode != null">
        unique_code,
      </if>
      <if test="collectionId != null">
        collection_id,
      </if>
      <if test="registerNo != null">
        register_no,
      </if>
      <if test="rfid != null">
        rfid,
      </if>
      <if test="name != null">
        `name`,
      </if>
      <if test="originalNo != null">
        original_no,
      </if>
      <if test="originalName != null">
        original_name,
      </if>
      <if test="classifyName != null">
        classify_name,
      </if>
      <if test="classifyDesc != null">
        classify_desc,
      </if>
      <if test="identifyLevelName != null">
        identify_level_name,
      </if>
      <if test="categoryName != null">
        category_name,
      </if>
      <if test="inTibetanAgeRangeName != null">
        in_tibetan_age_range_name,
      </if>
      <if test="inTibetanDate != null">
        in_tibetan_date,
      </if>
      <if test="address != null">
        address,
      </if>
      <if test="specificInfo != null">
        specific_info,
      </if>
      <if test="era != null">
        era,
      </if>
      <if test="age != null">
        age,
      </if>
      <if test="use != null">
        `use`,
      </if>
      <if test="color != null">
        color,
      </if>
      <if test="textureName != null">
        texture_name,
      </if>
      <if test="completeDegreeName != null">
        complete_degree_name,
      </if>
      <if test="sourceName != null">
        source_name,
      </if>
      <if test="size != null">
        `size`,
      </if>
      <if test="coverUrl != null">
        cover_url,
      </if>
      <if test="twoModelUrl != null">
        two_model_url,
      </if>
      <if test="threeModelUrl != null">
        three_model_url,
      </if>
      <if test="registerStatus != null">
        register_status,
      </if>
      <if test="culturalStatus != null">
        cultural_status,
      </if>
      <if test="loginDate != null">
        login_date,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="uniqueCode != null">
        #{uniqueCode,jdbcType=VARCHAR},
      </if>
      <if test="collectionId != null">
        #{collectionId,jdbcType=BIGINT},
      </if>
      <if test="registerNo != null">
        #{registerNo,jdbcType=VARCHAR},
      </if>
      <if test="rfid != null">
        #{rfid,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="originalNo != null">
        #{originalNo,jdbcType=VARCHAR},
      </if>
      <if test="originalName != null">
        #{originalName,jdbcType=VARCHAR},
      </if>
      <if test="classifyName != null">
        #{classifyName,jdbcType=VARCHAR},
      </if>
      <if test="classifyDesc != null">
        #{classifyDesc,jdbcType=VARCHAR},
      </if>
      <if test="identifyLevelName != null">
        #{identifyLevelName,jdbcType=VARCHAR},
      </if>
      <if test="categoryName != null">
        #{categoryName,jdbcType=VARCHAR},
      </if>
      <if test="inTibetanAgeRangeName != null">
        #{inTibetanAgeRangeName,jdbcType=VARCHAR},
      </if>
      <if test="inTibetanDate != null">
        #{inTibetanDate,jdbcType=TIMESTAMP},
      </if>
      <if test="address != null">
        #{address,jdbcType=VARCHAR},
      </if>
      <if test="specificInfo != null">
        #{specificInfo,jdbcType=VARCHAR},
      </if>
      <if test="era != null">
        #{era,jdbcType=VARCHAR},
      </if>
      <if test="age != null">
        #{age,jdbcType=VARCHAR},
      </if>
      <if test="use != null">
        #{use,jdbcType=VARCHAR},
      </if>
      <if test="color != null">
        #{color,jdbcType=VARCHAR},
      </if>
      <if test="textureName != null">
        #{textureName,jdbcType=VARCHAR},
      </if>
      <if test="completeDegreeName != null">
        #{completeDegreeName,jdbcType=VARCHAR},
      </if>
      <if test="sourceName != null">
        #{sourceName,jdbcType=VARCHAR},
      </if>
      <if test="size != null">
        #{size,jdbcType=VARCHAR},
      </if>
      <if test="coverUrl != null">
        #{coverUrl,jdbcType=VARCHAR},
      </if>
      <if test="twoModelUrl != null">
        #{twoModelUrl,jdbcType=VARCHAR},
      </if>
      <if test="threeModelUrl != null">
        #{threeModelUrl,jdbcType=VARCHAR},
      </if>
      <if test="registerStatus != null">
        #{registerStatus,jdbcType=VARCHAR},
      </if>
      <if test="culturalStatus != null">
        #{culturalStatus,jdbcType=TINYINT},
      </if>
      <if test="loginDate != null">
        #{loginDate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>

  <insert id="batchInsert">
    insert into sync_comm_collection_register_info (unique_code, collection_id, register_no,
    rfid, `name`, original_no,
    original_name, classify_name, classify_desc,
    identify_level_name, category_name, in_tibetan_age_range_name,
    in_tibetan_date, address, specific_info,
    era, age, `use`, color,
    texture_name, complete_degree_name, source_name,
    `size`, cover_url, two_model_url,
    three_model_url, register_status, cultural_status,
    login_date, gmt_create, gmt_modified
    )
    values
    <foreach collection="collectionList" item="t" index="index" separator=",">
      (
      #{t.uniqueCode,jdbcType=VARCHAR}, #{t.collectionId,jdbcType=BIGINT}, #{t.registerNo,jdbcType=VARCHAR},
      #{t.rfid,jdbcType=VARCHAR}, #{t.name,jdbcType=VARCHAR}, #{t.originalNo,jdbcType=VARCHAR},
      #{t.originalName,jdbcType=VARCHAR}, #{t.classifyName,jdbcType=VARCHAR}, #{t.classifyDesc,jdbcType=VARCHAR},
      #{t.identifyLevelName,jdbcType=VARCHAR}, #{t.categoryName,jdbcType=VARCHAR}, #{t.inTibetanAgeRangeName,jdbcType=VARCHAR},
      #{t.inTibetanDate,jdbcType=TIMESTAMP}, #{t.address,jdbcType=VARCHAR}, #{t.specificInfo,jdbcType=VARCHAR},
      #{t.era,jdbcType=VARCHAR}, #{t.age,jdbcType=VARCHAR}, #{t.use,jdbcType=VARCHAR}, #{t.color,jdbcType=VARCHAR},
      #{t.textureName,jdbcType=VARCHAR}, #{t.completeDegreeName,jdbcType=VARCHAR}, #{t.sourceName,jdbcType=VARCHAR},
      #{t.size,jdbcType=VARCHAR}, #{t.coverUrl,jdbcType=VARCHAR}, #{t.twoModelUrl,jdbcType=VARCHAR},
      #{t.threeModelUrl,jdbcType=VARCHAR}, #{t.registerStatus,jdbcType=VARCHAR}, #{t.culturalStatus,jdbcType=TINYINT},
      #{t.loginDate,jdbcType=TIMESTAMP}, #{t.gmtCreate,jdbcType=TIMESTAMP}, #{t.gmtModified,jdbcType=TIMESTAMP}
      )
    </foreach>
  </insert>

  <update id="updateByPrimaryKeySelective" parameterType="com.das.province.infr.dataobject.SyncCommCollectionRegisterInfoDO">
    update sync_comm_collection_register_info
    <set>
      <if test="uniqueCode != null">
        unique_code = #{uniqueCode,jdbcType=VARCHAR},
      </if>
      <if test="collectionId != null">
        collection_id = #{collectionId,jdbcType=BIGINT},
      </if>
      <if test="registerNo != null">
        register_no = #{registerNo,jdbcType=VARCHAR},
      </if>
      <if test="rfid != null">
        rfid = #{rfid,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        `name` = #{name,jdbcType=VARCHAR},
      </if>
      <if test="originalNo != null">
        original_no = #{originalNo,jdbcType=VARCHAR},
      </if>
      <if test="originalName != null">
        original_name = #{originalName,jdbcType=VARCHAR},
      </if>
      <if test="classifyName != null">
        classify_name = #{classifyName,jdbcType=VARCHAR},
      </if>
      <if test="classifyDesc != null">
        classify_desc = #{classifyDesc,jdbcType=VARCHAR},
      </if>
      <if test="identifyLevelName != null">
        identify_level_name = #{identifyLevelName,jdbcType=VARCHAR},
      </if>
      <if test="categoryName != null">
        category_name = #{categoryName,jdbcType=VARCHAR},
      </if>
      <if test="inTibetanAgeRangeName != null">
        in_tibetan_age_range_name = #{inTibetanAgeRangeName,jdbcType=VARCHAR},
      </if>
      <if test="inTibetanDate != null">
        in_tibetan_date = #{inTibetanDate,jdbcType=TIMESTAMP},
      </if>
      <if test="address != null">
        address = #{address,jdbcType=VARCHAR},
      </if>
      <if test="specificInfo != null">
        specific_info = #{specificInfo,jdbcType=VARCHAR},
      </if>
      <if test="era != null">
        era = #{era,jdbcType=VARCHAR},
      </if>
      <if test="age != null">
        age = #{age,jdbcType=VARCHAR},
      </if>
      <if test="use != null">
        `use` = #{use,jdbcType=VARCHAR},
      </if>
      <if test="color != null">
        color = #{color,jdbcType=VARCHAR},
      </if>
      <if test="textureName != null">
        texture_name = #{textureName,jdbcType=VARCHAR},
      </if>
      <if test="completeDegreeName != null">
        complete_degree_name = #{completeDegreeName,jdbcType=VARCHAR},
      </if>
      <if test="sourceName != null">
        source_name = #{sourceName,jdbcType=VARCHAR},
      </if>
      <if test="size != null">
        `size` = #{size,jdbcType=VARCHAR},
      </if>
      <if test="coverUrl != null">
        cover_url = #{coverUrl,jdbcType=VARCHAR},
      </if>
      <if test="twoModelUrl != null">
        two_model_url = #{twoModelUrl,jdbcType=VARCHAR},
      </if>
      <if test="threeModelUrl != null">
        three_model_url = #{threeModelUrl,jdbcType=VARCHAR},
      </if>
      <if test="registerStatus != null">
        register_status = #{registerStatus,jdbcType=VARCHAR},
      </if>
      <if test="culturalStatus != null">
        cultural_status = #{culturalStatus,jdbcType=TINYINT},
      </if>
      <if test="loginDate != null">
        login_date = #{loginDate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.das.province.infr.dataobject.SyncCommCollectionRegisterInfoDO">
    update sync_comm_collection_register_info
    set unique_code = #{uniqueCode,jdbcType=VARCHAR},
        collection_id = #{collectionId,jdbcType=BIGINT},
        register_no = #{registerNo,jdbcType=VARCHAR},
        rfid = #{rfid,jdbcType=VARCHAR},
        `name` = #{name,jdbcType=VARCHAR},
        original_no = #{originalNo,jdbcType=VARCHAR},
        original_name = #{originalName,jdbcType=VARCHAR},
        classify_name = #{classifyName,jdbcType=VARCHAR},
        classify_desc = #{classifyDesc,jdbcType=VARCHAR},
        identify_level_name = #{identifyLevelName,jdbcType=VARCHAR},
        category_name = #{categoryName,jdbcType=VARCHAR},
        in_tibetan_age_range_name = #{inTibetanAgeRangeName,jdbcType=VARCHAR},
        in_tibetan_date = #{inTibetanDate,jdbcType=TIMESTAMP},
        address = #{address,jdbcType=VARCHAR},
        specific_info = #{specificInfo,jdbcType=VARCHAR},
        era = #{era,jdbcType=VARCHAR},
        age = #{age,jdbcType=VARCHAR},
        `use` = #{use,jdbcType=VARCHAR},
        color = #{color,jdbcType=VARCHAR},
        texture_name = #{textureName,jdbcType=VARCHAR},
        complete_degree_name = #{completeDegreeName,jdbcType=VARCHAR},
        source_name = #{sourceName,jdbcType=VARCHAR},
        `size` = #{size,jdbcType=VARCHAR},
        cover_url = #{coverUrl,jdbcType=VARCHAR},
        two_model_url = #{twoModelUrl,jdbcType=VARCHAR},
        three_model_url = #{threeModelUrl,jdbcType=VARCHAR},
        register_status = #{registerStatus,jdbcType=VARCHAR},
        cultural_status = #{culturalStatus,jdbcType=TINYINT},
        login_date = #{loginDate,jdbcType=TIMESTAMP},
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
        gmt_modified = #{gmtModified,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="selectList" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from sync_comm_collection_register_info
  </select>
  <delete id="deleteByUniqueCode">
    delete from sync_comm_collection_register_info
    where unique_code = #{uniqueCode,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByUniqueCodeAndRegisterNos">
    delete from sync_comm_collection_register_info
    where unique_code = #{uniqueCode,jdbcType=VARCHAR} and register_no not in
    <foreach collection="registerNos" open="(" close=")" separator="," item="id" index="index">
      #{id}
    </foreach>
  </delete>
  <select id="selectUniqueCodeAndRegisterNo" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from sync_comm_collection_register_info
    where unique_code = #{uniqueCode,jdbcType=VARCHAR} and register_no = #{registerNo,jdbcType=VARCHAR}
  </select>
  <select id="listUniqueCode" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from sync_comm_collection_register_info
    where unique_code = #{uniqueCode,jdbcType=VARCHAR}
  </select>

  <select id="selectListByUniqueCode" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from sync_comm_collection_register_info
    where unique_code = #{uniqueCode,jdbcType=VARCHAR}
  </select>

  <select id="queryByCondition" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from sync_comm_collection_register_info
    where 1 = 1
    <if test="collectionName != null and collectionName != '' ">
      and `name` like CONCAT('%',#{collectionName,jdbcType=VARCHAR},'%')
    </if>
    <if test="levelName != null and levelName != '' ">
      and identify_level_name = #{levelName,jdbcType=VARCHAR}
    </if>
    <if test="categoryName != null and categoryName != '' ">
      and category_name = #{categoryName,jdbcType=VARCHAR}
    </if>
    <if test="age != null and age != '' ">
      and age = #{age,jdbcType=VARCHAR}
    </if>
    <if test="selectTexture != null and selectTexture != '' ">
      and id in
      <foreach collection="ids" open="(" close=")" separator="," item="id" index="index">
        #{id}
      </foreach>
    </if>
    <if test="completeDegreeName != null and completeDegreeName != '' ">
      and complete_degree_name = #{completeDegreeName,jdbcType=VARCHAR}
    </if>
    <if test="sourceName != null and sourceName != '' ">
      and source_name = #{sourceName,jdbcType=VARCHAR}
    </if>
    <if test="uniqueCodes != null and uniqueCodes.size()>0">
      and unique_code in
      <foreach collection="uniqueCodes" open="(" close=")" separator="," item="id" index="index">
        #{id}
      </foreach>
    </if>
    order by cover_url desc
  </select>

  <select id="collectionGroupByLevel" resultType="com.das.province.infr.dataobjectexpand.CollectionGroupByLevelDO">
    select
      IFNULL(identify_level_name,"无") identifyLevelName,
      count(*) collectionNum
    from sync_comm_collection_register_info
    group by identify_level_name
  </select>

  <select id="countByUniqueCode" resultType="java.lang.Integer">
    select
      count(1)
    from sync_comm_collection_register_info
    where unique_code = #{uniqueCode,jdbcType=VARCHAR}
    <if test="registerStatus != null and registerStatus != '' ">
      and register_status = #{registerStatus,jdbcType=VARCHAR}
    </if>
  </select>

</mapper>