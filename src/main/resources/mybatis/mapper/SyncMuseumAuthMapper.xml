<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.das.province.infr.mapper.SyncMuseumAuthMapper">
  <resultMap id="BaseResultMap" type="com.das.province.infr.dataobject.SyncMuseumAuthDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="museum_base_id" jdbcType="BIGINT" property="museumBaseId" />
    <result column="unique_code" jdbcType="VARCHAR" property="uniqueCode" />
    <result column="key" jdbcType="VARCHAR" property="key" />
    <result column="creator" jdbcType="BIGINT" property="creator" />
    <result column="modifier" jdbcType="BIGINT" property="modifier" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
  </resultMap>
  <sql id="Base_Column_List">
    id, museum_base_id, unique_code, `key`, creator, modifier, gmt_create, gmt_modified
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from sync_museum_auth
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from sync_museum_auth
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.das.province.infr.dataobject.SyncMuseumAuthDO" useGeneratedKeys="true">
    insert into sync_museum_auth (museum_base_id, unique_code, `key`, 
      creator, modifier, gmt_create, 
      gmt_modified)
    values (#{museumBaseId,jdbcType=BIGINT}, #{uniqueCode,jdbcType=VARCHAR}, #{key,jdbcType=VARCHAR}, 
      #{creator,jdbcType=BIGINT}, #{modifier,jdbcType=BIGINT}, #{gmtCreate,jdbcType=TIMESTAMP}, 
      #{gmtModified,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.das.province.infr.dataobject.SyncMuseumAuthDO" useGeneratedKeys="true">
    insert into sync_museum_auth
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="museumBaseId != null">
        museum_base_id,
      </if>
      <if test="uniqueCode != null">
        unique_code,
      </if>
      <if test="key != null">
        `key`,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="modifier != null">
        modifier,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="museumBaseId != null">
        #{museumBaseId,jdbcType=BIGINT},
      </if>
      <if test="uniqueCode != null">
        #{uniqueCode,jdbcType=VARCHAR},
      </if>
      <if test="key != null">
        #{key,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=BIGINT},
      </if>
      <if test="modifier != null">
        #{modifier,jdbcType=BIGINT},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.das.province.infr.dataobject.SyncMuseumAuthDO">
    update sync_museum_auth
    <set>
      <if test="museumBaseId != null">
        museum_base_id = #{museumBaseId,jdbcType=BIGINT},
      </if>
      <if test="uniqueCode != null">
        unique_code = #{uniqueCode,jdbcType=VARCHAR},
      </if>
      <if test="key != null">
        `key` = #{key,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=BIGINT},
      </if>
      <if test="modifier != null">
        modifier = #{modifier,jdbcType=BIGINT},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.das.province.infr.dataobject.SyncMuseumAuthDO">
    update sync_museum_auth
    set museum_base_id = #{museumBaseId,jdbcType=BIGINT},
      unique_code = #{uniqueCode,jdbcType=VARCHAR},
      `key` = #{key,jdbcType=VARCHAR},
      creator = #{creator,jdbcType=BIGINT},
      modifier = #{modifier,jdbcType=BIGINT},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_modified = #{gmtModified,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="selectByMuseumBaseId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from sync_museum_auth
    where museum_base_id = #{museumBaseId,jdbcType=BIGINT}
  </select>
</mapper>