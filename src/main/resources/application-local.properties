# mysql config
spring.datasource.druid.name=province_digital_museum
spring.datasource.druid.url=**************************************************************************************************************************************************************************************************************************************************************************************************************
spring.datasource.druid.username=root
spring.datasource.druid.password=123456
spring.datasource.druid.initial-size=5
spring.datasource.druid.maximum-pool-size=100
spring.datasource.druid.max-wait=60000
spring.datasource.druid.min-idle=5
spring.datasource.druid.max-active=20
spring.datasource.druid.validation-query=SELECT 1
spring.datasource.druid.test-on-borrow=false
spring.datasource.druid.test-while-idle=true
spring.datasource.druid.test-while-return=true
spring.datasource.druid.time-between-eviction-runs-millis=60000
spring.datasource.druid.min-evictableIdle-time-millis=300000
spring.datasource.druid.connection-init-sqls=set names utf8mb4;
spring.datasource.druid.filters=stat,slf4j
# reids config
#Redisé»è®¤ä½¿ç¨åº
spring.redis.database=2
#Redisæ¬å°æå¡å¨å°å
spring.redis.host=localhost
#Redisæå¡å¨ç«¯å£,é»è®¤ä¸º6379
spring.redis.port=6379
#Redisæå¡å¨è¿æ¥å¯ç ï¼é»è®¤ä¸ºç©ºï¼è¥æè®¾ç½®æè®¾ç½®çæ¥
#spring.redis.password=
#Redisè¶æ¶æ¶é´
spring.redis.timeout=1000
#è¿æ¥æ± æå¤§è¿æ¥æ°ï¼è¥ä¸ºè´è´£åè¡¨ç¤ºæ²¡æä»»ä½éå¶
spring.redis.lettuce.pool.max-active=8
#è¿æ¥æ± æå¤§é»å¡ç­å¾æ¶é´ï¼è¥ä¸ºè´è´£åè¡¨ç¤ºæ²¡æä»»ä½éå¶
spring.redis.lettuce.pool.max-wait=5000
#è¿æ¥æ± ä¸­çæå¤§ç©ºé²è¿æ¥
spring.redis.lettuce.pool.max-idle=8
#è¿æ¥æ± ä¸­çæå°ç©ºé²è¿æ¥
spring.redis.lettuce.pool.min-idle=0
logging.file.path=/data/logs/${spring.application.name}
logging.level.root=info
logging.level.sql=debug

# multipart
spring.servlet.multipart.max-file-size=-1
spring.servlet.multipart.max-request-size=-1

# éç½® Sa-Token åç¬ä½¿ç¨ç Redis è¿æ¥
# Redisæ°æ®åºç´¢å¼ï¼é»è®¤ä¸º0ï¼
sa-token.alone-redis.database=2
# Redisæå¡å¨å°å
sa-token.alone-redis.host=localhost
# Redisæå¡å¨è¿æ¥ç«¯å£
sa-token.alone-redis.port=6379
# Redisæå¡å¨è¿æ¥å¯ç ï¼é»è®¤ä¸ºç©ºï¼
#sa-token.alone-redis.password=
# è¿æ¥è¶æ¶æ¶é´
sa-token.alone-redis.timeout=10s
#è¿æ¥æ± æå¤§è¿æ¥æ°ï¼è¥ä¸ºè´è´£åè¡¨ç¤ºæ²¡æä»»ä½éå¶
sa-token.alone-redis.lettuce.pool.max-active=8
#è¿æ¥æ± æå¤§é»å¡ç­å¾æ¶é´ï¼è¥ä¸ºè´è´£åè¡¨ç¤ºæ²¡æä»»ä½éå¶
sa-token.alone-redis.lettuce.pool.max-wait=5000
#è¿æ¥æ± ä¸­çæå¤§ç©ºé²è¿æ¥
sa-token.alone-redis.lettuce.pool.max-idle=8
#è¿æ¥æ± ä¸­çæå°ç©ºé²è¿æ¥
sa-token.alone-redis.lettuce.pool.min-idle=0

md5.salt=das123456
aes.passkey=das123456

# æ¬ççèº«ä»½è¯å¼å¤´éç½®
system.location.adCode=220