# mysql config
spring.datasource.druid.name=province_digital_museum
spring.datasource.druid.url=**********************************************************************************************************************************************************************************************************************************************************************************************************
spring.datasource.druid.username=root
spring.datasource.druid.password=Das2023root.com
spring.datasource.druid.initial-size=5
spring.datasource.druid.maximum-pool-size=100
spring.datasource.druid.max-wait=60000
spring.datasource.druid.min-idle=5
spring.datasource.druid.max-active=20
spring.datasource.druid.validation-query=SELECT 1
spring.datasource.druid.test-on-borrow=false
spring.datasource.druid.test-while-idle=true
spring.datasource.druid.test-while-return=true
spring.datasource.druid.time-between-eviction-runs-millis=60000
spring.datasource.druid.min-evictableIdle-time-millis=300000
spring.datasource.druid.connection-init-sqls=set names utf8mb4;
spring.datasource.druid.filters=stat,slf4j
# reids config
spring.redis.database=2
spring.redis.host=localhost
spring.redis.port=6379
spring.redis.password=Das2023redis.com
spring.redis.timeout=1000
spring.redis.lettuce.pool.max-active=8
spring.redis.lettuce.pool.max-wait=5000
spring.redis.lettuce.pool.max-idle=8
spring.redis.lettuce.pool.min-idle=0
logging.file.path=/opt/saas/logs/${spring.application.name}
logging.level.root=info
logging.level.sql=debug

# multipart
spring.servlet.multipart.max-file-size=-1
spring.servlet.multipart.max-request-size=-1

# éç½® Sa-Token åç¬ä½¿ç¨ç Redis è¿æ¥
sa-token.alone-redis.database=2
sa-token.alone-redis.host=localhost
sa-token.alone-redis.port=6379
sa-token.alone-redis.password=Das2023redis.com
sa-token.alone-redis.timeout=10s
sa-token.alone-redis.lettuce.pool.max-active=8
sa-token.alone-redis.lettuce.pool.max-wait=5000
sa-token.alone-redis.lettuce.pool.max-idle=8
sa-token.alone-redis.lettuce.pool.min-idle=0

md5.salt=das123456
aes.passkey=das123456

# æ¬ççèº«ä»½è¯å¼å¤´éç½®
system.location.adCode=220
